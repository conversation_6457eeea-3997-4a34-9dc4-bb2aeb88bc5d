﻿!_Schema
types: !_Map_Type
  !_Identifier OutpFieldEnumValue: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        field: field
        label: true
        object: OutpFieldEnumValue
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    fields:
      - !_OutputEnum
        field: field
        label: true
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    name: OutpFieldEnumValue
    typeKind: !_TypeKind Output