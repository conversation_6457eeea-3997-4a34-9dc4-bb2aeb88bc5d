﻿!_Schema
types: !_Map_Type
  !_Identifier DmnEnumPrntDescr: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: PrntDmnEnumPrntDescr
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumPrntDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrntDescr
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumPrntDescr
        exclude: false
        value: !_EnumValue
          label: enum_dmnEnumPrntDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrntDescr
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: enum_dmnEnumPrntDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrntDescr
    name: DmnEnumPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnEnumPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier EnumDmnEnumPrntDescr: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumPrntDescr
        name: enum_dmnEnumPrntDescr
      - !_EnumLabel
        enum: EnumDmnEnumPrntDescr
        name: prnt_dmnEnumPrntDescr
    items:
      - !_Aliased
        name: enum_dmnEnumPrntDescr
      - !_Aliased
        name: prnt_dmnEnumPrntDescr
    name: EnumDmnEnumPrntDescr
    typeKind: !_TypeKind Enum
  !_Identifier PrntDmnEnumPrntDescr: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: PrntDmnEnumPrntDescr
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumPrntDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrntDescr
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumPrntDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrntDescr
    name: PrntDmnEnumPrntDescr
    typeKind: !_TypeKind Domain