﻿!_Schema
types: !_Map_Type
  !_Identifier RefGnrcAltModParamDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        object: RefGnrcAltModParamDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        type: !_DualBase
          typeParam: ref
    name: RefGnrcAltModParamDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: mod