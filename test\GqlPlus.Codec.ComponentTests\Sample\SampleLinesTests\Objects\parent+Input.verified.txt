﻿!_Schema
types: !_Map_Type
  !_Identifier PrntInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefPrntInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: parent
        object: RefPrntInp
        type: !_InputBase
          input: Number
    name: PrntInp
    parent: !_InputBase
      input: RefPrntInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefPrntInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: parent
        object: RefPrntInp
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: parent
        type: !_InputBase
          input: Number
    name: RefPrntInp
    typeKind: !_TypeKind Input