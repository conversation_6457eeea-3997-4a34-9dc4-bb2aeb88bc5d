﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstPrntObjPrntInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: PrntCnstPrntObjPrntInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltCnstPrntObjPrntInp
        type: !_InputBase
          input: Number
    fields:
      - !_InputField
        name: alt
        type: !_InputBase
          input: Number
    name: AltCnstPrntObjPrntInp
    parent: !_InputBase
      input: PrntCnstPrntObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier CnstPrntObjPrntInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: PrntCnstPrntObjPrntInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltCnstPrntObjPrntInp
        type: !_InputBase
          input: Number
    name: CnstPrntObjPrntInp
    parent: !_InputBase
      input: RefCnstPrntObjPrntInp
      typeArgs:
        - !_InputArg
          input: AltCnstPrntObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstPrntObjPrntInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: PrntCnstPrntObjPrntInp
        type: !_InputBase
          input: String
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    name: PrntCnstPrntObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier RefCnstPrntObjPrntInp: !_TypeInput
    name: RefCnstPrntObjPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Input
          typeName: PrntCnstPrntObjPrntInp
        name: ref