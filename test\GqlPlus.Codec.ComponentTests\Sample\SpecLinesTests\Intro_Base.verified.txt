﻿!_Schema
errors:
  - !_Error
    _kind: !_TokenKind End
    _message: In _TypeObject can't get model for type '_ChildType'
  - !_Error
    _kind: !_TokenKind End
    _message: In _ObjTypeParam can't get model for type '_Named'
  - !_Error
    _kind: !_TokenKind End
    _message: In _ObjConstraint can't get model for type '_TypeRef'
  - !_Error
    _kind: !_TokenKind End
    _message: In _ObjBase can't get model for type '_Described'
  - !_Error
    _kind: !_TokenKind End
    _message: In _ObjType<PERSON><PERSON> can't get model for type '_TypeRef'
  - !_Error
    _kind: !_TokenKind End
    _message: In _TypeParam can't get model for type '_Described'
  - !_Error
    _kind: !_TokenKind End
    _message: In _Field can't get model for type '_Aliased'
types: !_Map_Type
  !_Identifier _Alternate: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: type
        object: _Alternate
        type: !_OutputBase
          typeParam: base
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: collections
        object: _Alternate
        type: !_OutputBase
          output: _Collections
    fields:
      - !_OutputField
        name: type
        type: !_OutputBase
          typeParam: base
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: collections
        type: !_OutputBase
          output: _Collections
    name: _Alternate
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _ObjBase
        name: base
  !_Identifier _Field: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: type
        object: _Field
        type: !_OutputBase
          typeParam: base
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: modifiers
        object: _Field
        type: !_OutputBase
          output: _Modifiers
    fields:
      - !_OutputField
        name: type
        type: !_OutputBase
          typeParam: base
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: modifiers
        type: !_OutputBase
          output: _Modifiers
    name: _Field
    parent: !_OutputBase
      output: _Aliased
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _ObjBase
        name: base
  !_Identifier _ForParam: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _ForParam
        type: !_OutputBase
          output: _Alternate
          typeArgs:
            - !_OutputArg
              typeParam: base
      - !_ObjectFor(_OutputAlternate)
        object: _ForParam
        type: !_OutputBase
          output: _Field
          typeArgs:
            - !_OutputArg
              typeParam: base
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _Alternate
          typeArgs:
            - !_OutputArg
              typeParam: base
      - !_OutputAlternate
        type: !_OutputBase
          output: _Field
          typeArgs:
            - !_OutputArg
              typeParam: base
    name: _ForParam
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _ObjBase
        name: base
  !_Identifier _ObjBase: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _ObjBase
        type: !_OutputBase
          output: _TypeParam
    allFields:
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: typeArgs
        object: _ObjBase
        type: !_OutputBase
          typeParam: arg
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _TypeParam
    fields:
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: typeArgs
        type: !_OutputBase
          typeParam: arg
    name: _ObjBase
    parent: !_DualBase
      dual: _Described
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _ObjTypeArg
        name: arg
  !_Identifier _ObjConstraint: !_TypeOutput
    name: _ObjConstraint
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
        - !_OutputArg
          typeParam: kind
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Domain
          typeName: _ObjectKind
        name: kind
  !_Identifier _ObjTypeArg: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _ObjTypeArg
        type: !_OutputBase
          output: _TypeParam
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _TypeParam
    name: _ObjTypeArg
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
        - !_OutputArg
          output: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _ObjTypeParam: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: constraint
        object: _ObjTypeParam
        type: !_OutputBase
          output: _ObjConstraint
          typeArgs:
            - !_OutputArg
              typeParam: kind
    fields:
      - !_OutputField
        name: constraint
        type: !_OutputBase
          output: _ObjConstraint
          typeArgs:
            - !_OutputArg
              typeParam: kind
    name: _ObjTypeParam
    parent: !_OutputBase
      output: _Named
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Domain
          typeName: _ObjectKind
        name: kind
  !_Identifier _ObjectFor: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: object
        object: _ObjectFor
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: object
        type: !_OutputBase
          output: _Identifier
    name: _ObjectFor
    parent: !_OutputBase
      typeParam: for
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _ForParam
        name: for
  !_Identifier _ObjectKind: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: _ObjectKind
        exclude: false
        value: !_EnumValue
          label: Dual
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
      - !_DomainItem(_DomainLabel)
        domain: _ObjectKind
        exclude: false
        value: !_EnumValue
          label: Input
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
      - !_DomainItem(_DomainLabel)
        domain: _ObjectKind
        exclude: false
        value: !_EnumValue
          label: Output
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: Dual
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: Input
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: Output
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    name: _ObjectKind
    typeKind: !_TypeKind Domain
  !_Identifier _TypeObject: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: typeParams
        object: _TypeObject
        type: !_OutputBase
          typeParam: typeParam
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: fields
        object: _TypeObject
        type: !_OutputBase
          typeParam: field
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: alternates
        object: _TypeObject
        type: !_OutputBase
          typeParam: alternate
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: allFields
        object: _TypeObject
        type: !_OutputBase
          output: _ObjectFor
          typeArgs:
            - !_OutputArg
              typeParam: field
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: allAlternates
        object: _TypeObject
        type: !_OutputBase
          output: _ObjectFor
          typeArgs:
            - !_OutputArg
              typeParam: alternate
    fields:
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: typeParams
        type: !_OutputBase
          typeParam: typeParam
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: fields
        type: !_OutputBase
          typeParam: field
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: alternates
        type: !_OutputBase
          typeParam: alternate
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: allFields
        type: !_OutputBase
          output: _ObjectFor
          typeArgs:
            - !_OutputArg
              typeParam: field
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: allAlternates
        type: !_OutputBase
          output: _ObjectFor
          typeArgs:
            - !_OutputArg
              typeParam: alternate
    name: _TypeObject
    parent: !_OutputBase
      output: _ChildType
      typeArgs:
        - !_OutputArg
          typeParam: kind
        - !_OutputArg
          typeParam: parent
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Domain
          typeName: _ObjectKind
        name: kind
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _ObjBase
        name: parent
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _ObjTypeParam
        name: typeParam
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _Field
        name: field
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _Alternate
        name: alternate
  !_Identifier _TypeParam: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: typeParam
        object: _TypeParam
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: typeParam
        type: !_OutputBase
          output: _Identifier
    name: _TypeParam
    parent: !_DualBase
      dual: _Described
    typeKind: !_TypeKind Output