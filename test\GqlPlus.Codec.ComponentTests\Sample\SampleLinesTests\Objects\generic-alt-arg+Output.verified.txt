﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcAltArgOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: GnrcAltArgOutp
        type: !_OutputBase
          output: RefGnrcAltArgOutp
          typeArgs:
            - !_OutputArg
              typeParam: type
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: RefGnrcAltArgOutp
          typeArgs:
            - !_OutputArg
              typeParam: type
    name: GnrcAltArgOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type
  !_Identifier RefGnrcAltArgOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcAltArgOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcAltArgOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref