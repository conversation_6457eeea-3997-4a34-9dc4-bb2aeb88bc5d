﻿{
  "$tag": "_Schema",
  "types": {
    "$tag": "_Map_Type",
    "_Aliased": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        }
      ],
      "name": "_Aliased",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Named"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "_Alternate": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Alternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "base"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "collections",
          "object": "_Alternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Collections"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "type",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "base"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "collections",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Collections"
          }
        }
      ],
      "name": "_Alternate",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_ObjBase"
          },
          "name": "base"
        }
      ]
    },
    "_BaseDomain": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_BaseType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "_ChildType",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Named"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "items",
          "object": "_ParentType",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "item"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allItems",
          "object": "_ParentType",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "allItem"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "domainKind",
          "object": "_BaseDomain",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "domain"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "domainKind",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "domain"
          }
        }
      ],
      "name": "_BaseDomain",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ParentType",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Domain",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          },
          {
            "$tag": "_OutputArg",
            "typeParam": "item"
          },
          {
            "$tag": "_OutputArg",
            "typeParam": "domainItem"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "_DomainKind"
          },
          "name": "domain"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "_BaseDomainItem"
          },
          "name": "item"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_DomainItem"
          },
          "name": "domainItem"
        }
      ]
    },
    "_BaseDomainItem": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "exclude",
          "object": "_BaseDomainItem",
          "type": {
            "$tag": "_DualBase",
            "dual": "Boolean"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "exclude",
          "type": {
            "$tag": "_DualBase",
            "dual": "Boolean"
          }
        }
      ],
      "name": "_BaseDomainItem",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Described"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "_BaseType": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_BaseType",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "typeKind",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        }
      ],
      "name": "_BaseType",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Aliased"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          },
          "name": "kind"
        }
      ]
    },
    "_BasicValue": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_BasicValue",
          "type": {
            "$tag": "_OutputBase",
            "output": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_BasicValue",
          "type": {
            "$tag": "_OutputBase",
            "output": "_EnumValue"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_BasicValue",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_BasicValue",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "Boolean"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_EnumValue"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "name": "_BasicValue",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Categories": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Categories",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Category"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Categories",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "category",
          "object": "_Categories",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Category"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Categories",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Category"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "category",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Category"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "name": "_Categories",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Category": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "resolution",
          "object": "_Category",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Resolution"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "output",
          "object": "_Category",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Output",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "object": "_Category",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "resolution",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Resolution"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "output",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Output",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        }
      ],
      "name": "_Category",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Aliased"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_CategoryFilter": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "collections": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "names",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": true,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "matchAliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnByAlias",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnReferencedTypes",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "resolutions",
          "object": "_CategoryFilter",
          "type": {
            "$tag": "_InputBase",
            "input": "_Resolution"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "resolutions",
          "type": {
            "$tag": "_InputBase",
            "input": "_Resolution"
          }
        }
      ],
      "name": "_CategoryFilter",
      "parent": {
        "$tag": "_InputBase",
        "input": "_Filter"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "_ChildType": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_BaseType",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "_ChildType",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "parent"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "parent",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "parent"
          }
        }
      ],
      "name": "_ChildType",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_BaseType",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "typeParam": "kind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          },
          "name": "kind"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "_Described"
          },
          "name": "parent"
        }
      ]
    },
    "_Collections": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Collections",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifier",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "List",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_ModifierKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Collections",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ModifierKeyed",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Dictionary",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_ModifierKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Collections",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ModifierKeyed",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "TypeParam",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_ModifierKind"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifier",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "List",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_ModifierKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ModifierKeyed",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Dictionary",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_ModifierKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ModifierKeyed",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "TypeParam",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_ModifierKind"
              }
            ]
          }
        }
      ],
      "name": "_Collections",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Constant": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Constant",
          "type": {
            "$tag": "_OutputBase",
            "output": "_SimpleValue"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Constant",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ConstantList"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Constant",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ConstantMap"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_SimpleValue"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ConstantList"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ConstantMap"
          }
        }
      ],
      "name": "_Constant",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_ConstantList": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "collections": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "object": "_ConstantList",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "collections": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "name": "_ConstantList",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_ConstantMap": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "Simple",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Internal"
              }
            }
          ],
          "object": "_ConstantMap",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "Simple",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Internal"
              }
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "name": "_ConstantMap",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Described": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "_Described",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "_Directive": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "parameters",
          "object": "_Directive",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputParam"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "repeatable",
          "object": "_Directive",
          "type": {
            "$tag": "_OutputBase",
            "output": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Location",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Enum"
              }
            }
          ],
          "name": "locations",
          "object": "_Directive",
          "type": {
            "$tag": "_OutputBase",
            "output": "_"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "parameters",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputParam"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "repeatable",
          "type": {
            "$tag": "_OutputBase",
            "output": "Boolean"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Location",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Enum"
              }
            }
          ],
          "name": "locations",
          "type": {
            "$tag": "_OutputBase",
            "output": "_"
          }
        }
      ],
      "name": "_Directive",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Aliased"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Directives": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Directives",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directive"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Directives",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "directive",
          "object": "_Directives",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directive"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Directives",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directive"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "directive",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directive"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "name": "_Directives",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_DomainItem": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "domain",
          "object": "_DomainItem",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "domain",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_DomainItem",
      "parent": {
        "$tag": "_OutputBase",
        "typeParam": "item"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "_BaseDomainItem"
          },
          "name": "item"
        }
      ]
    },
    "_DomainItemLabel": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "exclude",
          "object": "_BaseDomainItem",
          "type": {
            "$tag": "_DualBase",
            "dual": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "label",
          "object": "_DomainLabel",
          "type": {
            "$tag": "_OutputBase",
            "output": "_EnumValue"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "domain",
          "object": "_DomainItem",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_DomainItemLabel",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_DomainItem",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_DomainLabel"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_DomainItemRange": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "domain",
          "object": "_DomainItem",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_DomainItemRange",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_DomainItem",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "_DomainRange"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_DomainItemRegex": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "domain",
          "object": "_DomainItem",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_DomainItemRegex",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_DomainItem",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "_DomainRegex"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_DomainItemTrueFalse": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "domain",
          "object": "_DomainItem",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_DomainItemTrueFalse",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_DomainItem",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "_DomainTrueFalse"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_DomainKind": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "_DomainKind",
          "name": "Boolean"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_DomainKind",
          "name": "Enum"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_DomainKind",
          "name": "Number"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_DomainKind",
          "name": "String"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Boolean"
        },
        {
          "$tag": "_Aliased",
          "name": "Enum"
        },
        {
          "$tag": "_Aliased",
          "name": "Number"
        },
        {
          "$tag": "_Aliased",
          "name": "String"
        }
      ],
      "name": "_DomainKind",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "_DomainLabel": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "exclude",
          "object": "_BaseDomainItem",
          "type": {
            "$tag": "_DualBase",
            "dual": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "label",
          "object": "_DomainLabel",
          "type": {
            "$tag": "_OutputBase",
            "output": "_EnumValue"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "label",
          "type": {
            "$tag": "_OutputBase",
            "output": "_EnumValue"
          }
        }
      ],
      "name": "_DomainLabel",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_BaseDomainItem"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_DomainRange": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "exclude",
          "object": "_BaseDomainItem",
          "type": {
            "$tag": "_DualBase",
            "dual": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "lower",
          "object": "_DomainRange",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "upper",
          "object": "_DomainRange",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "lower",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        },
        {
          "$tag": "_DualField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "upper",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "_DomainRange",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_BaseDomainItem"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "_DomainRef": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "domainKind",
          "object": "_DomainRef",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "domainKind",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        }
      ],
      "name": "_DomainRef",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_TypeRef",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Domain",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "_DomainKind"
          },
          "name": "kind"
        }
      ]
    },
    "_DomainRegex": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "exclude",
          "object": "_BaseDomainItem",
          "type": {
            "$tag": "_DualBase",
            "dual": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "pattern",
          "object": "_DomainRegex",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "pattern",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "_DomainRegex",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_BaseDomainItem"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "_DomainTrueFalse": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "exclude",
          "object": "_BaseDomainItem",
          "type": {
            "$tag": "_DualBase",
            "dual": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "value",
          "object": "_DomainTrueFalse",
          "type": {
            "$tag": "_DualBase",
            "dual": "Boolean"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "value",
          "type": {
            "$tag": "_DualBase",
            "dual": "Boolean"
          }
        }
      ],
      "name": "_DomainTrueFalse",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_BaseDomainItem"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "_DomainValue": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "domainKind",
          "object": "_DomainRef",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "value",
          "object": "_DomainValue",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "value"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "value",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "value"
          }
        }
      ],
      "name": "_DomainValue",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_DomainRef",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "typeParam": "kind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "_DomainKind"
          },
          "name": "kind"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_BasicValue"
          },
          "name": "value"
        }
      ]
    },
    "_DualAlternate": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Alternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualBase"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "collections",
          "object": "_Alternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Collections"
          }
        }
      ],
      "name": "_DualAlternate",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Alternate",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_DualBase"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_DualBase": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ObjBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeArgs",
          "object": "_ObjBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualTypeArg"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "dual",
          "object": "_DualBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "dual",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_DualBase",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ObjBase",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_DualTypeArg"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_DualField": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Field",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualBase"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "object": "_Field",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        }
      ],
      "name": "_DualField",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Field",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_DualBase"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_DualTypeArg": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ObjTypeArg",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "dual",
          "object": "_DualTypeArg",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "dual",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_DualTypeArg",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ObjTypeArg"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_DualTypeParam": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "constraint",
          "object": "_ObjTypeParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjConstraint",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "kind"
              }
            ]
          }
        }
      ],
      "name": "_DualTypeParam",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ObjTypeParam",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Dual",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_EnumLabel": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "enum",
          "object": "_EnumLabel",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "enum",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        }
      ],
      "name": "_EnumLabel",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Aliased"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "_EnumValue": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "label",
          "object": "_EnumValue",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "label",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_EnumValue",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_TypeRef",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Enum",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Field": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "base"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "object": "_Field",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "type",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "base"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        }
      ],
      "name": "_Field",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Aliased"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_ObjBase"
          },
          "name": "base"
        }
      ]
    },
    "_Filter": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "collections": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "names",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": true,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "matchAliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnByAlias",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnReferencedTypes",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "collections": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "names",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_InputField",
          "default": true,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "matchAliases",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_InputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_InputField",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnByAlias",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_InputField",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnReferencedTypes",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        }
      ],
      "name": "_Filter",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "_ForParam": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ForParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Alternate",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "base"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ForParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Field",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "base"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Alternate",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "base"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Field",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "base"
              }
            ]
          }
        }
      ],
      "name": "_ForParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_ObjBase"
          },
          "name": "base"
        }
      ]
    },
    "_Identifier": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "_Identifier",
          "exclude": false,
          "pattern": "[A-Za-z_]\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "[A-Za-z_]\u002B"
        }
      ],
      "name": "_Identifier",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "_InputAlternate": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Alternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputBase"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "collections",
          "object": "_Alternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Collections"
          }
        }
      ],
      "name": "_InputAlternate",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Alternate",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_InputBase"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_InputBase": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ObjBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_InputBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualBase"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeArgs",
          "object": "_ObjBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputTypeArg"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "input",
          "object": "_InputBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualBase"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "input",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_InputBase",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ObjBase",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_InputTypeArg"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_InputField": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Field",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputBase"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "object": "_Field",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "default",
          "object": "_InputField",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "default",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "name": "_InputField",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Field",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_InputBase"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_InputParam": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ObjBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_InputBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualBase"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeArgs",
          "object": "_ObjBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputTypeArg"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "input",
          "object": "_InputBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "object": "_InputParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "default",
          "object": "_InputParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "default",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "name": "_InputParam",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_InputBase"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_InputTypeArg": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ObjTypeArg",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "input",
          "object": "_InputTypeArg",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "input",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_InputTypeArg",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ObjTypeArg"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_InputTypeParam": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_InputTypeParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Dual",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "constraint",
          "object": "_ObjTypeParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjConstraint",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "kind"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Dual",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        }
      ],
      "name": "_InputTypeParam",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ObjTypeParam",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Input",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Internal": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Internal",
          "type": {
            "$tag": "_OutputBase",
            "output": "Null"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Internal",
          "type": {
            "$tag": "_OutputBase",
            "output": "Object"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Internal",
          "type": {
            "$tag": "_OutputBase",
            "output": "Void"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "Null"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "Object"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "Void"
          }
        }
      ],
      "name": "_Internal",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Location": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Operation"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Variable"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Field"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Inline"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Spread"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Fragment"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Operation"
        },
        {
          "$tag": "_Aliased",
          "name": "Variable"
        },
        {
          "$tag": "_Aliased",
          "name": "Field"
        },
        {
          "$tag": "_Aliased",
          "name": "Inline"
        },
        {
          "$tag": "_Aliased",
          "name": "Spread"
        },
        {
          "$tag": "_Aliased",
          "name": "Fragment"
        }
      ],
      "name": "_Location",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "_Modifier": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "modifierKind",
          "object": "_Modifier",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "modifierKind",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        }
      ],
      "name": "_Modifier",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "_ModifierKind"
          },
          "name": "kind"
        }
      ]
    },
    "_ModifierKeyed": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "modifierKind",
          "object": "_Modifier",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "by",
          "object": "_ModifierKeyed",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeSimple"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "optional",
          "object": "_ModifierKeyed",
          "type": {
            "$tag": "_OutputBase",
            "output": "Boolean"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "by",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeSimple"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "optional",
          "type": {
            "$tag": "_OutputBase",
            "output": "Boolean"
          }
        }
      ],
      "name": "_ModifierKeyed",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Modifier",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "typeParam": "kind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "_ModifierKind"
          },
          "name": "kind"
        }
      ]
    },
    "_ModifierKind": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "aliases": ["Optional"],
          "enum": "_ModifierKind",
          "name": "Opt"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_ModifierKind",
          "name": "List"
        },
        {
          "$tag": "_EnumLabel",
          "aliases": ["Dictionary"],
          "enum": "_ModifierKind",
          "name": "Dict"
        },
        {
          "$tag": "_EnumLabel",
          "aliases": ["TypeParam"],
          "enum": "_ModifierKind",
          "name": "Param"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "aliases": ["Optional"],
          "name": "Opt"
        },
        {
          "$tag": "_Aliased",
          "name": "List"
        },
        {
          "$tag": "_Aliased",
          "aliases": ["Dictionary"],
          "name": "Dict"
        },
        {
          "$tag": "_Aliased",
          "aliases": ["TypeParam"],
          "name": "Param"
        }
      ],
      "name": "_ModifierKind",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "_Modifiers": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Modifiers",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifier",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Optional",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_ModifierKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Modifiers",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Collections"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifier",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Optional",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_ModifierKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Collections"
          }
        }
      ],
      "name": "_Modifiers",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Named": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "name",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        }
      ],
      "name": "_Named",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Described"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "_NameFilter": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "_NameFilter",
          "exclude": false,
          "pattern": "[A-Za-z_.*]\u002B"
        }
      ],
      "description": "_NameFilter is a simple match expression against _Identifier where \u0027.\u0027 matches any single character and \u0027*\u0027 matches zero or more of any character.",
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "[A-Za-z_.*]\u002B"
        }
      ],
      "name": "_NameFilter",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "_ObjBase": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ObjBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeArgs",
          "object": "_ObjBase",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "arg"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeArgs",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "arg"
          }
        }
      ],
      "name": "_ObjBase",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Described"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_ObjTypeArg"
          },
          "name": "arg"
        }
      ]
    },
    "_ObjConstraint": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_ObjConstraint",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_TypeRef",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "typeParam": "kind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Domain"
            },
            "typeName": "_ObjectKind"
          },
          "name": "kind"
        }
      ]
    },
    "_ObjectFor": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "object",
          "object": "_ObjectFor",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "object",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_ObjectFor",
      "parent": {
        "$tag": "_OutputBase",
        "typeParam": "for"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_ForParam"
          },
          "name": "for"
        }
      ]
    },
    "_ObjectKind": {
      "$tag": "_DomainEnum",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainLabel)",
          "domain": "_ObjectKind",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "Dual",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        },
        {
          "$tag": "_DomainItem(_DomainLabel)",
          "domain": "_ObjectKind",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "Input",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        },
        {
          "$tag": "_DomainItem(_DomainLabel)",
          "domain": "_ObjectKind",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "Output",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Enum"
      },
      "items": [
        {
          "$tag": "_DomainLabel",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "Dual",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        },
        {
          "$tag": "_DomainLabel",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "Input",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        },
        {
          "$tag": "_DomainLabel",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "Output",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        }
      ],
      "name": "_ObjectKind",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "_ObjTypeArg": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ObjTypeArg",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        }
      ],
      "name": "_ObjTypeArg",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_TypeRef",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_TypeKind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_ObjTypeParam": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "constraint",
          "object": "_ObjTypeParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjConstraint",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "kind"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "constraint",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjConstraint",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "kind"
              }
            ]
          }
        }
      ],
      "name": "_ObjTypeParam",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Named"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Domain"
            },
            "typeName": "_ObjectKind"
          },
          "name": "kind"
        }
      ]
    },
    "_OutputAlternate": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Alternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_OutputBase"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "collections",
          "object": "_Alternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Collections"
          }
        }
      ],
      "name": "_OutputAlternate",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Alternate",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_OutputBase"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_OutputBase": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ObjBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_OutputBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualBase"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeArgs",
          "object": "_ObjBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_OutputTypeArg"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "output",
          "object": "_OutputBase",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualBase"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "output",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_OutputBase",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ObjBase",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_OutputTypeArg"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_OutputEnum": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "_OutputEnum",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "label",
          "object": "_OutputEnum",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "label",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_OutputEnum",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_TypeRef",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Enum",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_OutputField": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_OutputField",
          "type": {
            "$tag": "_OutputBase",
            "output": "_OutputEnum"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Field",
          "type": {
            "$tag": "_OutputBase",
            "output": "_OutputBase"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "object": "_Field",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "parameters",
          "object": "_OutputField",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputParam"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_OutputEnum"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "parameters",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputParam"
          }
        }
      ],
      "name": "_OutputField",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Field",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_OutputBase"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_OutputTypeArg": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_ObjTypeArg",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeParam"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "output",
          "object": "_OutputTypeArg",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "label",
          "object": "_OutputTypeArg",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "output",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "label",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_OutputTypeArg",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ObjTypeArg"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_OutputTypeParam": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_OutputTypeParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Dual",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_OutputTypeParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Enum",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "constraint",
          "object": "_ObjTypeParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjConstraint",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "kind"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Dual",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Enum",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        }
      ],
      "name": "_OutputTypeParam",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ObjTypeParam",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Output",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_ParentType": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_BaseType",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "_ChildType",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Named"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "items",
          "object": "_ParentType",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "item"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allItems",
          "object": "_ParentType",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "allItem"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "items",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "item"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allItems",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "allItem"
          }
        }
      ],
      "name": "_ParentType",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ChildType",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "typeParam": "kind"
          },
          {
            "$tag": "_DualArg",
            "dual": "_Named"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          },
          "name": "kind"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "_Described"
          },
          "name": "item"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "_Described"
          },
          "name": "allItem"
        }
      ]
    },
    "_Resolution": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "_Resolution",
          "name": "Parallel"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Resolution",
          "name": "Sequential"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Resolution",
          "name": "Single"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Parallel"
        },
        {
          "$tag": "_Aliased",
          "name": "Sequential"
        },
        {
          "$tag": "_Aliased",
          "name": "Single"
        }
      ],
      "name": "_Resolution",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "_Schema": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "categories",
          "object": "_Schema",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_CategoryFilter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Categories"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "directives",
          "object": "_Schema",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_Filter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directives"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "types",
          "object": "_Schema",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_TypeFilter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "settings",
          "object": "_Schema",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_Filter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Setting"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "categories",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_CategoryFilter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Categories"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "directives",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_Filter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directives"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "types",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_TypeFilter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "settings",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_Filter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Setting"
          }
        }
      ],
      "name": "_Schema",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Named"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Setting": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "value",
          "object": "_Setting",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "value",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "name": "_Setting",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Named"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_SimpleKind": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "_SimpleKind",
          "name": "Basic"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_SimpleKind",
          "name": "Enum"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_SimpleKind",
          "name": "Internal"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_SimpleKind",
          "name": "Domain"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_SimpleKind",
          "name": "Union"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Basic"
        },
        {
          "$tag": "_Aliased",
          "name": "Enum"
        },
        {
          "$tag": "_Aliased",
          "name": "Internal"
        },
        {
          "$tag": "_Aliased",
          "name": "Domain"
        },
        {
          "$tag": "_Aliased",
          "name": "Union"
        }
      ],
      "name": "_SimpleKind",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "_SimpleValue": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_SimpleValue",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DomainValue",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Boolean",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_OutputArg",
                "output": "Boolean"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_SimpleValue",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DomainValue",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Enum",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_OutputArg",
                "output": "_EnumValue"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_SimpleValue",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DomainValue",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Number",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_OutputArg",
                "output": "Number"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_SimpleValue",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DomainValue",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "String",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_OutputArg",
                "output": "String"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DomainValue",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Boolean",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_OutputArg",
                "output": "Boolean"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DomainValue",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Enum",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_OutputArg",
                "output": "_EnumValue"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DomainValue",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Number",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_OutputArg",
                "output": "Number"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DomainValue",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "String",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_OutputArg",
                "output": "String"
              }
            ]
          }
        }
      ],
      "name": "_SimpleValue",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Type": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseType",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Basic",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseType",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Internal",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeDual"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeEnum"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeInput"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeOutput"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeDomain"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeUnion"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseType",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Basic",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseType",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Internal",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeDual"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeEnum"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeInput"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeOutput"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeDomain"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeUnion"
          }
        }
      ],
      "name": "_Type",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_TypeDomain": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_TypeDomain",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseDomain",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Boolean",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_DualArg",
                "dual": "_DomainTrueFalse"
              },
              {
                "$tag": "_OutputArg",
                "output": "_DomainItemTrueFalse"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_TypeDomain",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseDomain",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Enum",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_OutputArg",
                "output": "_DomainLabel"
              },
              {
                "$tag": "_OutputArg",
                "output": "_DomainItemLabel"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_TypeDomain",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseDomain",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Number",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_DualArg",
                "dual": "_DomainRange"
              },
              {
                "$tag": "_OutputArg",
                "output": "_DomainItemRange"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_TypeDomain",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseDomain",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "String",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_DualArg",
                "dual": "_DomainRegex"
              },
              {
                "$tag": "_OutputArg",
                "output": "_DomainItemRegex"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseDomain",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Boolean",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_DualArg",
                "dual": "_DomainTrueFalse"
              },
              {
                "$tag": "_OutputArg",
                "output": "_DomainItemTrueFalse"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseDomain",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Enum",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_OutputArg",
                "output": "_DomainLabel"
              },
              {
                "$tag": "_OutputArg",
                "output": "_DomainItemLabel"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseDomain",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Number",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_DualArg",
                "dual": "_DomainRange"
              },
              {
                "$tag": "_OutputArg",
                "output": "_DomainItemRange"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_BaseDomain",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "String",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_DomainKind"
              },
              {
                "$tag": "_DualArg",
                "dual": "_DomainRegex"
              },
              {
                "$tag": "_OutputArg",
                "output": "_DomainItemRegex"
              }
            ]
          }
        }
      ],
      "name": "_TypeDomain",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_TypeDual": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_BaseType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "_ChildType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualBase"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeParams",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualTypeParam"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "fields",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualField"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "alternates",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_DualAlternate"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allFields",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjectFor",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "field"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allAlternates",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjectFor",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "alternate"
              }
            ]
          }
        }
      ],
      "name": "_TypeDual",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_TypeObject",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Dual",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          },
          {
            "$tag": "_OutputArg",
            "output": "_DualBase"
          },
          {
            "$tag": "_OutputArg",
            "output": "_DualTypeParam"
          },
          {
            "$tag": "_OutputArg",
            "output": "_DualField"
          },
          {
            "$tag": "_OutputArg",
            "output": "_DualAlternate"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_TypeEnum": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_BaseType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "_ChildType",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Named"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "items",
          "object": "_ParentType",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Aliased"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allItems",
          "object": "_ParentType",
          "type": {
            "$tag": "_DualBase",
            "dual": "_EnumLabel"
          }
        }
      ],
      "name": "_TypeEnum",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ParentType",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Enum",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          },
          {
            "$tag": "_DualArg",
            "dual": "_Aliased"
          },
          {
            "$tag": "_DualArg",
            "dual": "_EnumLabel"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_TypeFilter": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "collections": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "names",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": true,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "matchAliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnByAlias",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnReferencedTypes",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "kinds",
          "object": "_TypeFilter",
          "type": {
            "$tag": "_InputBase",
            "input": "_TypeKind"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "kinds",
          "type": {
            "$tag": "_InputBase",
            "input": "_TypeKind"
          }
        }
      ],
      "name": "_TypeFilter",
      "parent": {
        "$tag": "_InputBase",
        "input": "_Filter"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "_TypeInput": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_BaseType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "_ChildType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputBase"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeParams",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputTypeParam"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "fields",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputField"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "alternates",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputAlternate"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allFields",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjectFor",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "field"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allAlternates",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjectFor",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "alternate"
              }
            ]
          }
        }
      ],
      "name": "_TypeInput",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_TypeObject",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Input",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          },
          {
            "$tag": "_OutputArg",
            "output": "_InputBase"
          },
          {
            "$tag": "_OutputArg",
            "output": "_InputTypeParam"
          },
          {
            "$tag": "_OutputArg",
            "output": "_InputField"
          },
          {
            "$tag": "_OutputArg",
            "output": "_InputAlternate"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_TypeKind": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "_SimpleKind",
          "name": "Basic"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_SimpleKind",
          "name": "Enum"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_SimpleKind",
          "name": "Internal"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_SimpleKind",
          "name": "Domain"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_SimpleKind",
          "name": "Union"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_TypeKind",
          "name": "Dual"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_TypeKind",
          "name": "Input"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_TypeKind",
          "name": "Output"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Dual"
        },
        {
          "$tag": "_Aliased",
          "name": "Input"
        },
        {
          "$tag": "_Aliased",
          "name": "Output"
        }
      ],
      "name": "_TypeKind",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "_SimpleKind"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "_TypeObject": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_BaseType",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "_ChildType",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "parent"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeParams",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "typeParam"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "fields",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "field"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "alternates",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "alternate"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allFields",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjectFor",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "field"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allAlternates",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjectFor",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "alternate"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeParams",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "typeParam"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "fields",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "field"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "alternates",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "alternate"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allFields",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjectFor",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "field"
              }
            ]
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allAlternates",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjectFor",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "alternate"
              }
            ]
          }
        }
      ],
      "name": "_TypeObject",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ChildType",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "typeParam": "kind"
          },
          {
            "$tag": "_OutputArg",
            "typeParam": "parent"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Domain"
            },
            "typeName": "_ObjectKind"
          },
          "name": "kind"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_ObjBase"
          },
          "name": "parent"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_ObjTypeParam"
          },
          "name": "typeParam"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_Field"
          },
          "name": "field"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "_Alternate"
          },
          "name": "alternate"
        }
      ]
    },
    "_TypeOutput": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_BaseType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "_ChildType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_OutputBase"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "typeParams",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_OutputTypeParam"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "fields",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_OutputField"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "alternates",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_OutputAlternate"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allFields",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjectFor",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "field"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allAlternates",
          "object": "_TypeObject",
          "type": {
            "$tag": "_OutputBase",
            "output": "_ObjectFor",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "alternate"
              }
            ]
          }
        }
      ],
      "name": "_TypeOutput",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_TypeObject",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Output",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          },
          {
            "$tag": "_OutputArg",
            "output": "_OutputBase"
          },
          {
            "$tag": "_OutputArg",
            "output": "_OutputTypeParam"
          },
          {
            "$tag": "_OutputArg",
            "output": "_OutputField"
          },
          {
            "$tag": "_OutputArg",
            "output": "_OutputAlternate"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_TypeParam": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeParam",
          "object": "_TypeParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "typeParam",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_TypeParam",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Described"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_TypeRef": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "typeKind",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "kind"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "typeName",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_TypeRef",
      "parent": {
        "$tag": "_DualBase",
        "dual": "_Described"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          },
          "name": "kind"
        }
      ]
    },
    "_TypeSimple": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_TypeSimple",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Basic",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_TypeSimple",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Enum",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_TypeSimple",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Domain",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_TypeSimple",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Union",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Basic",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Enum",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Domain",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Union",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        }
      ],
      "name": "_TypeSimple",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_TypeUnion": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "name",
          "object": "_Named",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Aliased",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_BaseType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "_ChildType",
          "type": {
            "$tag": "_DualBase",
            "dual": "_Named"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "items",
          "object": "_ParentType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_UnionRef"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "allItems",
          "object": "_ParentType",
          "type": {
            "$tag": "_OutputBase",
            "output": "_UnionMember"
          }
        }
      ],
      "name": "_TypeUnion",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_ParentType",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "label": "Union",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "_TypeKind"
          },
          {
            "$tag": "_OutputArg",
            "output": "_UnionRef"
          },
          {
            "$tag": "_OutputArg",
            "output": "_UnionMember"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_UnionMember": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_SimpleKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "union",
          "object": "_UnionMember",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "union",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_UnionMember",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_UnionRef"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_UnionRef": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "description",
          "object": "_Described",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeKind",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_SimpleKind"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "typeName",
          "object": "_TypeRef",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Identifier"
          }
        }
      ],
      "name": "_UnionRef",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_TypeRef",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "_SimpleKind"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    }
  }
}