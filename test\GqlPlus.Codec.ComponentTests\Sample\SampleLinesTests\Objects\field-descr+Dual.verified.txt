﻿!_Schema
types: !_Map_Type
  !_Identifier FieldDescrDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        description: 'Test Descr'
        name: field
        object: FieldDescrDual
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        description: 'Test Descr'
        name: field
        type: !_DualBase
          dual: String
    name: FieldDescrDual
    typeKind: !_TypeKind Dual