﻿!_Schema
types: !_Map_Type
  !_Identifier CnstAltDmnInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: CnstAltDmnInp
        type: !_InputBase
          input: RefCnstAltDmnInp
          typeArgs:
            - !_InputArg
              input: DomCnstAltDmnInp
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: RefCnstAltDmnInp
          typeArgs:
            - !_InputArg
              input: DomCnstAltDmnInp
    name: CnstAltDmnInp
    typeKind: !_TypeKind Input
  !_Identifier DomCnstAltDmnInp: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: DomCnstAltDmnInp
        exclude: false
        pattern: \w+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: \w+
    name: DomCnstAltDmnInp
    typeKind: !_TypeKind Domain
  !_Identifier RefCnstAltDmnInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefCnstAltDmnInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefCnstAltDmnInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref