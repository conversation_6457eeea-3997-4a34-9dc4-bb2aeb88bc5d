﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcFieldArgDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: GnrcFieldArgDual
        type: !_DualBase
          dual: RefGnrcFieldArgDual
          typeArgs:
            - !_DualArg
              typeParam: type
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          dual: RefGnrcFieldArgDual
          typeArgs:
            - !_DualArg
              typeParam: type
    name: GnrcFieldArgDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type
  !_Identifier RefGnrcFieldArgDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefGnrcFieldArgDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefGnrcFieldArgDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref