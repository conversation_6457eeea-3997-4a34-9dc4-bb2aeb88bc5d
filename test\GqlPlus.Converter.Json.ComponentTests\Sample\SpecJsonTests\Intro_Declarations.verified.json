﻿{
  "$tag": "_Schema",
  "errors": [
    {
      "$tag": "_Error",
      "_kind": {
        "$tag": "_TokenKind",
        "value": "End"
      },
      "_message": "In _Schema can\u0027t get model for type \u0027_Named\u0027"
    }
  ],
  "types": {
    "$tag": "_Map_Type",
    "_CategoryFilter": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "collections": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "names",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": true,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "matchAliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnByAlias",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnReferencedTypes",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "resolutions",
          "object": "_CategoryFilter",
          "type": {
            "$tag": "_InputBase",
            "input": "_Resolution"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "resolutions",
          "type": {
            "$tag": "_InputBase",
            "input": "_Resolution"
          }
        }
      ],
      "name": "_CategoryFilter",
      "parent": {
        "$tag": "_InputBase",
        "input": "_Filter"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "_Filter": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "collections": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "names",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": true,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "matchAliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnByAlias",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnReferencedTypes",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "collections": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "names",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_InputField",
          "default": true,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "matchAliases",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_InputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_InputField",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnByAlias",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_InputField",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnReferencedTypes",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        }
      ],
      "name": "_Filter",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "_Identifier": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "_Identifier",
          "exclude": false,
          "pattern": "[A-Za-z_]\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "[A-Za-z_]\u002B"
        }
      ],
      "name": "_Identifier",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "_NameFilter": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "_NameFilter",
          "exclude": false,
          "pattern": "[A-Za-z_.*]\u002B"
        }
      ],
      "description": "_NameFilter is a simple match expression against _Identifier where \u0027.\u0027 matches any single character and \u0027*\u0027 matches zero or more of any character.",
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "[A-Za-z_.*]\u002B"
        }
      ],
      "name": "_NameFilter",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "_Schema": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "categories",
          "object": "_Schema",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_CategoryFilter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Categories"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "directives",
          "object": "_Schema",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_Filter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directives"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "types",
          "object": "_Schema",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_TypeFilter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "settings",
          "object": "_Schema",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_Filter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Setting"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "categories",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_CategoryFilter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Categories"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "directives",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_Filter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directives"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "types",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_TypeFilter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Identifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Domain"
              }
            }
          ],
          "name": "settings",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "_Filter",
              "modifiers": [
                {
                  "$tag": "_Modifier",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Opt"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "_Setting"
          }
        }
      ],
      "name": "_Schema",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Named"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_TypeFilter": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "collections": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "names",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": true,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "matchAliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "aliases",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "_NameFilter"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnByAlias",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": false,
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "returnReferencedTypes",
          "object": "_Filter",
          "type": {
            "$tag": "_InputBase",
            "input": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "kinds",
          "object": "_TypeFilter",
          "type": {
            "$tag": "_InputBase",
            "input": "_TypeKind"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "kinds",
          "type": {
            "$tag": "_InputBase",
            "input": "_TypeKind"
          }
        }
      ],
      "name": "_TypeFilter",
      "parent": {
        "$tag": "_InputBase",
        "input": "_Filter"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    }
  }
}