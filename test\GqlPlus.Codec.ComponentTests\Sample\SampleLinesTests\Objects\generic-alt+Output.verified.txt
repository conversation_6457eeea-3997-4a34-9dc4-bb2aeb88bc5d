﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcAltOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: GnrcAltOutp
        type: !_OutputBase
          typeParam: type
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: type
    name: GnrcAltOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type