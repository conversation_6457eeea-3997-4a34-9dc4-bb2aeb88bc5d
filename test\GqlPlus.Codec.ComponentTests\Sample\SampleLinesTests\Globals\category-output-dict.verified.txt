﻿!_Schema
categories: !_Map_Categories
  !_Identifier ctgrOutpDict: !_Category
    modifiers: [!_ModifierDictionary{by:'*',modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Basic}]
    name: ctgrOutpDict
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrOutpDict
    resolution: !_Resolution Parallel
types: !_Map_Type
  !_Identifier CtgrOutpDict: !_TypeOutput
    name: CtgrOutpDict
    typeKind: !_TypeKind Output