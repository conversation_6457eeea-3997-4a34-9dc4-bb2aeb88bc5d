﻿!_Schema
types: !_Map_Type
  !_Identifier ObjParamOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: test
        object: ObjParamOutp
        type: !_OutputBase
          typeParam: test
      - !_ObjectFor(_OutputField)
        name: type
        object: ObjParamOutp
        type: !_OutputBase
          typeParam: type
    fields:
      - !_OutputField
        name: test
        type: !_OutputBase
          typeParam: test
      - !_OutputField
        name: type
        type: !_OutputBase
          typeParam: type
    name: ObjParamOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: test
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type