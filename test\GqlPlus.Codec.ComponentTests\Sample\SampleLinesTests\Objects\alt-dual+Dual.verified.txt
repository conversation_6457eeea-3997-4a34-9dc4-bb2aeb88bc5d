﻿!_Schema
types: !_Map_Type
  !_Identifier AltDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltDualDual
        type: !_DualBase
          dual: ObjDualAltDualDual
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: ObjDualAltDualDual
    name: AltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjDualAltDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: ObjDualAltDualDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: ObjDualAltDualDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: ObjDualAltDualDual
    typeKind: !_TypeKind Dual