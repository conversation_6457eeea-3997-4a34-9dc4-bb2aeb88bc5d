﻿!_Schema
types: !_Map_Type
  !_Identifier FldOutpParamTypeDescr: !_TypeDual
    name: FldOutpParamTypeDescr
    typeKind: !_TypeKind Dual
  !_Identifier InOutpParamTypeDescr: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: InOutpParamTypeDescr
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: param
        object: InOutpParamTypeDescr
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: param
        type: !_InputBase
          input: Number
    name: InOutpParamTypeDescr
    typeKind: !_TypeKind Input
  !_Identifier OutpParamTypeDescr: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: OutpParamTypeDescr
        parameters:
          - !_InputParam
            input: InOutpParamTypeDescr
        type: !_DualBase
          description: 'Test Descr'
          dual: FldOutpParamTypeDescr
    fields:
      - !_OutputField
        name: field
        parameters:
          - !_InputParam
            input: InOutpParamTypeDescr
        type: !_DualBase
          description: 'Test Descr'
          dual: FldOutpParamTypeDescr
    name: OutpParamTypeDescr
    typeKind: !_TypeKind Output