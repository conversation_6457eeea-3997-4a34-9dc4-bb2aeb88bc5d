﻿!_Schema
types: !_Map_Type
  !_Identifier EnumOutpPrntGnrc: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntOutpPrntGnrc
        name: prnt_outpPrntGnrc
      - !_EnumLabel
        enum: EnumOutpPrntGnrc
        name: outpPrntGnrc
    items:
      - !_Aliased
        name: outpPrntGnrc
    name: EnumOutpPrntGnrc
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntOutpPrntGnrc
    typeKind: !_TypeKind Enum
  !_Identifier OutpPrntGnrc: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: OutpPrntGnrc
        type: !_OutputBase
          output: RefOutpPrntGnrc
          typeArgs:
            - !_OutputArg
              label: prnt_outpPrntGnrc
              typeKind: !_SimpleKind Enum
              typeName: EnumOutpPrntGnrc
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: RefOutpPrntGnrc
          typeArgs:
            - !_OutputArg
              label: prnt_outpPrntGnrc
              typeKind: !_SimpleKind Enum
              typeName: EnumOutpPrntGnrc
    name: OutpPrntGnrc
    typeKind: !_TypeKind Output
  !_Identifier PrntOutpPrntGnrc: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntOutpPrntGnrc
        name: prnt_outpPrntGnrc
    items:
      - !_Aliased
        name: prnt_outpPrntGnrc
    name: PrntOutpPrntGnrc
    typeKind: !_TypeKind Enum
  !_Identifier RefOutpPrntGnrc: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: RefOutpPrntGnrc
        type: !_OutputBase
          typeParam: type
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: type
    name: RefOutpPrntGnrc
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: PrntOutpPrntGnrc
        name: type