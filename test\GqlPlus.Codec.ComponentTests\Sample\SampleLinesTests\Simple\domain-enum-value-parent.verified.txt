﻿!_Schema
types: !_Map_Type
  !_Identifier DmnEnumValuePrnt: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumValuePrnt
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumValuePrnt
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumValuePrnt
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumValuePrnt
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumValuePrnt
    name: DmnEnumValuePrnt
    typeKind: !_TypeKind Domain
  !_Identifier EnumDmnEnumValuePrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntDmnEnumValuePrnt
        name: prnt_dmnEnumValuePrnt
      - !_EnumLabel
        enum: EnumDmnEnumValuePrnt
        name: dmnEnumValuePrnt
    items:
      - !_Aliased
        name: dmnEnumValuePrnt
    name: EnumDmnEnumValuePrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntDmnEnumValuePrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntDmnEnumValuePrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntDmnEnumValuePrnt
        name: prnt_dmnEnumValuePrnt
    items:
      - !_Aliased
        name: prnt_dmnEnumValuePrnt
    name: PrntDmnEnumValuePrnt
    typeKind: !_TypeKind Enum