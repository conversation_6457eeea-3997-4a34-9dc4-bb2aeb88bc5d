﻿!_Schema
types: !_Map_Type
  !_Identifier PrntFieldInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefPrntFieldInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: parent
        object: RefPrntFieldInp
        type: !_InputBase
          input: Number
      - !_ObjectFor(_InputField)
        name: field
        object: PrntFieldInp
        type: !_InputBase
          input: Number
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          input: Number
    name: PrntFieldInp
    parent: !_InputBase
      input: RefPrntFieldInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntFieldInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefPrntFieldInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: parent
        object: RefPrntFieldInp
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: parent
        type: !_InputBase
          input: Number
    name: RefPrntFieldInp
    typeKind: !_TypeKind Input