﻿!_Schema
types: !_Map_Type
  !_Identifier PrntDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefPrntDualDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: parent
        object: RefPrntDualDual
        type: !_DualBase
          dual: Number
    name: PrntDualDual
    parent: !_DualBase
      dual: RefPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefPrntDualDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: parent
        object: RefPrntDualDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: parent
        type: !_DualBase
          dual: Number
    name: RefPrntDualDual
    typeKind: !_TypeKind Dual