﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstFieldDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstFieldDualDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltCnstFieldDualDual
        type: !_DualBase
          dual: Number
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltCnstFieldDualDual
    parent: !_DualBase
      dual: PrntCnstFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstFieldDualDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: RefCnstFieldDualDual
        type: !_DualBase
          dual: AltCnstFieldDualDual
    name: CnstFieldDualDual
    parent: !_DualBase
      dual: RefCnstFieldDualDual
      typeArgs:
        - !_DualArg
          dual: AltCnstFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstFieldDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstFieldDualDual
        type: !_DualBase
          dual: String
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    name: PrntCnstFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier RefCnstFieldDualDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: RefCnstFieldDualDual
        type: !_DualBase
          typeParam: ref
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          typeParam: ref
    name: RefCnstFieldDualDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Dual
          typeName: PrntCnstFieldDualDual
        name: ref