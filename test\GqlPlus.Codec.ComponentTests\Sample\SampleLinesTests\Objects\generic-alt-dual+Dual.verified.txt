﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcAltDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcAltDualDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcAltDualDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: GnrcAltDualDual
        type: !_DualBase
          dual: RefGnrcAltDualDual
          typeArgs:
            - !_DualArg
              dual: AltGnrcAltDualDual
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: RefGnrcAltDualDual
          typeArgs:
            - !_DualArg
              dual: AltGnrcAltDualDual
    name: GnrcAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier RefGnrcAltDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefGnrcAltDualDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefGnrcAltDualDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref