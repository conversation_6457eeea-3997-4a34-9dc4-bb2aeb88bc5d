﻿<Project>

  <PropertyGroup Condition="$(IsTestProject) == false">
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition="$(IsTestProject) == true">
    <OutputType>Exe</OutputType>
  </PropertyGroup>

  <ItemGroup Condition="$(IsTestProject) == true">
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="xunit.v3" Version="2.0.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>

    <Compile Remove="TestResults\**" />
    <EmbeddedResource Remove="TestResults\**" />
    <None Remove="TestResults\**" />
    <Folder Remove="TestResults" />

    <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>

</Project>
