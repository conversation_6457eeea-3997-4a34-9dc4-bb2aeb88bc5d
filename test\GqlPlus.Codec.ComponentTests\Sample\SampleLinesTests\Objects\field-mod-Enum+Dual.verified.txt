﻿!_Schema
types: !_Map_Type
  !_Identifier EnumFieldModEnumDual: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumFieldModEnumDual
        name: value
    items:
      - !_Aliased
        name: value
    name: EnumFieldModEnumDual
    typeKind: !_TypeKind Enum
  !_Identifier FieldModEnumDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        modifiers: [!_ModifierDictionary{by:EnumFieldModEnumDual,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Enum}]
        name: field
        object: FieldModEnumDual
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        modifiers: [!_ModifierDictionary{by:EnumFieldModEnumDual,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Enum}]
        name: field
        type: !_DualBase
          dual: String
    name: FieldModEnumDual
    typeKind: !_TypeKind Dual