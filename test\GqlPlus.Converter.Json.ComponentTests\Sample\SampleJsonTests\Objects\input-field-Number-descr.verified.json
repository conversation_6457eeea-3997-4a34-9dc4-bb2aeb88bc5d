﻿{
  "$tag": "_Schema",
  "types": {
    "$tag": "_Map_Type",
    "InpFieldNmbrDescr": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": 42,
          "name": "field",
          "object": "InpFieldNmbrDescr",
          "type": {
            "$tag": "_InputBase",
            "description": "Test Descr",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "default": 42,
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "description": "Test Descr",
            "input": "Number"
          }
        }
      ],
      "name": "InpFieldNmbrDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    }
  }
}