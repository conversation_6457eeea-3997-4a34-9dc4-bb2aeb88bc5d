﻿!_Schema
types: !_Map_Type
  !_Identifier PrntUnionPrntDescr: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrntDescr
    items:
      - !_Aliased
        name: Number
    name: PrntUnionPrntDescr
    typeKind: !_TypeKind Union
  !_Identifier UnionPrntDescr: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrntDescr
      - !_UnionMember
        name: Number
        union: UnionPrntDescr
    items:
      - !_Aliased
        name: Number
    name: UnionPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Union
      typeName: PrntUnionPrntDescr
    typeKind: !_TypeKind Union