﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcPrntParamOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: AltGnrcPrntParamOutp
        type: !_OutputBase
          output: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: alt
        object: AltGnrcPrntParamOutp
        type: !_OutputBase
          output: Number
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: String
    fields:
      - !_OutputField
        name: alt
        type: !_OutputBase
          output: Number
    name: AltGnrcPrntParamOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntParamOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcPrntParamOutp
        type: !_OutputBase
          output: AltGnrcPrntParamOutp
    name: GnrcPrntParamOutp
    parent: !_OutputBase
      output: RefGnrcPrntParamOutp
      typeArgs:
        - !_OutputArg
          output: AltGnrcPrntParamOutp
    typeKind: !_TypeKind Output
  !_Identifier RefGnrcPrntParamOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcPrntParamOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcPrntParamOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Output
        name: ref