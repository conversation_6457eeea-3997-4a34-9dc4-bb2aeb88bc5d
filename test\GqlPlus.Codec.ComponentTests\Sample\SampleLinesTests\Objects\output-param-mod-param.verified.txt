﻿!_Schema
types: !_Map_Type
  !_Identifier DomOutpParamModParam: !_DomainNumber
    allItems:
      - !_DomainItem(_DomainRange)
        domain: DomOutpParamModParam
        exclude: false
        from: 1
        to: 10
    domainKind: !_DomainKind Number
    items:
      - !_DomainRange
        exclude: false
        from: 1
        to: 10
    name: DomOutpParamModParam
    typeKind: !_TypeKind Domain
  !_Identifier InOutpParamModParam: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: InOutpParamModParam
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: param
        object: InOutpParamModParam
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: param
        type: !_InputBase
          input: Number
    name: InOutpParamModParam
    typeKind: !_TypeKind Input
  !_Identifier OutpParamModParam: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: OutpParamModParam
        parameters:
          - !_InputParam
            input: InOutpParamModParam
            modifiers: [!_ModifierTypeParam{by:mod,modifierKind:!_ModifierKind Param}]
        type: !_OutputBase
          output: DomOutpParamModParam
    fields:
      - !_OutputField
        name: field
        parameters:
          - !_InputParam
            input: InOutpParamModParam
            modifiers: [!_ModifierTypeParam{by:mod,modifierKind:!_ModifierKind Param}]
        type: !_OutputBase
          output: DomOutpParamModParam
    name: OutpParamModParam
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: mod