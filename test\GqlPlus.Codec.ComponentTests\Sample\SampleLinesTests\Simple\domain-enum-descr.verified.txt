﻿!_Schema
types: !_Map_Type
  !_Identifier DmnEnumDescr: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumDescr
        exclude: false
        value: !_EnumValue
          label: dmnEnumDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumDescr
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: dmnEnumDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumDescr
    name: DmnEnumDescr
    typeKind: !_TypeKind Domain
  !_Identifier EnumDmnEnumDescr: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumDescr
        name: dmnEnumDescr
    items:
      - !_Aliased
        name: dmnEnumDescr
    name: EnumDmnEnumDescr
    typeKind: !_TypeKind Enum