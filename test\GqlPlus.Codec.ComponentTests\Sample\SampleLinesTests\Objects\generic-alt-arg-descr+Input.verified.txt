﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcAltArgDescrInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: GnrcAltArgDescrInp
        type: !_InputBase
          input: RefGnrcAltArgDescrInp
          typeArgs:
            - !_InputArg
              description: 'Test Descr'
              typeParam: type
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: RefGnrcAltArgDescrInp
          typeArgs:
            - !_InputArg
              description: 'Test Descr'
              typeParam: type
    name: GnrcAltArgDescrInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type
  !_Identifier RefGnrcAltArgDescrInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefGnrcAltArgDescrInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefGnrcAltArgDescrInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref