﻿{%- if base.typeParam -%}
  <I>${{ base.typeParam }}</I>
{%- elsif base.typeKind == "Enum" -%}
  {{ base.name }}.{{ base.member }}
{%- else -%}
  {{ base.dual }}{{ base.input }}{{ base.output }}
  {%- if base.typeArgs -%}
    <B>&lt;</B>
    {%- for arg in base.typeArgs -%}
      {%- render "base" with arg as base -%}
      {%- unless forloop.last %}, {% endunless -%}
    {%- endfor -%}
    <B>&gt;</B>
  {%- endif -%}
{%- endif -%}
