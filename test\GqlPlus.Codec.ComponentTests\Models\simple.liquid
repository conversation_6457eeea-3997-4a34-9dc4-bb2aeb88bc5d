﻿{%- if simple.parent %}
<p>
  <sub>Parent:</sub> {{ simple.parent.name }} <sup>{{ simple.parent.typeKind }}</sup>
</p>
{% endif -%}
{%- case simple.typeKind %}
  {%- when "Domain" %}
    {%- render "domain" with simple as domain %}
  {%- when "Enum" %}
    {%- render "enum" with simple as enum %}
  {%- when "Union" %}
    {%- render "union" with simple as union %}
  {%- else %}
    <i>Other Simple type:</i> " {{ simple.typeKind }}
{%- endcase %}
