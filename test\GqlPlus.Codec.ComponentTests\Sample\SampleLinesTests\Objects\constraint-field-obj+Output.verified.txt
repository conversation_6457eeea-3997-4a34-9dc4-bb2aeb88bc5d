﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstFieldObjOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: PrntCnstFieldObjOutp
        type: !_OutputBase
          output: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: alt
        object: AltCnstFieldObjOutp
        type: !_OutputBase
          output: Number
    fields:
      - !_OutputField
        name: alt
        type: !_OutputBase
          output: Number
    name: AltCnstFieldObjOutp
    parent: !_OutputBase
      output: PrntCnstFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstFieldObjOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: RefCnstFieldObjOutp
        type: !_OutputBase
          output: AltCnstFieldObjOutp
    name: CnstFieldObjOutp
    parent: !_OutputBase
      output: RefCnstFieldObjOutp
      typeArgs:
        - !_OutputArg
          output: AltCnstFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntCnstFieldObjOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: PrntCnstFieldObjOutp
        type: !_OutputBase
          output: String
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: String
    name: PrntCnstFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier RefCnstFieldObjOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: RefCnstFieldObjOutp
        type: !_OutputBase
          typeParam: ref
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: ref
    name: RefCnstFieldObjOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: PrntCnstFieldObjOutp
        name: ref