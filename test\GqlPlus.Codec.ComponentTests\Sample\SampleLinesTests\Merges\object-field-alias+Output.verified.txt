﻿!_Schema
types: !_Map_Type
  !_Identifier FldObjFieldAliasOutp: !_TypeOutput
    name: FldObjFieldAliasOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjFieldAliasOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        aliases: [field1,field2]
        name: field
        object: ObjFieldAliasOutp
        type: !_OutputBase
          output: FldObjFieldAliasOutp
    fields:
      - !_OutputField
        aliases: [field1,field2]
        name: field
        type: !_OutputBase
          output: FldObjFieldAliasOutp
    name: Obj<PERSON>ieldAliasOutp
    typeKind: !_TypeKind Output