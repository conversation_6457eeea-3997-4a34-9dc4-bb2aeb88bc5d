﻿!_Schema
types: !_Map_Type
  !_Identifier EnumOutpFieldEnum: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumOutpFieldEnum
        name: outpFieldEnum
    items:
      - !_Aliased
        name: outpFieldEnum
    name: EnumOutpFieldEnum
    typeKind: !_TypeKind Enum
  !_Identifier OutpFieldEnum: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        field: field
        label: outpFieldEnum
        object: OutpFieldEnum
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpFieldEnum
    fields:
      - !_OutputEnum
        field: field
        label: outpFieldEnum
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpFieldEnum
    name: OutpFieldEnum
    typeKind: !_TypeKind Output