﻿!_Schema
types: !_Map_Type
  !_Identifier DmnEnumSame: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumSame
        exclude: false
        value: !_EnumValue
          label: true
          typeKind: !_SimpleKind Enum
          typeName: Boolean
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: true
          typeKind: !_SimpleKind Enum
          typeName: Boolean
    name: DmnEnumSame
    typeKind: !_TypeKind Domain