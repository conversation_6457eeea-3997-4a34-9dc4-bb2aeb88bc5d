﻿!_Schema
types: !_Map_Type
  !_Identifier DomOutpParamModDmn: !_DomainNumber
    allItems:
      - !_DomainItem(_DomainRange)
        domain: DomOutpParamModDmn
        exclude: false
        from: 1
        to: 10
    domainKind: !_DomainKind Number
    items:
      - !_DomainRange
        exclude: false
        from: 1
        to: 10
    name: DomOutpParamModDmn
    typeKind: !_TypeKind Domain
  !_Identifier InOutpParamModDmn: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: InOutpParamModDmn
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: param
        object: InOutpParamModDmn
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: param
        type: !_InputBase
          input: Number
    name: InOutpParamModDmn
    typeKind: !_TypeKind Input
  !_Identifier OutpParamModDmn: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: OutpParamModDmn
        parameters:
          - !_InputParam
            input: InOutpParamModDmn
            modifiers: [!_ModifierDictionary{by:DomOutpParamModDmn,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Domain}]
        type: !_OutputBase
          output: DomOutpParamModDmn
    fields:
      - !_OutputField
        name: field
        parameters:
          - !_InputParam
            input: InOutpParamModDmn
            modifiers: [!_ModifierDictionary{by:DomOutpParamModDmn,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Domain}]
        type: !_OutputBase
          output: DomOutpParamModDmn
    name: OutpParamModDmn
    typeKind: !_TypeKind Output