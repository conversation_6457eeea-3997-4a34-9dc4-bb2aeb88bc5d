﻿!_Schema
types: !_Map_Type
  !_Identifier DmnBoolPrntDescr: !_DomainBoolean
    allItems:
      - !_DomainItem(_DomainTrueFalse)
        domain: PrntDmnBoolPrntDescr
        exclude: false
        value: true
      - !_DomainItem(_DomainTrueFalse)
        domain: DmnBoolPrntDescr
        exclude: false
        value: false
    domainKind: !_DomainKind Boolean
    items:
      - !_DomainTrueFalse
        exclude: false
        value: false
    name: DmnBoolPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnBoolPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnBoolPrntDescr: !_DomainBoolean
    allItems:
      - !_DomainItem(_DomainTrueFalse)
        domain: PrntDmnBoolPrntDescr
        exclude: false
        value: true
    domainKind: !_DomainKind Boolean
    items:
      - !_DomainTrueFalse
        exclude: false
        value: true
    name: PrntDmnBoolPrntDescr
    typeKind: !_TypeKind Domain