﻿!_Schema
types: !_Map_Type
  !_Identifier RefGnrcAltModParamInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        object: RefGnrcAltModParamInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        type: !_InputBase
          typeParam: ref
    name: RefGnrcAltModParamInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: mod