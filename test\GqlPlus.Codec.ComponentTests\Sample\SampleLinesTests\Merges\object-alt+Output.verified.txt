﻿!_Schema
types: !_Map_Type
  !_Identifier ObjAltOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: ObjAltOutp
        type: !_OutputBase
          output: ObjAltOutpType
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: ObjAltOutpType
    name: ObjAltOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjAltOutpType: !_TypeOutput
    name: ObjAltOutpType
    typeKind: !_TypeKind Output