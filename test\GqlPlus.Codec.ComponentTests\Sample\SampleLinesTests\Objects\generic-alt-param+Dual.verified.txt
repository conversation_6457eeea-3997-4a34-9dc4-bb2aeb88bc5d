﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcAltParamDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcAltParamDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcAltParamDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcAltParamDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltParamDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: GnrcAltParamDual
        type: !_DualBase
          dual: RefGnrcAltParamDual
          typeArgs:
            - !_DualArg
              dual: AltGnrcAltParamDual
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: RefGnrcAltParamDual
          typeArgs:
            - !_DualArg
              dual: AltGnrcAltParamDual
    name: GnrcAltParamDual
    typeKind: !_TypeKind Dual
  !_Identifier RefGnrcAltParamDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefGnrcAltParamDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefGnrcAltParamDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref