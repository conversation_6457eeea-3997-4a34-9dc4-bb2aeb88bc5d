﻿!_Schema
types: !_Map_Type
  !_Identifier AltDualOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: AltDualOutp
        type: !_DualBase
          dual: ObjDualAltDualOutp
    alternates:
      - !_OutputAlternate
        type: !_DualBase
          dual: ObjDualAltDualOutp
    name: AltDualOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjDualAltDualOutp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: ObjDualAltDualOutp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: ObjDualAltDualOutp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: ObjDualAltDualOutp
    typeKind: !_TypeKind Dual