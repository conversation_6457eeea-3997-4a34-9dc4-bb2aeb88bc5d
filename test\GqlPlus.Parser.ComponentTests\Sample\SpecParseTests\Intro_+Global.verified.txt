﻿!Sc I@001/0001
Success
{
  !Ou I@008/0001
  _Categories
  {
    !OF I@009/0002
    category
    :
    I@019/0002
    _Category
    !OF I@009/0003
    type
    :
    I@015/0003
    _Type
  }
  |
  I@007/0004
  _Category
  I@007/0005
  _Type
}
{
  !Ou I@008/0008
  _Category
  :
  I@007/0009
  _Aliased
  {
    !OF I@009/0010
    resolution
    :
    I@021/0010
    _Resolution
    !OF I@009/0011
    output
    :
    I@017/0011
    _TypeRef
    <
      I@026/0011
      _TypeKind.Output
    >
    !OF I@009/0012
    modifiers
    :
    I@020/0012
    _Modifiers
    []
  }
}
{
  !En I@006/0015
  _Resolution
  !EL I@020/0015
  Parallel
  !EL I@029/0015
  Sequential
  !EL I@040/0015
  Single
}
{
  !Ou I@008/0017
  _Directives
  {
    !OF I@009/0018
    directive
    :
    I@020/0018
    _Directive
    !OF I@009/0019
    type
    :
    I@015/0019
    _Type
  }
  |
  I@007/0020
  _Directive
  I@007/0021
  _Type
}
{
  !Ou I@008/0024
  _Directive
  :
  I@007/0025
  _Aliased
  {
    !OF I@009/0026
    parameters
    :
    I@021/0026
    _InputParam
    []
    !OF I@009/0027
    repeatable
    :
    I@021/0027
    Boolean
    !OF I@009/0028
    locations
    :
    I@020/0028
    _
    [_Location]
  }
}
{
  !En I@006/0031
  _Location
  !EL I@018/0031
  Operation
  !EL I@028/0031
  Variable
  !EL I@037/0031
  Field
  !EL I@043/0031
  Inline
  !EL I@050/0031
  Spread
  !EL I@057/0031
  Fragment
}
{
  !Ou I@008/0034
  _Setting
  :
  I@007/0035
  _Named
  {
    !OF I@009/0036
    value
    :
    I@016/0036
    _Constant
  }
}