﻿!_Schema
types: !_Map_Type
  !_Identifier AltAltModParamInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: AltAltModParamInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltAltModParamInp
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: alt
        type: !_InputBase
          input: Number
    name: AltAltModParamInp
    typeKind: !_TypeKind Input
  !_Identifier AltModParamInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        object: AltModParamInp
        type: !_InputBase
          input: AltAltModParamInp
    alternates:
      - !_InputAlternate
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        type: !_InputBase
          input: AltAltModParamInp
    name: AltModParamInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: mod