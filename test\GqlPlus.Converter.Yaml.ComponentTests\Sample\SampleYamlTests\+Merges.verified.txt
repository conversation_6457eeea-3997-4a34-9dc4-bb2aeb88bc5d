﻿!_Schema
aliases: [Opt1, Opt2]
categories: !_Map_Categories
  !_Identifier ctgr: !_Category
    name: ctgr
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: Ctgr
    resolution: !_Resolution Parallel
  !_Identifier ctgrAlias: !_Category
    aliases: [CatA1, CatA2]
    name: ctgrAlias
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrAlias
    resolution: !_Resolution Parallel
  !_Identifier ctgrDescr: !_Category
    description: 'First category Second category'
    name: ctgrDescr
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrDescr
    resolution: !_Resolution Parallel
  !_Identifier ctgrMod: !_Category
    aliases: [CatM1, CatM2]
    modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
    name: ctgrMod
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrMod
    resolution: !_Resolution Parallel
directives: !_Map_Directives
  !_Identifier Drct: !_Directive
    locations: !_Set(_Location) {Inline: _, Spread: _}
    name: Drct
    repeatable: false
  !_Identifier DrctAlias: !_Directive
    aliases: [DirA1, DirA2]
    locations: !_Set(_Location) {Field: _, Variable: _}
    name: DrctAlias
    repeatable: false
  !_Identifier DrctParam: !_Directive
    locations: !_Set(_Location) {Fragment: _, Operation: _}
    name: DrctParam
    parameters:
    - !_InputParam
      input: InDrctParam
    repeatable: false
name: Schema
settings: !_Map_Setting
  !_Identifier merged: !_Setting
    name: merged
    value:
    - true
    - 0
types: !_Map_Type
  !_Identifier Ctgr: !_TypeOutput
    name: Ctgr
    typeKind: !_TypeKind Output
  !_Identifier CtgrAlias: !_TypeOutput
    name: CtgrAlias
    typeKind: !_TypeKind Output
  !_Identifier CtgrDescr: !_TypeOutput
    name: CtgrDescr
    typeKind: !_TypeKind Output
  !_Identifier CtgrMod: !_TypeOutput
    name: CtgrMod
    typeKind: !_TypeKind Output
  !_Identifier DmnAlias: !_DomainNumber
    aliases: [Num1, Num2]
    domainKind: !_DomainKind Number
    name: DmnAlias
    typeKind: !_TypeKind Domain
  !_Identifier DmnBool: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBool
      exclude: false
      value: false
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBool
      exclude: false
      value: true
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: false
    - !_DomainTrueFalse
      exclude: false
      value: true
    name: DmnBool
    typeKind: !_TypeKind Domain
  !_Identifier DmnBoolDiff: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBoolDiff
      exclude: false
      value: true
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBoolDiff
      exclude: false
      value: false
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: true
    - !_DomainTrueFalse
      exclude: false
      value: false
    name: DmnBoolDiff
    typeKind: !_TypeKind Domain
  !_Identifier DmnBoolSame: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBoolSame
      exclude: false
      value: true
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: true
    name: DmnBoolSame
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumDiff: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumDiff
      exclude: false
      value: !_EnumValue
        label: true
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumDiff
      exclude: false
      value: !_EnumValue
        label: false
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: true
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: false
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    name: DmnEnumDiff
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumSame: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumSame
      exclude: false
      value: !_EnumValue
        label: true
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: true
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    name: DmnEnumSame
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbr: !_DomainNumber
    domainKind: !_DomainKind Number
    name: DmnNmbr
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbrDiff: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: DmnNmbrDiff
      exclude: false
      from: 1
      to: 9
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      from: 1
      to: 9
    name: DmnNmbrDiff
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbrSame: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: DmnNmbrSame
      exclude: false
      from: 1
      to: 9
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      from: 1
      to: 9
    name: DmnNmbrSame
    typeKind: !_TypeKind Domain
  !_Identifier DmnStr: !_DomainString
    domainKind: !_DomainKind String
    name: DmnStr
    typeKind: !_TypeKind Domain
  !_Identifier DmnStrDiff: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DmnStrDiff
      exclude: false
      pattern: a+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: a+
    name: DmnStrDiff
    typeKind: !_TypeKind Domain
  !_Identifier DmnStrSame: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DmnStrSame
      exclude: false
      pattern: a+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: a+
    name: DmnStrSame
    typeKind: !_TypeKind Domain
  !_Identifier EnumAlias: !_TypeEnum
    aliases: [En1, En2]
    allItems:
    - !_EnumLabel
      enum: EnumAlias
      name: enumAlias
    items:
    - !_Aliased
      name: enumAlias
    name: EnumAlias
    typeKind: !_TypeKind Enum
  !_Identifier EnumDiff: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDiff
      name: one
    - !_EnumLabel
      enum: EnumDiff
      name: two
    items:
    - !_Aliased
      name: one
    - !_Aliased
      name: two
    name: EnumDiff
    typeKind: !_TypeKind Enum
  !_Identifier EnumSame: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumSame
      name: enumSame
    items:
    - !_Aliased
      name: enumSame
    name: EnumSame
    typeKind: !_TypeKind Enum
  !_Identifier EnumSamePrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntEnumSamePrnt
      name: prnt_enumSamePrnt
    - !_EnumLabel
      enum: EnumSamePrnt
      name: enumSamePrnt
    items:
    - !_Aliased
      name: enumSamePrnt
    name: EnumSamePrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumSamePrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumValueAlias: !_TypeEnum
    allItems:
    - !_EnumLabel
      aliases: [val1, val2]
      enum: EnumValueAlias
      name: enumValueAlias
    items:
    - !_Aliased
      aliases: [val1, val2]
      name: enumValueAlias
    name: EnumValueAlias
    typeKind: !_TypeKind Enum
  !_Identifier FldObjFieldAliasDual: !_TypeDual
    name: FldObjFieldAliasDual
    typeKind: !_TypeKind Dual
  !_Identifier FldObjFieldAliasInp: !_TypeInput
    name: FldObjFieldAliasInp
    typeKind: !_TypeKind Input
  !_Identifier FldObjFieldAliasOutp: !_TypeOutput
    name: FldObjFieldAliasOutp
    typeKind: !_TypeKind Output
  !_Identifier FldObjFieldDual: !_TypeDual
    name: FldObjFieldDual
    typeKind: !_TypeKind Dual
  !_Identifier FldObjFieldInp: !_TypeInput
    name: FldObjFieldInp
    typeKind: !_TypeKind Input
  !_Identifier FldObjFieldOutp: !_TypeOutput
    name: FldObjFieldOutp
    typeKind: !_TypeKind Output
  !_Identifier FldOutpFieldParam: !_TypeDual
    name: FldOutpFieldParam
    typeKind: !_TypeKind Dual
  !_Identifier InDrctParam: !_TypeInput
    name: InDrctParam
    typeKind: !_TypeKind Input
  !_Identifier ObjAliasDual: !_TypeDual
    aliases: [Dual1, Dual2]
    name: ObjAliasDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjAliasInp: !_TypeInput
    aliases: [Input1, Input2]
    name: ObjAliasInp
    typeKind: !_TypeKind Input
  !_Identifier ObjAliasOutp: !_TypeOutput
    aliases: [Output1, Output2]
    name: ObjAliasOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: ObjAltDual
      type: !_DualBase
        dual: ObjAltDualType
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: ObjAltDualType
    name: ObjAltDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjAltDualType: !_TypeDual
    name: ObjAltDualType
    typeKind: !_TypeKind Dual
  !_Identifier ObjAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: ObjAltInp
      type: !_InputBase
        input: ObjAltInpType
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: ObjAltInpType
    name: ObjAltInp
    typeKind: !_TypeKind Input
  !_Identifier ObjAltInpType: !_TypeInput
    name: ObjAltInpType
    typeKind: !_TypeKind Input
  !_Identifier ObjAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: ObjAltOutp
      type: !_OutputBase
        output: ObjAltOutpType
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: ObjAltOutpType
    name: ObjAltOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjAltOutpType: !_TypeOutput
    name: ObjAltOutpType
    typeKind: !_TypeKind Output
  !_Identifier ObjCnstDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: ObjCnstDual
      type: !_DualBase
        typeParam: type
    - !_ObjectFor(_DualField)
      name: str
      object: ObjCnstDual
      type: !_DualBase
        typeParam: type
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: type
    - !_DualField
      name: str
      type: !_DualBase
        typeParam: type
    name: ObjCnstDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjCnstInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: ObjCnstInp
      type: !_InputBase
        typeParam: type
    - !_ObjectFor(_InputField)
      name: str
      object: ObjCnstInp
      type: !_InputBase
        typeParam: type
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: type
    - !_InputField
      name: str
      type: !_InputBase
        typeParam: type
    name: ObjCnstInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjCnstOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: ObjCnstOutp
      type: !_OutputBase
        typeParam: type
    - !_ObjectFor(_OutputField)
      name: str
      object: ObjCnstOutp
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    - !_OutputField
      name: str
      type: !_OutputBase
        typeParam: type
    name: ObjCnstOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjDual: !_TypeDual
    name: ObjDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjFieldAliasDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      aliases: [field1, field2]
      name: field
      object: ObjFieldAliasDual
      type: !_DualBase
        dual: FldObjFieldAliasDual
    fields:
    - !_DualField
      aliases: [field1, field2]
      name: field
      type: !_DualBase
        dual: FldObjFieldAliasDual
    name: ObjFieldAliasDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjFieldAliasInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      aliases: [field1, field2]
      name: field
      object: ObjFieldAliasInp
      type: !_InputBase
        input: FldObjFieldAliasInp
    fields:
    - !_InputField
      aliases: [field1, field2]
      name: field
      type: !_InputBase
        input: FldObjFieldAliasInp
    name: ObjFieldAliasInp
    typeKind: !_TypeKind Input
  !_Identifier ObjFieldAliasOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      aliases: [field1, field2]
      name: field
      object: ObjFieldAliasOutp
      type: !_OutputBase
        output: FldObjFieldAliasOutp
    fields:
    - !_OutputField
      aliases: [field1, field2]
      name: field
      type: !_OutputBase
        output: FldObjFieldAliasOutp
    name: ObjFieldAliasOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjFieldDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: ObjFieldDual
      type: !_DualBase
        dual: FldObjFieldDual
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: FldObjFieldDual
    name: ObjFieldDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjFieldInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: ObjFieldInp
      type: !_InputBase
        input: FldObjFieldInp
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: FldObjFieldInp
    name: ObjFieldInp
    typeKind: !_TypeKind Input
  !_Identifier ObjFieldOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: ObjFieldOutp
      type: !_OutputBase
        output: FldObjFieldOutp
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: FldObjFieldOutp
    name: ObjFieldOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjFieldTypeAliasDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: ObjFieldTypeAliasDual
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: String
    name: ObjFieldTypeAliasDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjFieldTypeAliasInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: ObjFieldTypeAliasInp
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: String
    name: ObjFieldTypeAliasInp
    typeKind: !_TypeKind Input
  !_Identifier ObjFieldTypeAliasOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: ObjFieldTypeAliasOutp
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: String
    name: ObjFieldTypeAliasOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjInp: !_TypeInput
    name: ObjInp
    typeKind: !_TypeKind Input
  !_Identifier ObjOutp: !_TypeOutput
    name: ObjOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjParamCnstDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: test
      object: ObjParamCnstDual
      type: !_DualBase
        typeParam: test
    - !_ObjectFor(_DualField)
      name: type
      object: ObjParamCnstDual
      type: !_DualBase
        typeParam: test
    fields:
    - !_DualField
      name: test
      type: !_DualBase
        typeParam: test
    - !_DualField
      name: type
      type: !_DualBase
        typeParam: test
    name: ObjParamCnstDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamCnstInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: test
      object: ObjParamCnstInp
      type: !_InputBase
        typeParam: test
    - !_ObjectFor(_InputField)
      name: type
      object: ObjParamCnstInp
      type: !_InputBase
        typeParam: test
    fields:
    - !_InputField
      name: test
      type: !_InputBase
        typeParam: test
    - !_InputField
      name: type
      type: !_InputBase
        typeParam: test
    name: ObjParamCnstInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamCnstOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: test
      object: ObjParamCnstOutp
      type: !_OutputBase
        typeParam: test
    - !_ObjectFor(_OutputField)
      name: type
      object: ObjParamCnstOutp
      type: !_OutputBase
        typeParam: test
    fields:
    - !_OutputField
      name: test
      type: !_OutputBase
        typeParam: test
    - !_OutputField
      name: type
      type: !_OutputBase
        typeParam: test
    name: ObjParamCnstOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: test
      object: ObjParamDual
      type: !_DualBase
        typeParam: test
    - !_ObjectFor(_DualField)
      name: type
      object: ObjParamDual
      type: !_DualBase
        typeParam: type
    fields:
    - !_DualField
      name: test
      type: !_DualBase
        typeParam: test
    - !_DualField
      name: type
      type: !_DualBase
        typeParam: type
    name: ObjParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjParamDupDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: test
      object: ObjParamDupDual
      type: !_DualBase
        typeParam: test
    - !_ObjectFor(_DualField)
      name: type
      object: ObjParamDupDual
      type: !_DualBase
        typeParam: test
    fields:
    - !_DualField
      name: test
      type: !_DualBase
        typeParam: test
    - !_DualField
      name: type
      type: !_DualBase
        typeParam: test
    name: ObjParamDupDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamDupInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: test
      object: ObjParamDupInp
      type: !_InputBase
        typeParam: test
    - !_ObjectFor(_InputField)
      name: type
      object: ObjParamDupInp
      type: !_InputBase
        typeParam: test
    fields:
    - !_InputField
      name: test
      type: !_InputBase
        typeParam: test
    - !_InputField
      name: type
      type: !_InputBase
        typeParam: test
    name: ObjParamDupInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamDupOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: test
      object: ObjParamDupOutp
      type: !_OutputBase
        typeParam: test
    - !_ObjectFor(_OutputField)
      name: type
      object: ObjParamDupOutp
      type: !_OutputBase
        typeParam: test
    fields:
    - !_OutputField
      name: test
      type: !_OutputBase
        typeParam: test
    - !_OutputField
      name: type
      type: !_OutputBase
        typeParam: test
    name: ObjParamDupOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: test
      object: ObjParamInp
      type: !_InputBase
        typeParam: test
    - !_ObjectFor(_InputField)
      name: type
      object: ObjParamInp
      type: !_InputBase
        typeParam: type
    fields:
    - !_InputField
      name: test
      type: !_InputBase
        typeParam: test
    - !_InputField
      name: type
      type: !_InputBase
        typeParam: type
    name: ObjParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjParamOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: test
      object: ObjParamOutp
      type: !_OutputBase
        typeParam: test
    - !_ObjectFor(_OutputField)
      name: type
      object: ObjParamOutp
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: test
      type: !_OutputBase
        typeParam: test
    - !_OutputField
      name: type
      type: !_OutputBase
        typeParam: type
    name: ObjParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjPrntDual: !_TypeDual
    name: ObjPrntDual
    parent: !_DualBase
      dual: RefObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjPrntInp: !_TypeInput
    name: ObjPrntInp
    parent: !_InputBase
      input: RefObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier ObjPrntOutp: !_TypeOutput
    name: ObjPrntOutp
    parent: !_OutputBase
      output: RefObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldEnumAlias: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      field: field
      label: true
      object: OutpFieldEnumAlias
      typeKind: !_SimpleKind Enum
      typeName: Boolean
    fields:
    - !_OutputEnum
      field: field
      label: true
      typeKind: !_SimpleKind Enum
      typeName: Boolean
    name: OutpFieldEnumAlias
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldEnumValue: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      field: field
      label: true
      object: OutpFieldEnumValue
      typeKind: !_SimpleKind Enum
      typeName: Boolean
    fields:
    - !_OutputEnum
      field: field
      label: true
      typeKind: !_SimpleKind Enum
      typeName: Boolean
    name: OutpFieldEnumValue
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpFieldParam
      parameters:
      - !_InputParam
        input: OutpFieldParam1
      - !_InputParam
        input: OutpFieldParam2
      type: !_DualBase
        dual: FldOutpFieldParam
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: OutpFieldParam1
      - !_InputParam
        input: OutpFieldParam2
      type: !_DualBase
        dual: FldOutpFieldParam
    name: OutpFieldParam
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldParam1: !_TypeInput
    name: OutpFieldParam1
    typeKind: !_TypeKind Input
  !_Identifier OutpFieldParam2: !_TypeInput
    name: OutpFieldParam2
    typeKind: !_TypeKind Input
  !_Identifier PrntEnumSamePrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntEnumSamePrnt
      name: prnt_enumSamePrnt
    items:
    - !_Aliased
      name: prnt_enumSamePrnt
    name: PrntEnumSamePrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntUnionSamePrnt: !_TypeUnion
    allItems:
    - !_UnionMember
      name: String
      union: PrntUnionSamePrnt
    items:
    - !_Aliased
      name: String
    name: PrntUnionSamePrnt
    typeKind: !_TypeKind Union
  !_Identifier RefObjPrntDual: !_TypeDual
    name: RefObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier RefObjPrntInp: !_TypeInput
    name: RefObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier RefObjPrntOutp: !_TypeOutput
    name: RefObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier UnionAlias: !_TypeUnion
    aliases: [UnA1, UnA2]
    allItems:
    - !_UnionMember
      name: Boolean
      union: UnionAlias
    - !_UnionMember
      name: Number
      union: UnionAlias
    items:
    - !_Aliased
      name: Boolean
    - !_Aliased
      name: Number
    name: UnionAlias
    typeKind: !_TypeKind Union
  !_Identifier UnionDiff: !_TypeUnion
    allItems:
    - !_UnionMember
      name: Boolean
      union: UnionDiff
    - !_UnionMember
      name: Number
      union: UnionDiff
    items:
    - !_Aliased
      name: Boolean
    - !_Aliased
      name: Number
    name: UnionDiff
    typeKind: !_TypeKind Union
  !_Identifier UnionSame: !_TypeUnion
    allItems:
    - !_UnionMember
      name: Boolean
      union: UnionSame
    items:
    - !_Aliased
      name: Boolean
    name: UnionSame
    typeKind: !_TypeKind Union
  !_Identifier UnionSamePrnt: !_TypeUnion
    allItems:
    - !_UnionMember
      name: String
      union: PrntUnionSamePrnt
    - !_UnionMember
      name: Boolean
      union: UnionSamePrnt
    items:
    - !_Aliased
      name: Boolean
    name: UnionSamePrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Union
      typeName: PrntUnionSamePrnt
    typeKind: !_TypeKind Union