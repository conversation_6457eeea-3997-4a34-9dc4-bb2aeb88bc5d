﻿[  
!Sc I@001/0001
Success
{
  !Ca P@010/0001
  ctgr
  (Parallel)
  !Tr I@012/0001
  Ctgr
}
{
  !Ca P@010/0001
  ctgrAlias
  [
    CatA1
    CatA2
  ]
  (Parallel)
  !Tr I@020/0001
  CtgrAlias
}
{
  'First category Second category'
  !Ca P@010/0002
  ctgrDescr
  (Parallel)
  !Tr I@012/0002
  CtgrDescr
}
{
  !Ca P@010/0001
  ctgrMod
  [
    CatM1
    CatM2
  ]
  (Parallel)
  !Tr I@020/0001
  CtgrMod
  ?
}
{
  !Di I@012/0001
  Drct
  (Unique)
  Inline, Spread
}
{
  !Di I@012/0001
  DrctAlias
  [
    DirA1
    DirA2
  ]
  (Unique)
  Variable, Field
}
{
  !Di I@012/0001
  DrctParam
  (
    !Pa
    I@022/0001
    InDrctParam
  )
  (Unique)
  Operation, Fragment
}
{
  !Op I@008/0001
  Schema
  [
    Opt1
    Opt2
  ]
  {
    !OS I@017/0001
    merged
    =( !c P@024/0002 [ !k I@024/0001 Boolean.true !k N@025/0002 0 ] )
  }
}
{
  !Ou I@008/0003
  Ctgr
}
{
  !Ou I@008/0003
  CtgrAlias
}
{
  !Ou I@008/0005
  CtgrDescr
}
{
  !Ou I@008/0003
  CtgrMod
}
{
  !In I@007/0003
  InDrctParam
}
{
  !Do I@008/0001
  DmnAlias
  [
    Num1
    Num2
  ]
  Number
}
{
  !Do I@008/0001
  DmnBool
  Boolean
  !DT P@026/0001
  False
  !DT P@026/0001
  True
}
{
  !Do I@008/0001
  DmnBoolDiff
  Boolean
  !DT I@030/0001
  True
  !DT I@030/0002
  False
}
{
  !Do I@008/0001
  DmnBoolSame
  Boolean
  !DT I@030/0001
  True
}
{
  !Do I@008/0001
  DmnEnumDiff
  Enum
  !DE I@027/0001
  Boolean
  true
  !DE I@027/0002
  Boolean
  false
}
{
  !Do I@008/0001
  DmnEnumSame
  Enum
  !DE I@027/0001
  Boolean
  true
}
{
  !Do I@008/0001
  DmnNmbr
  Number
}
{
  !Do I@008/0001
  DmnNmbrDiff
  Number
  !DN N@029/0001
  1
  ~
  9
}
{
  !Do I@008/0001
  DmnNmbrSame
  Number
  !DN N@029/0001
  1
  ~
  9
}
{
  !Do I@008/0001
  DmnStr
  String
}
{
  !Do I@008/0001
  DmnStrDiff
  String
  !DX R@028/0001
  /a+/
}
{
  !Do I@008/0001
  DmnStrSame
  String
  !DX R@028/0001
  /a+/
}
{
  !En I@006/0001
  EnumAlias
  [
    En1
    En2
  ]
  !EL I@024/0001
  enumAlias
}
{
  !En I@006/0001
  EnumDiff
  !EL I@017/0001
  one
  !EL I@017/0002
  two
}
{
  !En I@006/0001
  EnumSame
  !EL I@017/0001
  enumSame
}
{
  !En I@006/0001
  EnumSamePrnt
  :( !Tr I@022/0001 PrntEnumSamePrnt )
  !EL I@039/0001
  enumSamePrnt
}
{
  !En I@006/0003
  PrntEnumSamePrnt
  !EL I@025/0003
  prnt_enumSamePrnt
}
{
  !En I@006/0001
  EnumValueAlias
  !EL I@023/0001
  enumValueAlias
  [
    val1
    val2
  ]
}
{
  !Du I@006/0001
  ObjDual
}
{
  !In I@007/0001
  ObjInp
}
{
  !Ou I@008/0001
  ObjOutp
}
{
  !Du I@006/0001
  ObjAliasDual
  [
    Dual1
    Dual2
  ]
}
{
  !In I@007/0001
  ObjAliasInp
  [
    Input1
    Input2
  ]
}
{
  !Ou I@008/0001
  ObjAliasOutp
  [
    Output1
    Output2
  ]
}
{
  !Du I@006/0001
  ObjAltDual
  |
  I@021/0001
  ObjAltDualType
}
{
  !Du I@006/0003
  ObjAltDualType
}
{
  !In I@007/0001
  ObjAltInp
  |
  I@021/0001
  ObjAltInpType
}
{
  !In I@007/0003
  ObjAltInpType
}
{
  !Ou I@008/0001
  ObjAltOutp
  |
  I@023/0001
  ObjAltOutpType
}
{
  !Ou I@008/0003
  ObjAltOutpType
}
{
  !Du I@006/0001
  ObjCnstDual
  <
    I@019/0001
    $type
    :String
  >
  {
    !DF I@034/0001
    field
    :
    I@042/0001
    $type
    !DF I@034/0002
    str
    :
    I@040/0002
    $type
  }
}
{
  !In I@007/0001
  ObjCnstInp
  <
    I@019/0001
    $type
    :String
  >
  {
    !IF I@034/0001
    field
    :
    I@042/0001
    $type
    !IF I@034/0002
    str
    :
    I@040/0002
    $type
  }
}
{
  !Ou I@008/0001
  ObjCnstOutp
  <
    I@021/0001
    $type
    :String
  >
  {
    !OF I@036/0001
    field
    :
    I@044/0001
    $type
    !OF I@036/0002
    str
    :
    I@042/0002
    $type
  }
}
{
  !Du I@006/0001
  ObjFieldDual
  {
    !DF I@021/0001
    field
    :
    I@028/0001
    FldObjFieldDual
  }
}
{
  !Du I@006/0003
  FldObjFieldDual
}
{
  !In I@007/0001
  ObjFieldInp
  {
    !IF I@021/0001
    field
    :
    I@028/0001
    FldObjFieldInp
  }
}
{
  !In I@007/0003
  FldObjFieldInp
}
{
  !Ou I@008/0001
  ObjFieldOutp
  {
    !OF I@023/0001
    field
    :
    I@030/0001
    FldObjFieldOutp
  }
}
{
  !Ou I@008/0003
  FldObjFieldOutp
}
{
  !Du I@006/0001
  ObjFieldAliasDual
  {
    !DF I@026/0001
    field
    [
      field1
      field2
    ]
    :
    I@042/0001
    FldObjFieldAliasDual
  }
}
{
  !Du I@006/0003
  FldObjFieldAliasDual
}
{
  !In I@007/0001
  ObjFieldAliasInp
  {
    !IF I@026/0001
    field
    [
      field1
      field2
    ]
    :
    I@042/0001
    FldObjFieldAliasInp
  }
}
{
  !In I@007/0003
  FldObjFieldAliasInp
}
{
  !Ou I@008/0001
  ObjFieldAliasOutp
  {
    !OF I@028/0001
    field
    [
      field1
      field2
    ]
    :
    I@044/0001
    FldObjFieldAliasOutp
  }
}
{
  !Ou I@008/0003
  FldObjFieldAliasOutp
}
{
  !Du I@006/0001
  ObjFieldTypeAliasDual
  {
    !DF I@030/0001
    field
    :
    I@037/0001
    String
  }
}
{
  !In I@007/0001
  ObjFieldTypeAliasInp
  {
    !IF I@030/0001
    field
    :
    I@037/0001
    String
  }
}
{
  !Ou I@008/0001
  ObjFieldTypeAliasOutp
  {
    !OF I@032/0001
    field
    :
    I@039/0001
    String
  }
}
{
  !Du I@006/0001
  ObjParamDual
  <
    I@020/0001
    $test
    :String
    I@020/0002
    $type
    :String
  >
  {
    !DF I@035/0001
    test
    :
    I@042/0001
    $test
    !DF I@035/0002
    type
    :
    I@042/0002
    $type
  }
}
{
  !In I@007/0001
  ObjParamInp
  <
    I@020/0001
    $test
    :String
    I@020/0002
    $type
    :String
  >
  {
    !IF I@035/0001
    test
    :
    I@042/0001
    $test
    !IF I@035/0002
    type
    :
    I@042/0002
    $type
  }
}
{
  !Ou I@008/0001
  ObjParamOutp
  <
    I@022/0001
    $test
    :String
    I@022/0002
    $type
    :String
  >
  {
    !OF I@037/0001
    test
    :
    I@044/0001
    $test
    !OF I@037/0002
    type
    :
    I@044/0002
    $type
  }
}
{
  !Du I@006/0001
  ObjParamCnstDual
  <
    I@024/0001
    $test
    :String
  >
  {
    !DF I@039/0001
    test
    :
    I@046/0001
    $test
    !DF I@039/0002
    type
    :
    I@046/0002
    $test
  }
}
{
  !In I@007/0001
  ObjParamCnstInp
  <
    I@024/0001
    $test
    :String
  >
  {
    !IF I@039/0001
    test
    :
    I@046/0001
    $test
    !IF I@039/0002
    type
    :
    I@046/0002
    $test
  }
}
{
  !Ou I@008/0001
  ObjParamCnstOutp
  <
    I@026/0001
    $test
    :String
  >
  {
    !OF I@041/0001
    test
    :
    I@048/0001
    $test
    !OF I@041/0002
    type
    :
    I@048/0002
    $test
  }
}
{
  !Du I@006/0001
  ObjParamDupDual
  <
    I@023/0001
    $test
    :String
  >
  {
    !DF I@038/0001
    test
    :
    I@045/0001
    $test
    !DF I@038/0002
    type
    :
    I@045/0002
    $test
  }
}
{
  !In I@007/0001
  ObjParamDupInp
  <
    I@023/0001
    $test
    :String
  >
  {
    !IF I@038/0001
    test
    :
    I@045/0001
    $test
    !IF I@038/0002
    type
    :
    I@045/0002
    $test
  }
}
{
  !Ou I@008/0001
  ObjParamDupOutp
  <
    I@025/0001
    $test
    :String
  >
  {
    !OF I@040/0001
    test
    :
    I@047/0001
    $test
    !OF I@040/0002
    type
    :
    I@047/0002
    $test
  }
}
{
  !Du I@006/0001
  ObjPrntDual
  :
  I@021/0001
  RefObjPrntDual
}
{
  !Du I@006/0003
  RefObjPrntDual
}
{
  !In I@007/0001
  ObjPrntInp
  :
  I@021/0001
  RefObjPrntInp
}
{
  !In I@007/0003
  RefObjPrntInp
}
{
  !Ou I@008/0001
  ObjPrntOutp
  :
  I@023/0001
  RefObjPrntOutp
}
{
  !Ou I@008/0003
  RefObjPrntOutp
}
{
  !Ou I@008/0001
  OutpFieldEnumAlias
  {
    !OF I@029/0001
    field
    [
      field1
      field2
    ]
    =
    I@046/0001
    Boolean
    .true
  }
}
{
  !Ou I@008/0001
  OutpFieldEnumValue
  {
    !OF I@029/0001
    field
    =
    I@037/0001
    Boolean
    .true
  }
}
{
  !Ou I@008/0001
  OutpFieldParam
  {
    !OF I@025/0001
    field
    (
      !Pa
      I@031/0001
      OutpFieldParam1
      !Pa
      I@031/0002
      OutpFieldParam2
    )
    :
    I@049/0001
    FldOutpFieldParam
  }
}
{
  !In I@007/0003
  OutpFieldParam1
}
{
  !In I@007/0004
  OutpFieldParam2
}
{
  !Du I@006/0005
  FldOutpFieldParam
}
{
  !Un I@007/0001
  UnionAlias
  [
    UnA1
    UnA2
  ]
  !UM I@027/0001
  Boolean
  !UM I@027/0002
  Number
}
{
  !Un I@007/0001
  UnionDiff
  !UM I@019/0001
  Boolean
  !UM I@019/0002
  Number
}
{
  !Un I@007/0001
  UnionSame
  !UM I@019/0001
  Boolean
}
{
  !Un I@007/0001
  UnionSamePrnt
  :( !Tr I@024/0001 PrntUnionSamePrnt )
  !UM I@042/0001
  Boolean
}
{
  !Un I@007/0003
  PrntUnionSamePrnt
  !UM I@027/0003
  String
}
]