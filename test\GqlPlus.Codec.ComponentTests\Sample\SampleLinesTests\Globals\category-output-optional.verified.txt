﻿!_Schema
categories: !_Map_Categories
  !_Identifier ctgrOutpOptl: !_Category
    modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
    name: ctgrOutpOptl
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrOutpOptl
    resolution: !_Resolution Parallel
types: !_Map_Type
  !_Identifier CtgrOutpOptl: !_TypeOutput
    name: CtgrOutpOptl
    typeKind: !_TypeKind Output