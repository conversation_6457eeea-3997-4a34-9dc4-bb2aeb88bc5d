﻿!_Schema
categories: !_Map_Categories
  !_Identifier ctgrMod: !_Category
    aliases: [CatM1,CatM2]
    modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
    name: ctgrMod
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrMod
    resolution: !_Resolution Parallel
types: !_Map_Type
  !_Identifier CtgrMod: !_TypeOutput
    name: CtgrMod
    typeKind: !_TypeKind Output