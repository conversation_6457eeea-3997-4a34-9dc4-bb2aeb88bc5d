﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcPrntDualPrntInp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcPrntDualPrntInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcPrntDualPrntInp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcPrntDualPrntInp
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntDualPrntInp: !_TypeInput
    name: GnrcPrntDualPrntInp
    parent: !_InputBase
      input: RefGnrcPrntDualPrntInp
      typeArgs:
        - !_DualArg
          dual: AltGnrcPrntDualPrntInp
    typeKind: !_TypeKind Input
  !_Identifier RefGnrcPrntDualPrntInp: !_TypeInput
    name: RefGnrcPrntDualPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref