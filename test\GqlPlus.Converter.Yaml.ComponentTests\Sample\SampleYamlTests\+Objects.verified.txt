﻿!_Schema
types: !_Map_Type
  !_Identifier AltAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltAltDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltAltDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltAltDual
    typeKind: !_TypeKind Dual
  !_Identifier AltAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltAltInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltAltInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltAltInp
    typeKind: !_TypeKind Input
  !_Identifier AltAltModBoolDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltAltModBoolDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltAltModBoolDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltAltModBoolDual
    typeKind: !_TypeKind Dual
  !_Identifier AltAltModBoolInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltAltModBoolInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltAltModBoolInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltAltModBoolInp
    typeKind: !_TypeKind Input
  !_Identifier AltAltModBoolOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltAltModBoolOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltAltModBoolOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltAltModBoolOutp
    typeKind: !_TypeKind Output
  !_Identifier AltAltModParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltAltModParamDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltAltModParamDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltAltModParamDual
    typeKind: !_TypeKind Dual
  !_Identifier AltAltModParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltAltModParamInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltAltModParamInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltAltModParamInp
    typeKind: !_TypeKind Input
  !_Identifier AltAltModParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltAltModParamOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltAltModParamOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltAltModParamOutp
    typeKind: !_TypeKind Output
  !_Identifier AltAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltAltOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltAltOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltAltOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstAltDualDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstAltDualDual
    parent: !_DualBase
      dual: PrntCnstAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstAltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstAltDualInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstAltDualInp
    parent: !_DualBase
      dual: PrntCnstAltDualInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstAltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstAltDualOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstAltDualOutp
    parent: !_DualBase
      dual: PrntCnstAltDualOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstAltObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltObjDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstAltObjDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstAltObjDual
    parent: !_DualBase
      dual: PrntCnstAltObjDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstAltObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstAltObjInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstAltObjInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstAltObjInp
    parent: !_InputBase
      input: PrntCnstAltObjInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstAltObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstAltObjOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstAltObjOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstAltObjOutp
    parent: !_OutputBase
      output: PrntCnstAltObjOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstFieldDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstFieldDualDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstFieldDualDual
    parent: !_DualBase
      dual: PrntCnstFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstFieldDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstFieldDualInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstFieldDualInp
    parent: !_DualBase
      dual: PrntCnstFieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstFieldDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstFieldDualOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstFieldDualOutp
    parent: !_DualBase
      dual: PrntCnstFieldDualOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstFieldObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldObjDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstFieldObjDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstFieldObjDual
    parent: !_DualBase
      dual: PrntCnstFieldObjDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstFieldObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstFieldObjInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstFieldObjInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstFieldObjInp
    parent: !_InputBase
      input: PrntCnstFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstFieldObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstFieldObjOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstFieldObjOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstFieldObjOutp
    parent: !_OutputBase
      output: PrntCnstFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstPrntDualPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstPrntDualPrntDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstPrntDualPrntDual
    parent: !_DualBase
      dual: PrntCnstPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstPrntDualPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstPrntDualPrntInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstPrntDualPrntInp
    parent: !_DualBase
      dual: PrntCnstPrntDualPrntInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstPrntDualPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstPrntDualPrntOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstPrntDualPrntOutp
    parent: !_DualBase
      dual: PrntCnstPrntDualPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstPrntObjPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntObjPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstPrntObjPrntDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstPrntObjPrntDual
    parent: !_DualBase
      dual: PrntCnstPrntObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstPrntObjPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstPrntObjPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstPrntObjPrntInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstPrntObjPrntInp
    parent: !_InputBase
      input: PrntCnstPrntObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstPrntObjPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstPrntObjPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstPrntObjPrntOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstPrntObjPrntOutp
    parent: !_OutputBase
      output: PrntCnstPrntObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier AltDescrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltDescrDual
      type: !_DualBase
        description: 'Test Descr'
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        description: 'Test Descr'
        dual: String
    name: AltDescrDual
    typeKind: !_TypeKind Dual
  !_Identifier AltDescrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltDescrInp
      type: !_InputBase
        description: 'Test Descr'
        input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        description: 'Test Descr'
        input: String
    name: AltDescrInp
    typeKind: !_TypeKind Input
  !_Identifier AltDescrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltDescrOutp
      type: !_OutputBase
        description: 'Test Descr'
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        description: 'Test Descr'
        output: String
    name: AltDescrOutp
    typeKind: !_TypeKind Output
  !_Identifier AltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltDual
      type: !_DualBase
        dual: AltAltDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: AltAltDual
    name: AltDual
    typeKind: !_TypeKind Dual
  !_Identifier AltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltDualDual
      type: !_DualBase
        dual: ObjDualAltDualDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: ObjDualAltDualDual
    name: AltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltDualInp
      type: !_DualBase
        dual: ObjDualAltDualInp
    alternates:
    - !_InputAlternate
      type: !_DualBase
        dual: ObjDualAltDualInp
    name: AltDualInp
    typeKind: !_TypeKind Input
  !_Identifier AltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltDualOutp
      type: !_DualBase
        dual: ObjDualAltDualOutp
    alternates:
    - !_OutputAlternate
      type: !_DualBase
        dual: ObjDualAltDualOutp
    name: AltDualOutp
    typeKind: !_TypeKind Output
  !_Identifier AltGnrcAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcAltDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcAltDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcAltDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcAltDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcAltDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcAltDualInp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcAltDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcAltDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcAltDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcAltDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcAltParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcAltParamDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcAltParamDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcAltParamDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcAltParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltGnrcAltParamInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltGnrcAltParamInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltGnrcAltParamInp
    typeKind: !_TypeKind Input
  !_Identifier AltGnrcAltParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltGnrcAltParamOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltGnrcAltParamOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltGnrcAltParamOutp
    typeKind: !_TypeKind Output
  !_Identifier AltGnrcFieldDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcFieldDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcFieldDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcFieldDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcFieldDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcFieldDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcFieldDualInp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcFieldDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcFieldDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcFieldDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcFieldDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcFieldParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcFieldParamDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcFieldParamDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcFieldParamDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcFieldParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltGnrcFieldParamInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltGnrcFieldParamInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltGnrcFieldParamInp
    typeKind: !_TypeKind Input
  !_Identifier AltGnrcFieldParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltGnrcFieldParamOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltGnrcFieldParamOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltGnrcFieldParamOutp
    typeKind: !_TypeKind Output
  !_Identifier AltGnrcPrntDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualInp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntDualPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualPrntDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntDualPrntInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualPrntInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualPrntInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualPrntInp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntDualPrntOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualPrntOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualPrntOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualPrntOutp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntParamDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntParamDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntParamDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltGnrcPrntParamInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltGnrcPrntParamInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltGnrcPrntParamInp
    typeKind: !_TypeKind Input
  !_Identifier AltGnrcPrntParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltGnrcPrntParamOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltGnrcPrntParamOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltGnrcPrntParamOutp
    typeKind: !_TypeKind Output
  !_Identifier AltGnrcPrntParamPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntParamPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntParamPrntDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntParamPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntParamPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltGnrcPrntParamPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltGnrcPrntParamPrntInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltGnrcPrntParamPrntInp
    typeKind: !_TypeKind Input
  !_Identifier AltGnrcPrntParamPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltGnrcPrntParamPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltGnrcPrntParamPrntOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltGnrcPrntParamPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier AltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltInp
      type: !_InputBase
        input: AltAltInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: AltAltInp
    name: AltInp
    typeKind: !_TypeKind Input
  !_Identifier AltModBoolDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: AltModBoolDual
      type: !_DualBase
        dual: AltAltModBoolDual
    alternates:
    - !_DualAlternate
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_DualBase
        dual: AltAltModBoolDual
    name: AltModBoolDual
    typeKind: !_TypeKind Dual
  !_Identifier AltModBoolInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: AltModBoolInp
      type: !_InputBase
        input: AltAltModBoolInp
    alternates:
    - !_InputAlternate
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_InputBase
        input: AltAltModBoolInp
    name: AltModBoolInp
    typeKind: !_TypeKind Input
  !_Identifier AltModBoolOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: AltModBoolOutp
      type: !_OutputBase
        output: AltAltModBoolOutp
    alternates:
    - !_OutputAlternate
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_OutputBase
        output: AltAltModBoolOutp
    name: AltModBoolOutp
    typeKind: !_TypeKind Output
  !_Identifier AltModParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: AltModParamDual
      type: !_DualBase
        dual: AltAltModParamDual
    alternates:
    - !_DualAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_DualBase
        dual: AltAltModParamDual
    name: AltModParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier AltModParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: AltModParamInp
      type: !_InputBase
        input: AltAltModParamInp
    alternates:
    - !_InputAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_InputBase
        input: AltAltModParamInp
    name: AltModParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier AltModParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: AltModParamOutp
      type: !_OutputBase
        output: AltAltModParamOutp
    alternates:
    - !_OutputAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_OutputBase
        output: AltAltModParamOutp
    name: AltModParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier AltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltOutp
      type: !_OutputBase
        output: AltAltOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: AltAltOutp
    name: AltOutp
    typeKind: !_TypeKind Output
  !_Identifier AltSmplDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltSmplDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: AltSmplDual
    typeKind: !_TypeKind Dual
  !_Identifier AltSmplInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltSmplInp
      type: !_InputBase
        input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    name: AltSmplInp
    typeKind: !_TypeKind Input
  !_Identifier AltSmplOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltSmplOutp
      type: !_OutputBase
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    name: AltSmplOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstAltDmnDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: CnstAltDmnDual
      type: !_DualBase
        dual: RefCnstAltDmnDual
        typeArgs:
        - !_DualArg
          dual: DomCnstAltDmnDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefCnstAltDmnDual
        typeArgs:
        - !_DualArg
          dual: DomCnstAltDmnDual
    name: CnstAltDmnDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstAltDmnInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: CnstAltDmnInp
      type: !_InputBase
        input: RefCnstAltDmnInp
        typeArgs:
        - !_InputArg
          input: DomCnstAltDmnInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefCnstAltDmnInp
        typeArgs:
        - !_InputArg
          input: DomCnstAltDmnInp
    name: CnstAltDmnInp
    typeKind: !_TypeKind Input
  !_Identifier CnstAltDmnOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: CnstAltDmnOutp
      type: !_OutputBase
        output: RefCnstAltDmnOutp
        typeArgs:
        - !_OutputArg
          output: DomCnstAltDmnOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefCnstAltDmnOutp
        typeArgs:
        - !_OutputArg
          output: DomCnstAltDmnOutp
    name: CnstAltDmnOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: CnstAltDual
      type: !_DualBase
        typeParam: type
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: type
    name: CnstAltDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: Number
      name: type
  !_Identifier CnstAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: CnstAltDualDual
      type: !_DualBase
        dual: RefCnstAltDualDual
        typeArgs:
        - !_DualArg
          dual: AltCnstAltDualDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefCnstAltDualDual
        typeArgs:
        - !_DualArg
          dual: AltCnstAltDualDual
    name: CnstAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstAltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: CnstAltDualInp
      type: !_InputBase
        input: RefCnstAltDualInp
        typeArgs:
        - !_InputArg
          input: AltCnstAltDualInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefCnstAltDualInp
        typeArgs:
        - !_InputArg
          input: AltCnstAltDualInp
    name: CnstAltDualInp
    typeKind: !_TypeKind Input
  !_Identifier CnstAltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: CnstAltDualOutp
      type: !_OutputBase
        output: RefCnstAltDualOutp
        typeArgs:
        - !_OutputArg
          output: AltCnstAltDualOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefCnstAltDualOutp
        typeArgs:
        - !_OutputArg
          output: AltCnstAltDualOutp
    name: CnstAltDualOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: CnstAltInp
      type: !_InputBase
        typeParam: type
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: type
    name: CnstAltInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: Number
      name: type
  !_Identifier CnstAltObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: CnstAltObjDual
      type: !_DualBase
        dual: RefCnstAltObjDual
        typeArgs:
        - !_DualArg
          dual: AltCnstAltObjDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefCnstAltObjDual
        typeArgs:
        - !_DualArg
          dual: AltCnstAltObjDual
    name: CnstAltObjDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstAltObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: CnstAltObjInp
      type: !_InputBase
        input: RefCnstAltObjInp
        typeArgs:
        - !_InputArg
          input: AltCnstAltObjInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefCnstAltObjInp
        typeArgs:
        - !_InputArg
          input: AltCnstAltObjInp
    name: CnstAltObjInp
    typeKind: !_TypeKind Input
  !_Identifier CnstAltObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: CnstAltObjOutp
      type: !_OutputBase
        output: RefCnstAltObjOutp
        typeArgs:
        - !_OutputArg
          output: AltCnstAltObjOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefCnstAltObjOutp
        typeArgs:
        - !_OutputArg
          output: AltCnstAltObjOutp
    name: CnstAltObjOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: CnstAltOutp
      type: !_OutputBase
        typeParam: type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: type
    name: CnstAltOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: Number
      name: type
  !_Identifier CnstFieldDmnDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldDmnDual
      type: !_DualBase
        dual: DomCnstFieldDmnDual
    name: CnstFieldDmnDual
    parent: !_DualBase
      dual: RefCnstFieldDmnDual
      typeArgs:
      - !_DualArg
        dual: DomCnstFieldDmnDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstFieldDmnInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldDmnInp
      type: !_InputBase
        input: DomCnstFieldDmnInp
    name: CnstFieldDmnInp
    parent: !_InputBase
      input: RefCnstFieldDmnInp
      typeArgs:
      - !_InputArg
        input: DomCnstFieldDmnInp
    typeKind: !_TypeKind Input
  !_Identifier CnstFieldDmnOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldDmnOutp
      type: !_OutputBase
        output: DomCnstFieldDmnOutp
    name: CnstFieldDmnOutp
    parent: !_OutputBase
      output: RefCnstFieldDmnOutp
      typeArgs:
      - !_OutputArg
        output: DomCnstFieldDmnOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstFieldDualDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldDualDual
      type: !_DualBase
        dual: AltCnstFieldDualDual
    name: CnstFieldDualDual
    parent: !_DualBase
      dual: RefCnstFieldDualDual
      typeArgs:
      - !_DualArg
        dual: AltCnstFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstFieldDualInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldDualInp
      type: !_InputBase
        input: AltCnstFieldDualInp
    name: CnstFieldDualInp
    parent: !_InputBase
      input: RefCnstFieldDualInp
      typeArgs:
      - !_InputArg
        input: AltCnstFieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier CnstFieldDualOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldDualOutp
      type: !_OutputBase
        output: AltCnstFieldDualOutp
    name: CnstFieldDualOutp
    parent: !_OutputBase
      output: RefCnstFieldDualOutp
      typeArgs:
      - !_OutputArg
        output: AltCnstFieldDualOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstFieldObjDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldObjDual
      type: !_DualBase
        dual: AltCnstFieldObjDual
    name: CnstFieldObjDual
    parent: !_DualBase
      dual: RefCnstFieldObjDual
      typeArgs:
      - !_DualArg
        dual: AltCnstFieldObjDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstFieldObjInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldObjInp
      type: !_InputBase
        input: AltCnstFieldObjInp
    name: CnstFieldObjInp
    parent: !_InputBase
      input: RefCnstFieldObjInp
      typeArgs:
      - !_InputArg
        input: AltCnstFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier CnstFieldObjOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldObjOutp
      type: !_OutputBase
        output: AltCnstFieldObjOutp
    name: CnstFieldObjOutp
    parent: !_OutputBase
      output: RefCnstFieldObjOutp
      typeArgs:
      - !_OutputArg
        output: AltCnstFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstPrntDualPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstPrntDualPrntDual
      type: !_DualBase
        dual: Number
    name: CnstPrntDualPrntDual
    parent: !_DualBase
      dual: RefCnstPrntDualPrntDual
      typeArgs:
      - !_DualArg
        dual: AltCnstPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstPrntDualPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstPrntDualPrntInp
      type: !_InputBase
        input: Number
    name: CnstPrntDualPrntInp
    parent: !_InputBase
      input: RefCnstPrntDualPrntInp
      typeArgs:
      - !_InputArg
        input: AltCnstPrntDualPrntInp
    typeKind: !_TypeKind Input
  !_Identifier CnstPrntDualPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstPrntDualPrntOutp
      type: !_OutputBase
        output: Number
    name: CnstPrntDualPrntOutp
    parent: !_OutputBase
      output: RefCnstPrntDualPrntOutp
      typeArgs:
      - !_OutputArg
        output: AltCnstPrntDualPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstPrntObjPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntObjPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstPrntObjPrntDual
      type: !_DualBase
        dual: Number
    name: CnstPrntObjPrntDual
    parent: !_DualBase
      dual: RefCnstPrntObjPrntDual
      typeArgs:
      - !_DualArg
        dual: AltCnstPrntObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstPrntObjPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstPrntObjPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstPrntObjPrntInp
      type: !_InputBase
        input: Number
    name: CnstPrntObjPrntInp
    parent: !_InputBase
      input: RefCnstPrntObjPrntInp
      typeArgs:
      - !_InputArg
        input: AltCnstPrntObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier CnstPrntObjPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstPrntObjPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstPrntObjPrntOutp
      type: !_OutputBase
        output: Number
    name: CnstPrntObjPrntOutp
    parent: !_OutputBase
      output: RefCnstPrntObjPrntOutp
      typeArgs:
      - !_OutputArg
        output: AltCnstPrntObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier DomCnstAltDmnDual: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstAltDmnDual
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstAltDmnDual
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstAltDmnInp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstAltDmnInp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstAltDmnInp
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstAltDmnOutp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstAltDmnOutp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstAltDmnOutp
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstFieldDmnDual: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstFieldDmnDual
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstFieldDmnDual
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstFieldDmnInp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstFieldDmnInp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstFieldDmnInp
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstFieldDmnOutp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstFieldDmnOutp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstFieldDmnOutp
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntEnumDomDual: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DomGnrcPrntEnumDomDual
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomDualLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomDual
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomDualLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomDual
    name: DomGnrcPrntEnumDomDual
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntEnumDomInp: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DomGnrcPrntEnumDomInp
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomInpLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomInp
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomInpLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomInp
    name: DomGnrcPrntEnumDomInp
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntEnumDomOutp: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DomGnrcPrntEnumDomOutp
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomOutpLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomOutp
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomOutpLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomOutp
    name: DomGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntStrDomDual: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomGnrcPrntStrDomDual
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomGnrcPrntStrDomDual
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntStrDomInp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomGnrcPrntStrDomInp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomGnrcPrntStrDomInp
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntStrDomOutp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomGnrcPrntStrDomOutp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomGnrcPrntStrDomOutp
    typeKind: !_TypeKind Domain
  !_Identifier DomOutpParamModDmn: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: DomOutpParamModDmn
      exclude: false
      from: 1
      to: 10
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      from: 1
      to: 10
    name: DomOutpParamModDmn
    typeKind: !_TypeKind Domain
  !_Identifier DomOutpParamModParam: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: DomOutpParamModParam
      exclude: false
      from: 1
      to: 10
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      from: 1
      to: 10
    name: DomOutpParamModParam
    typeKind: !_TypeKind Domain
  !_Identifier EnumFieldModEnumDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumFieldModEnumDual
      name: value
    items:
    - !_Aliased
      name: value
    name: EnumFieldModEnumDual
    typeKind: !_TypeKind Enum
  !_Identifier EnumFieldModEnumInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumFieldModEnumInp
      name: value
    items:
    - !_Aliased
      name: value
    name: EnumFieldModEnumInp
    typeKind: !_TypeKind Enum
  !_Identifier EnumFieldModEnumOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumFieldModEnumOutp
      name: value
    items:
    - !_Aliased
      name: value
    name: EnumFieldModEnumOutp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumChildDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildDual
      name: gnrcPrntEnumChildDualParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumChildDual
      name: gnrcPrntEnumChildDualLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumChildDualLabel
    name: EnumGnrcPrntEnumChildDual
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumChildDual
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumChildInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildInp
      name: gnrcPrntEnumChildInpParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumChildInp
      name: gnrcPrntEnumChildInpLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumChildInpLabel
    name: EnumGnrcPrntEnumChildInp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumChildInp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumChildOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildOutp
      name: gnrcPrntEnumChildOutpParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumChildOutp
      name: gnrcPrntEnumChildOutpLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumChildOutpLabel
    name: EnumGnrcPrntEnumChildOutp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumDomDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomDual
      name: gnrcPrntEnumDomDualLabel
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomDual
      name: gnrcPrntEnumDomDualOther
    items:
    - !_Aliased
      name: gnrcPrntEnumDomDualLabel
    - !_Aliased
      name: gnrcPrntEnumDomDualOther
    name: EnumGnrcPrntEnumDomDual
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumDomInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomInp
      name: gnrcPrntEnumDomInpLabel
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomInp
      name: gnrcPrntEnumDomInpOther
    items:
    - !_Aliased
      name: gnrcPrntEnumDomInpLabel
    - !_Aliased
      name: gnrcPrntEnumDomInpOther
    name: EnumGnrcPrntEnumDomInp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumDomOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomOutp
      name: gnrcPrntEnumDomOutpLabel
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomOutp
      name: gnrcPrntEnumDomOutpOther
    items:
    - !_Aliased
      name: gnrcPrntEnumDomOutpLabel
    - !_Aliased
      name: gnrcPrntEnumDomOutpOther
    name: EnumGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumPrntDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntDual
      name: gnrcPrntEnumPrntDualParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumPrntDual
      name: gnrcPrntEnumPrntDualLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntDualLabel
    name: EnumGnrcPrntEnumPrntDual
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumPrntInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntInp
      name: gnrcPrntEnumPrntInpParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumPrntInp
      name: gnrcPrntEnumPrntInpLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntInpLabel
    name: EnumGnrcPrntEnumPrntInp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumPrntOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntOutp
      name: gnrcPrntEnumPrntOutpParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumPrntOutp
      name: gnrcPrntEnumPrntOutpLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntOutpLabel
    name: EnumGnrcPrntEnumPrntOutp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumPrntOutp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntSmplEnumDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntSmplEnumDual
      name: gnrcPrntSmplEnumDual
    items:
    - !_Aliased
      name: gnrcPrntSmplEnumDual
    name: EnumGnrcPrntSmplEnumDual
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntSmplEnumInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntSmplEnumInp
      name: gnrcPrntSmplEnumInp
    items:
    - !_Aliased
      name: gnrcPrntSmplEnumInp
    name: EnumGnrcPrntSmplEnumInp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntSmplEnumOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntSmplEnumOutp
      name: gnrcPrntSmplEnumOutp
    items:
    - !_Aliased
      name: gnrcPrntSmplEnumOutp
    name: EnumGnrcPrntSmplEnumOutp
    typeKind: !_TypeKind Enum
  !_Identifier EnumInpFieldEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumInpFieldEnum
      name: inpFieldEnum
    items:
    - !_Aliased
      name: inpFieldEnum
    name: EnumInpFieldEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpCnstDomEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpCnstDomEnum
      name: outpCnstDomEnum
    - !_EnumLabel
      enum: EnumOutpCnstDomEnum
      name: other
    items:
    - !_Aliased
      name: outpCnstDomEnum
    - !_Aliased
      name: other
    name: EnumOutpCnstDomEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpCnstEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpCnstEnum
      name: outpCnstEnum
    items:
    - !_Aliased
      name: outpCnstEnum
    name: EnumOutpCnstEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpCnstEnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentOutpCnstEnumPrnt
      name: parentOutpCnstEnumPrnt
    - !_EnumLabel
      enum: EnumOutpCnstEnumPrnt
      name: outpCnstEnumPrnt
    items:
    - !_Aliased
      name: outpCnstEnumPrnt
    name: EnumOutpCnstEnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentOutpCnstEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpCnstPrntEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentOutpCnstPrntEnum
      name: parentOutpCnstPrntEnum
    - !_EnumLabel
      enum: EnumOutpCnstPrntEnum
      name: outpCnstPrntEnum
    items:
    - !_Aliased
      name: outpCnstPrntEnum
    name: EnumOutpCnstPrntEnum
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentOutpCnstPrntEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpFieldEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpFieldEnum
      name: outpFieldEnum
    items:
    - !_Aliased
      name: outpFieldEnum
    name: EnumOutpFieldEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpFieldEnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntOutpFieldEnumPrnt
      name: prnt_outpFieldEnumPrnt
    - !_EnumLabel
      enum: EnumOutpFieldEnumPrnt
      name: outpFieldEnumPrnt
    items:
    - !_Aliased
      name: outpFieldEnumPrnt
    name: EnumOutpFieldEnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntOutpFieldEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpFieldValue: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpFieldValue
      name: outpFieldValue
    items:
    - !_Aliased
      name: outpFieldValue
    name: EnumOutpFieldValue
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpFieldValueDescr: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpFieldValueDescr
      name: outpFieldValueDescr
    items:
    - !_Aliased
      name: outpFieldValueDescr
    name: EnumOutpFieldValueDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpGnrcEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpGnrcEnum
      name: outpGnrcEnum
    items:
    - !_Aliased
      name: outpGnrcEnum
    name: EnumOutpGnrcEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpGnrcValue: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpGnrcValue
      name: outpGnrcValue
    items:
    - !_Aliased
      name: outpGnrcValue
    name: EnumOutpGnrcValue
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpPrntGnrc: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntOutpPrntGnrc
      name: prnt_outpPrntGnrc
    - !_EnumLabel
      enum: EnumOutpPrntGnrc
      name: outpPrntGnrc
    items:
    - !_Aliased
      name: outpPrntGnrc
    name: EnumOutpPrntGnrc
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntOutpPrntGnrc
    typeKind: !_TypeKind Enum
  !_Identifier FieldDescrDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      description: 'Test Descr'
      name: field
      object: FieldDescrDual
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      description: 'Test Descr'
      name: field
      type: !_DualBase
        dual: String
    name: FieldDescrDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldDescrInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      description: 'Test Descr'
      name: field
      object: FieldDescrInp
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      description: 'Test Descr'
      name: field
      type: !_InputBase
        input: String
    name: FieldDescrInp
    typeKind: !_TypeKind Input
  !_Identifier FieldDescrOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      description: 'Test Descr'
      name: field
      object: FieldDescrOutp
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      description: 'Test Descr'
      name: field
      type: !_OutputBase
        output: String
    name: FieldDescrOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldDual
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: String
    name: FieldDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldDualDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldDualDual
      type: !_DualBase
        dual: FldFieldDualDual
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: FldFieldDualDual
    name: FieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldDualInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldDualInp
      type: !_DualBase
        dual: FldFieldDualInp
    fields:
    - !_InputField
      name: field
      type: !_DualBase
        dual: FldFieldDualInp
    name: FieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier FieldDualOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldDualOutp
      type: !_DualBase
        dual: FldFieldDualOutp
    fields:
    - !_OutputField
      name: field
      type: !_DualBase
        dual: FldFieldDualOutp
    name: FieldDualOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldGnrcPrntEnumChildDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumChildDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: FieldGnrcPrntEnumChildDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumChildDual
      name: ref
  !_Identifier FieldGnrcPrntEnumChildInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumChildInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: FieldGnrcPrntEnumChildInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumChildInp
      name: ref
  !_Identifier FieldGnrcPrntEnumChildOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumChildOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: FieldGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumChildOutp
      name: ref
  !_Identifier FieldGnrcPrntEnumDomDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumDomDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: FieldGnrcPrntEnumDomDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumDomDual
      name: ref
  !_Identifier FieldGnrcPrntEnumDomInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumDomInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: FieldGnrcPrntEnumDomInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumDomInp
      name: ref
  !_Identifier FieldGnrcPrntEnumDomOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumDomOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: FieldGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumDomOutp
      name: ref
  !_Identifier FieldGnrcPrntEnumPrntDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumPrntDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: FieldGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: ParentGnrcPrntEnumPrntDual
      name: ref
  !_Identifier FieldGnrcPrntEnumPrntInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumPrntInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: FieldGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: ParentGnrcPrntEnumPrntInp
      name: ref
  !_Identifier FieldGnrcPrntEnumPrntOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumPrntOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: FieldGnrcPrntEnumPrntOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: ParentGnrcPrntEnumPrntOutp
      name: ref
  !_Identifier FieldGnrcPrntSmplEnumDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntSmplEnumDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: FieldGnrcPrntSmplEnumDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Simple
      name: ref
  !_Identifier FieldGnrcPrntSmplEnumInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntSmplEnumInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: FieldGnrcPrntSmplEnumInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Simple
      name: ref
  !_Identifier FieldGnrcPrntSmplEnumOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntSmplEnumOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: FieldGnrcPrntSmplEnumOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Simple
      name: ref
  !_Identifier FieldGnrcPrntStrDomDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntStrDomDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: FieldGnrcPrntStrDomDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier FieldGnrcPrntStrDomInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntStrDomInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: FieldGnrcPrntStrDomInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier FieldGnrcPrntStrDomOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntStrDomOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: FieldGnrcPrntStrDomOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier FieldInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldInp
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: String
    name: FieldInp
    typeKind: !_TypeKind Input
  !_Identifier FieldModEnumDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumDual,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      object: FieldModEnumDual
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumDual,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      type: !_DualBase
        dual: String
    name: FieldModEnumDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldModEnumInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumInp,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      object: FieldModEnumInp
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumInp,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      type: !_InputBase
        input: String
    name: FieldModEnumInp
    typeKind: !_TypeKind Input
  !_Identifier FieldModEnumOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumOutp,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      object: FieldModEnumOutp
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumOutp,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      type: !_OutputBase
        output: String
    name: FieldModEnumOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldModParamDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      object: FieldModParamDual
      type: !_DualBase
        dual: FldFieldModParamDual
    fields:
    - !_DualField
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      type: !_DualBase
        dual: FldFieldModParamDual
    name: FieldModParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier FieldModParamInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      object: FieldModParamInp
      type: !_InputBase
        input: FldFieldModParamInp
    fields:
    - !_InputField
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      type: !_InputBase
        input: FldFieldModParamInp
    name: FieldModParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier FieldModParamOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      object: FieldModParamOutp
      type: !_OutputBase
        output: FldFieldModParamOutp
    fields:
    - !_OutputField
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      type: !_OutputBase
        output: FldFieldModParamOutp
    name: FieldModParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier FieldObjDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldObjDual
      type: !_DualBase
        dual: FldFieldObjDual
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: FldFieldObjDual
    name: FieldObjDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldObjInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldObjInp
      type: !_InputBase
        input: FldFieldObjInp
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: FldFieldObjInp
    name: FieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier FieldObjOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldObjOutp
      type: !_OutputBase
        output: FldFieldObjOutp
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: FldFieldObjOutp
    name: FieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldOutp
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: String
    name: FieldOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldSmplDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldSmplDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FieldSmplDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldSmplInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldSmplInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: Number
    name: FieldSmplInp
    typeKind: !_TypeKind Input
  !_Identifier FieldSmplOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldSmplOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: Number
    name: FieldSmplOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldTypeDescrDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldTypeDescrDual
      type: !_DualBase
        description: 'Test Descr'
        dual: Number
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        description: 'Test Descr'
        dual: Number
    name: FieldTypeDescrDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldTypeDescrInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldTypeDescrInp
      type: !_InputBase
        description: 'Test Descr'
        input: Number
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        description: 'Test Descr'
        input: Number
    name: FieldTypeDescrInp
    typeKind: !_TypeKind Input
  !_Identifier FieldTypeDescrOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldTypeDescrOutp
      type: !_OutputBase
        description: 'Test Descr'
        output: Number
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        description: 'Test Descr'
        output: Number
    name: FieldTypeDescrOutp
    typeKind: !_TypeKind Output
  !_Identifier FldFieldDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: FldFieldDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FldFieldDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FldFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: FldFieldDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FldFieldDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FldFieldDualInp
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: FldFieldDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FldFieldDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FldFieldDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldModParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: FldFieldModParamDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FldFieldModParamDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FldFieldModParamDual
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldModParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: FldFieldModParamInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FldFieldModParamInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: Number
    name: FldFieldModParamInp
    typeKind: !_TypeKind Input
  !_Identifier FldFieldModParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: FldFieldModParamOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FldFieldModParamOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: Number
    name: FldFieldModParamOutp
    typeKind: !_TypeKind Output
  !_Identifier FldFieldObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: FldFieldObjDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FldFieldObjDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FldFieldObjDual
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: FldFieldObjInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FldFieldObjInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: Number
    name: FldFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier FldFieldObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: FldFieldObjOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FldFieldObjOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: Number
    name: FldFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier FldInpFieldNull: !_TypeDual
    name: FldInpFieldNull
    typeKind: !_TypeKind Dual
  !_Identifier FldOutpDescrParam: !_TypeDual
    name: FldOutpDescrParam
    typeKind: !_TypeKind Dual
  !_Identifier FldOutpParam: !_TypeDual
    name: FldOutpParam
    typeKind: !_TypeKind Dual
  !_Identifier FldOutpParamDescr: !_TypeDual
    name: FldOutpParamDescr
    typeKind: !_TypeKind Dual
  !_Identifier FldOutpParamTypeDescr: !_TypeDual
    name: FldOutpParamTypeDescr
    typeKind: !_TypeKind Dual
  !_Identifier FldOutpPrntParam: !_TypeDual
    name: FldOutpPrntParam
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltArgDescrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltArgDescrDual
      type: !_DualBase
        dual: RefGnrcAltArgDescrDual
        typeArgs:
        - !_DualArg
          description: 'Test Descr'
          typeParam: type
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefGnrcAltArgDescrDual
        typeArgs:
        - !_DualArg
          description: 'Test Descr'
          typeParam: type
    name: GnrcAltArgDescrDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltArgDescrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltArgDescrInp
      type: !_InputBase
        input: RefGnrcAltArgDescrInp
        typeArgs:
        - !_InputArg
          description: 'Test Descr'
          typeParam: type
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefGnrcAltArgDescrInp
        typeArgs:
        - !_InputArg
          description: 'Test Descr'
          typeParam: type
    name: GnrcAltArgDescrInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltArgDescrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltArgDescrOutp
      type: !_OutputBase
        output: RefGnrcAltArgDescrOutp
        typeArgs:
        - !_OutputArg
          description: 'Test Descr'
          typeParam: type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefGnrcAltArgDescrOutp
        typeArgs:
        - !_OutputArg
          description: 'Test Descr'
          typeParam: type
    name: GnrcAltArgDescrOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltArgDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltArgDual
      type: !_DualBase
        dual: RefGnrcAltArgDual
        typeArgs:
        - !_DualArg
          typeParam: type
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefGnrcAltArgDual
        typeArgs:
        - !_DualArg
          typeParam: type
    name: GnrcAltArgDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltArgInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltArgInp
      type: !_InputBase
        input: RefGnrcAltArgInp
        typeArgs:
        - !_InputArg
          typeParam: type
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefGnrcAltArgInp
        typeArgs:
        - !_InputArg
          typeParam: type
    name: GnrcAltArgInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltArgOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltArgOutp
      type: !_OutputBase
        output: RefGnrcAltArgOutp
        typeArgs:
        - !_OutputArg
          typeParam: type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefGnrcAltArgOutp
        typeArgs:
        - !_OutputArg
          typeParam: type
    name: GnrcAltArgOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltDual
      type: !_DualBase
        typeParam: type
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: type
    name: GnrcAltDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltDualDual
      type: !_DualBase
        dual: RefGnrcAltDualDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefGnrcAltDualDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualDual
    name: GnrcAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltDualInp
      type: !_InputBase
        input: RefGnrcAltDualInp
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefGnrcAltDualInp
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualInp
    name: GnrcAltDualInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcAltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltDualOutp
      type: !_OutputBase
        output: RefGnrcAltDualOutp
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefGnrcAltDualOutp
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualOutp
    name: GnrcAltDualOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltInp
      type: !_InputBase
        typeParam: type
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: type
    name: GnrcAltInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltOutp
      type: !_OutputBase
        typeParam: type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: type
    name: GnrcAltOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltParamDual
      type: !_DualBase
        dual: RefGnrcAltParamDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltParamDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefGnrcAltParamDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltParamDual
    name: GnrcAltParamDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltParamInp
      type: !_InputBase
        input: RefGnrcAltParamInp
        typeArgs:
        - !_InputArg
          input: AltGnrcAltParamInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefGnrcAltParamInp
        typeArgs:
        - !_InputArg
          input: AltGnrcAltParamInp
    name: GnrcAltParamInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcAltParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltParamOutp
      type: !_OutputBase
        output: RefGnrcAltParamOutp
        typeArgs:
        - !_OutputArg
          output: AltGnrcAltParamOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefGnrcAltParamOutp
        typeArgs:
        - !_OutputArg
          output: AltGnrcAltParamOutp
    name: GnrcAltParamOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcAltSmplDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltSmplDual
      type: !_DualBase
        dual: RefGnrcAltSmplDual
        typeArgs:
        - !_DualArg
          dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefGnrcAltSmplDual
        typeArgs:
        - !_DualArg
          dual: String
    name: GnrcAltSmplDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltSmplInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltSmplInp
      type: !_InputBase
        input: RefGnrcAltSmplInp
        typeArgs:
        - !_InputArg
          input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefGnrcAltSmplInp
        typeArgs:
        - !_InputArg
          input: String
    name: GnrcAltSmplInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcAltSmplOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltSmplOutp
      type: !_OutputBase
        output: RefGnrcAltSmplOutp
        typeArgs:
        - !_OutputArg
          output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefGnrcAltSmplOutp
        typeArgs:
        - !_OutputArg
          output: String
    name: GnrcAltSmplOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcDescrDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: GnrcDescrDual
      type: !_DualBase
        typeParam: type
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: type
    name: GnrcDescrDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      description: 'Test Descr'
      name: type
  !_Identifier GnrcDescrInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: GnrcDescrInp
      type: !_InputBase
        typeParam: type
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: type
    name: GnrcDescrInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      description: 'Test Descr'
      name: type
  !_Identifier GnrcDescrOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: GnrcDescrOutp
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: GnrcDescrOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      description: 'Test Descr'
      name: type
  !_Identifier GnrcFieldArgDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: GnrcFieldArgDual
      type: !_DualBase
        dual: RefGnrcFieldArgDual
        typeArgs:
        - !_DualArg
          typeParam: type
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: RefGnrcFieldArgDual
        typeArgs:
        - !_DualArg
          typeParam: type
    name: GnrcFieldArgDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldArgInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: GnrcFieldArgInp
      type: !_InputBase
        input: RefGnrcFieldArgInp
        typeArgs:
        - !_InputArg
          typeParam: type
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: RefGnrcFieldArgInp
        typeArgs:
        - !_InputArg
          typeParam: type
    name: GnrcFieldArgInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldArgOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: GnrcFieldArgOutp
      type: !_OutputBase
        output: RefGnrcFieldArgOutp
        typeArgs:
        - !_OutputArg
          typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: RefGnrcFieldArgOutp
        typeArgs:
        - !_OutputArg
          typeParam: type
    name: GnrcFieldArgOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: GnrcFieldDual
      type: !_DualBase
        typeParam: type
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: type
    name: GnrcFieldDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldDualDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: GnrcFieldDualDual
      type: !_DualBase
        dual: RefGnrcFieldDualDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualDual
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: RefGnrcFieldDualDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualDual
    name: GnrcFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcFieldDualInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: GnrcFieldDualInp
      type: !_InputBase
        input: RefGnrcFieldDualInp
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualInp
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: RefGnrcFieldDualInp
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualInp
    name: GnrcFieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcFieldDualOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: GnrcFieldDualOutp
      type: !_OutputBase
        output: RefGnrcFieldDualOutp
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualOutp
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: RefGnrcFieldDualOutp
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualOutp
    name: GnrcFieldDualOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcFieldInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: GnrcFieldInp
      type: !_InputBase
        typeParam: type
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: type
    name: GnrcFieldInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: GnrcFieldOutp
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: GnrcFieldOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldParamDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: GnrcFieldParamDual
      type: !_DualBase
        dual: RefGnrcFieldParamDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldParamDual
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: RefGnrcFieldParamDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldParamDual
    name: GnrcFieldParamDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcFieldParamInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: GnrcFieldParamInp
      type: !_InputBase
        input: RefGnrcFieldParamInp
        typeArgs:
        - !_InputArg
          input: AltGnrcFieldParamInp
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: RefGnrcFieldParamInp
        typeArgs:
        - !_InputArg
          input: AltGnrcFieldParamInp
    name: GnrcFieldParamInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcFieldParamOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: GnrcFieldParamOutp
      type: !_OutputBase
        output: RefGnrcFieldParamOutp
        typeArgs:
        - !_OutputArg
          output: AltGnrcFieldParamOutp
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: RefGnrcFieldParamOutp
        typeArgs:
        - !_OutputArg
          output: AltGnrcFieldParamOutp
    name: GnrcFieldParamOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntArgDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntArgDual
      type: !_DualBase
        typeParam: ref
    name: GnrcPrntArgDual
    parent: !_DualBase
      dual: RefGnrcPrntArgDual
      typeArgs:
      - !_DualArg
        typeParam: type
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntArgInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntArgInp
      type: !_InputBase
        typeParam: ref
    name: GnrcPrntArgInp
    parent: !_InputBase
      input: RefGnrcPrntArgInp
      typeArgs:
      - !_InputArg
        typeParam: type
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntArgOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntArgOutp
      type: !_OutputBase
        typeParam: ref
    name: GnrcPrntArgOutp
    parent: !_OutputBase
      output: RefGnrcPrntArgOutp
      typeArgs:
      - !_OutputArg
        typeParam: type
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntDescrDual: !_TypeDual
    name: GnrcPrntDescrDual
    parent: !_DualBase
      description: 'Parent comment'
      typeParam: type
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntDescrInp: !_TypeInput
    name: GnrcPrntDescrInp
    parent: !_InputBase
      description: 'Parent comment'
      typeParam: type
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntDescrOutp: !_TypeOutput
    name: GnrcPrntDescrOutp
    parent: !_OutputBase
      description: 'Parent comment'
      typeParam: type
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntDual: !_TypeDual
    name: GnrcPrntDual
    parent: !_DualBase
      typeParam: type
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntDualDual
      type: !_DualBase
        dual: AltGnrcPrntDualDual
    name: GnrcPrntDualDual
    parent: !_DualBase
      dual: RefGnrcPrntDualDual
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntDualInp
      type: !_DualBase
        dual: AltGnrcPrntDualInp
    name: GnrcPrntDualInp
    parent: !_InputBase
      input: RefGnrcPrntDualInp
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntDualOutp
      type: !_DualBase
        dual: AltGnrcPrntDualOutp
    name: GnrcPrntDualOutp
    parent: !_OutputBase
      output: RefGnrcPrntDualOutp
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntDualPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualPrntDual
      type: !_DualBase
        dual: Number
    name: GnrcPrntDualPrntDual
    parent: !_DualBase
      dual: RefGnrcPrntDualPrntDual
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntDualPrntInp: !_TypeInput
    name: GnrcPrntDualPrntInp
    parent: !_InputBase
      input: RefGnrcPrntDualPrntInp
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualPrntInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntDualPrntOutp: !_TypeOutput
    name: GnrcPrntDualPrntOutp
    parent: !_OutputBase
      output: RefGnrcPrntDualPrntOutp
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntEnumChildDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumChildDual
      type: !_DualBase
        dual: ParentGnrcPrntEnumChildDual
    name: GnrcPrntEnumChildDual
    parent: !_DualBase
      dual: FieldGnrcPrntEnumChildDual
      typeArgs:
      - !_DualArg
        dual: ParentGnrcPrntEnumChildDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntEnumChildInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumChildInp
      type: !_InputBase
        input: ParentGnrcPrntEnumChildInp
    name: GnrcPrntEnumChildInp
    parent: !_InputBase
      input: FieldGnrcPrntEnumChildInp
      typeArgs:
      - !_InputArg
        input: ParentGnrcPrntEnumChildInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntEnumChildOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumChildOutp
      type: !_OutputBase
        output: ParentGnrcPrntEnumChildOutp
    name: GnrcPrntEnumChildOutp
    parent: !_OutputBase
      output: FieldGnrcPrntEnumChildOutp
      typeArgs:
      - !_OutputArg
        output: ParentGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntEnumDomDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumDomDual
      type: !_DualBase
        dual: DomGnrcPrntEnumDomDual
    name: GnrcPrntEnumDomDual
    parent: !_DualBase
      dual: FieldGnrcPrntEnumDomDual
      typeArgs:
      - !_DualArg
        dual: DomGnrcPrntEnumDomDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntEnumDomInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumDomInp
      type: !_InputBase
        input: DomGnrcPrntEnumDomInp
    name: GnrcPrntEnumDomInp
    parent: !_InputBase
      input: FieldGnrcPrntEnumDomInp
      typeArgs:
      - !_InputArg
        input: DomGnrcPrntEnumDomInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntEnumDomOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumDomOutp
      type: !_OutputBase
        output: DomGnrcPrntEnumDomOutp
    name: GnrcPrntEnumDomOutp
    parent: !_OutputBase
      output: FieldGnrcPrntEnumDomOutp
      typeArgs:
      - !_OutputArg
        output: DomGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntEnumPrntDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumPrntDual
      type: !_DualBase
        dual: EnumGnrcPrntEnumPrntDual
    name: GnrcPrntEnumPrntDual
    parent: !_DualBase
      dual: FieldGnrcPrntEnumPrntDual
      typeArgs:
      - !_DualArg
        dual: EnumGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntEnumPrntInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumPrntInp
      type: !_InputBase
        input: EnumGnrcPrntEnumPrntInp
    name: GnrcPrntEnumPrntInp
    parent: !_InputBase
      input: FieldGnrcPrntEnumPrntInp
      typeArgs:
      - !_InputArg
        input: EnumGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntEnumPrntOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumPrntOutp
      type: !_OutputBase
        output: EnumGnrcPrntEnumPrntOutp
    name: GnrcPrntEnumPrntOutp
    parent: !_OutputBase
      output: FieldGnrcPrntEnumPrntOutp
      typeArgs:
      - !_OutputArg
        output: EnumGnrcPrntEnumPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntInp: !_TypeInput
    name: GnrcPrntInp
    parent: !_InputBase
      typeParam: type
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntOutp: !_TypeOutput
    name: GnrcPrntOutp
    parent: !_OutputBase
      typeParam: type
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntParamDual
      type: !_DualBase
        dual: AltGnrcPrntParamDual
    name: GnrcPrntParamDual
    parent: !_DualBase
      dual: RefGnrcPrntParamDual
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntParamDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntParamInp
      type: !_InputBase
        input: AltGnrcPrntParamInp
    name: GnrcPrntParamInp
    parent: !_InputBase
      input: RefGnrcPrntParamInp
      typeArgs:
      - !_InputArg
        input: AltGnrcPrntParamInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntParamOutp
      type: !_OutputBase
        output: AltGnrcPrntParamOutp
    name: GnrcPrntParamOutp
    parent: !_OutputBase
      output: RefGnrcPrntParamOutp
      typeArgs:
      - !_OutputArg
        output: AltGnrcPrntParamOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntParamPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntParamPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntParamPrntDual
      type: !_DualBase
        dual: Number
    name: GnrcPrntParamPrntDual
    parent: !_DualBase
      dual: RefGnrcPrntParamPrntDual
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntParamPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntParamPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltGnrcPrntParamPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltGnrcPrntParamPrntInp
      type: !_InputBase
        input: Number
    name: GnrcPrntParamPrntInp
    parent: !_InputBase
      input: RefGnrcPrntParamPrntInp
      typeArgs:
      - !_InputArg
        input: AltGnrcPrntParamPrntInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntParamPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltGnrcPrntParamPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltGnrcPrntParamPrntOutp
      type: !_OutputBase
        output: Number
    name: GnrcPrntParamPrntOutp
    parent: !_OutputBase
      output: RefGnrcPrntParamPrntOutp
      typeArgs:
      - !_OutputArg
        output: AltGnrcPrntParamPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntSmplEnumDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntSmplEnumDual
      type: !_DualBase
        dual: EnumGnrcPrntSmplEnumDual
    name: GnrcPrntSmplEnumDual
    parent: !_DualBase
      dual: FieldGnrcPrntSmplEnumDual
      typeArgs:
      - !_DualArg
        dual: EnumGnrcPrntSmplEnumDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntSmplEnumInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntSmplEnumInp
      type: !_InputBase
        input: EnumGnrcPrntSmplEnumInp
    name: GnrcPrntSmplEnumInp
    parent: !_InputBase
      input: FieldGnrcPrntSmplEnumInp
      typeArgs:
      - !_InputArg
        input: EnumGnrcPrntSmplEnumInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntSmplEnumOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntSmplEnumOutp
      type: !_OutputBase
        output: EnumGnrcPrntSmplEnumOutp
    name: GnrcPrntSmplEnumOutp
    parent: !_OutputBase
      output: FieldGnrcPrntSmplEnumOutp
      typeArgs:
      - !_OutputArg
        output: EnumGnrcPrntSmplEnumOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntStrDomDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntStrDomDual
      type: !_DualBase
        dual: DomGnrcPrntStrDomDual
    name: GnrcPrntStrDomDual
    parent: !_DualBase
      dual: FieldGnrcPrntStrDomDual
      typeArgs:
      - !_DualArg
        dual: DomGnrcPrntStrDomDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntStrDomInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntStrDomInp
      type: !_InputBase
        input: DomGnrcPrntStrDomInp
    name: GnrcPrntStrDomInp
    parent: !_InputBase
      input: FieldGnrcPrntStrDomInp
      typeArgs:
      - !_InputArg
        input: DomGnrcPrntStrDomInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntStrDomOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntStrDomOutp
      type: !_OutputBase
        output: DomGnrcPrntStrDomOutp
    name: GnrcPrntStrDomOutp
    parent: !_OutputBase
      output: FieldGnrcPrntStrDomOutp
      typeArgs:
      - !_OutputArg
        output: DomGnrcPrntStrDomOutp
    typeKind: !_TypeKind Output
  !_Identifier InOutpDescrParam: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpDescrParam
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpDescrParam
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpDescrParam
    typeKind: !_TypeKind Input
  !_Identifier InOutpParam: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpParam
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpParam
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpParam
    typeKind: !_TypeKind Input
  !_Identifier InOutpParamDescr: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpParamDescr
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpParamDescr
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpParamDescr
    typeKind: !_TypeKind Input
  !_Identifier InOutpParamModDmn: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpParamModDmn
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpParamModDmn
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpParamModDmn
    typeKind: !_TypeKind Input
  !_Identifier InOutpParamModParam: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpParamModParam
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpParamModParam
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpParamModParam
    typeKind: !_TypeKind Input
  !_Identifier InOutpParamTypeDescr: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpParamTypeDescr
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpParamTypeDescr
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpParamTypeDescr
    typeKind: !_TypeKind Input
  !_Identifier InOutpPrntParam: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpPrntParam
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpPrntParam
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpPrntParam
    typeKind: !_TypeKind Input
  !_Identifier InpFieldDescrNmbr: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: 42
      description: 'Test Descr'
      name: field
      object: InpFieldDescrNmbr
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      default: 42
      description: 'Test Descr'
      name: field
      type: !_InputBase
        input: Number
    name: InpFieldDescrNmbr
    typeKind: !_TypeKind Input
  !_Identifier InpFieldEnum: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: inpFieldEnum
      name: field
      object: InpFieldEnum
      type: !_InputBase
        input: EnumInpFieldEnum
    fields:
    - !_InputField
      default: inpFieldEnum
      name: field
      type: !_InputBase
        input: EnumInpFieldEnum
    name: InpFieldEnum
    typeKind: !_TypeKind Input
  !_Identifier InpFieldNmbr: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: 42
      name: field
      object: InpFieldNmbr
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      default: 42
      name: field
      type: !_InputBase
        input: Number
    name: InpFieldNmbr
    typeKind: !_TypeKind Input
  !_Identifier InpFieldNmbrDescr: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: 42
      name: field
      object: InpFieldNmbrDescr
      type: !_InputBase
        description: 'Test Descr'
        input: Number
    fields:
    - !_InputField
      default: 42
      name: field
      type: !_InputBase
        description: 'Test Descr'
        input: Number
    name: InpFieldNmbrDescr
    typeKind: !_TypeKind Input
  !_Identifier InpFieldNull: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: !Null null
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: field
      object: InpFieldNull
      type: !_DualBase
        dual: FldInpFieldNull
    fields:
    - !_InputField
      default: !Null null
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: field
      type: !_DualBase
        dual: FldInpFieldNull
    name: InpFieldNull
    typeKind: !_TypeKind Input
  !_Identifier InpFieldStr: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: 'default'
      name: field
      object: InpFieldStr
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      default: 'default'
      name: field
      type: !_InputBase
        input: String
    name: InpFieldStr
    typeKind: !_TypeKind Input
  !_Identifier JustOutpCnstDomEnum: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: JustOutpCnstDomEnum
      exclude: false
      value: !_EnumValue
        label: outpCnstDomEnum
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpCnstDomEnum
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: outpCnstDomEnum
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpCnstDomEnum
    name: JustOutpCnstDomEnum
    typeKind: !_TypeKind Domain
  !_Identifier ObjDualAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: ObjDualAltDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: ObjDualAltDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: ObjDualAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjDualAltDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: ObjDualAltDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: ObjDualAltDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: ObjDualAltDualInp
    typeKind: !_TypeKind Dual
  !_Identifier ObjDualAltDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: ObjDualAltDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: ObjDualAltDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: ObjDualAltDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier OutpCnstDomEnum: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpCnstDomEnum
      type: !_OutputBase
        output: RefOutpCnstDomEnum
        typeArgs:
        - !_OutputArg
          output: outpCnstDomEnum
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpCnstDomEnum
        typeArgs:
        - !_OutputArg
          output: outpCnstDomEnum
    name: OutpCnstDomEnum
    typeKind: !_TypeKind Output
  !_Identifier OutpCnstEnum: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpCnstEnum
      type: !_OutputBase
        output: RefOutpCnstEnum
        typeArgs:
        - !_OutputArg
          output: outpCnstEnum
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpCnstEnum
        typeArgs:
        - !_OutputArg
          output: outpCnstEnum
    name: OutpCnstEnum
    typeKind: !_TypeKind Output
  !_Identifier OutpCnstEnumPrnt: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpCnstEnumPrnt
      type: !_OutputBase
        output: RefOutpCnstEnumPrnt
        typeArgs:
        - !_OutputArg
          output: outpCnstEnumPrnt
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpCnstEnumPrnt
        typeArgs:
        - !_OutputArg
          output: outpCnstEnumPrnt
    name: OutpCnstEnumPrnt
    typeKind: !_TypeKind Output
  !_Identifier OutpCnstPrntEnum: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpCnstPrntEnum
      type: !_OutputBase
        output: RefOutpCnstPrntEnum
        typeArgs:
        - !_OutputArg
          output: parentOutpCnstPrntEnum
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpCnstPrntEnum
        typeArgs:
        - !_OutputArg
          output: parentOutpCnstPrntEnum
    name: OutpCnstPrntEnum
    typeKind: !_TypeKind Output
  !_Identifier OutpDescrParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      description: 'Test Descr'
      name: field
      object: OutpDescrParam
      parameters:
      - !_InputParam
        input: InOutpDescrParam
      type: !_DualBase
        dual: FldOutpDescrParam
    fields:
    - !_OutputField
      description: 'Test Descr'
      name: field
      parameters:
      - !_InputParam
        input: InOutpDescrParam
      type: !_DualBase
        dual: FldOutpDescrParam
    name: OutpDescrParam
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      field: field
      label: outpFieldEnum
      object: OutpFieldEnum
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldEnum
    fields:
    - !_OutputEnum
      field: field
      label: outpFieldEnum
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldEnum
    name: OutpFieldEnum
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldEnumPrnt: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      field: field
      label: prnt_outpFieldEnumPrnt
      object: OutpFieldEnumPrnt
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldEnumPrnt
    fields:
    - !_OutputEnum
      field: field
      label: prnt_outpFieldEnumPrnt
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldEnumPrnt
    name: OutpFieldEnumPrnt
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldValue: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      field: field
      label: outpFieldValue
      object: OutpFieldValue
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldValue
    fields:
    - !_OutputEnum
      field: field
      label: outpFieldValue
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldValue
    name: OutpFieldValue
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldValueDescr: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      description: 'Test Descr'
      field: field
      label: outpFieldValueDescr
      object: OutpFieldValueDescr
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldValueDescr
    fields:
    - !_OutputEnum
      description: 'Test Descr'
      field: field
      label: outpFieldValueDescr
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldValueDescr
    name: OutpFieldValueDescr
    typeKind: !_TypeKind Output
  !_Identifier OutpGnrcEnum: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpGnrcEnum
      type: !_OutputBase
        output: RefOutpGnrcEnum
        typeArgs:
        - !_OutputArg
          label: outpGnrcEnum
          typeKind: !_SimpleKind Enum
          typeName: EnumOutpGnrcEnum
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpGnrcEnum
        typeArgs:
        - !_OutputArg
          label: outpGnrcEnum
          typeKind: !_SimpleKind Enum
          typeName: EnumOutpGnrcEnum
    name: OutpGnrcEnum
    typeKind: !_TypeKind Output
  !_Identifier OutpGnrcValue: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpGnrcValue
      type: !_OutputBase
        output: RefOutpGnrcValue
        typeArgs:
        - !_OutputArg
          output: outpGnrcValue
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpGnrcValue
        typeArgs:
        - !_OutputArg
          output: outpGnrcValue
    name: OutpGnrcValue
    typeKind: !_TypeKind Output
  !_Identifier OutpParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpParam
      parameters:
      - !_InputParam
        input: InOutpParam
      type: !_DualBase
        dual: FldOutpParam
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: InOutpParam
      type: !_DualBase
        dual: FldOutpParam
    name: OutpParam
    typeKind: !_TypeKind Output
  !_Identifier OutpParamDescr: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpParamDescr
      parameters:
      - !_InputParam
        description: 'Test Descr'
        input: InOutpParamDescr
      type: !_DualBase
        dual: FldOutpParamDescr
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        description: 'Test Descr'
        input: InOutpParamDescr
      type: !_DualBase
        dual: FldOutpParamDescr
    name: OutpParamDescr
    typeKind: !_TypeKind Output
  !_Identifier OutpParamModDmn: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpParamModDmn
      parameters:
      - !_InputParam
        input: InOutpParamModDmn
        modifiers: [!_ModifierDictionary {by: DomOutpParamModDmn,
            modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Domain}]
      type: !_OutputBase
        output: DomOutpParamModDmn
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: InOutpParamModDmn
        modifiers: [!_ModifierDictionary {by: DomOutpParamModDmn,
            modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Domain}]
      type: !_OutputBase
        output: DomOutpParamModDmn
    name: OutpParamModDmn
    typeKind: !_TypeKind Output
  !_Identifier OutpParamModParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpParamModParam
      parameters:
      - !_InputParam
        input: InOutpParamModParam
        modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      type: !_OutputBase
        output: DomOutpParamModParam
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: InOutpParamModParam
        modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      type: !_OutputBase
        output: DomOutpParamModParam
    name: OutpParamModParam
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier OutpParamTypeDescr: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpParamTypeDescr
      parameters:
      - !_InputParam
        input: InOutpParamTypeDescr
      type: !_DualBase
        description: 'Test Descr'
        dual: FldOutpParamTypeDescr
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: InOutpParamTypeDescr
      type: !_DualBase
        description: 'Test Descr'
        dual: FldOutpParamTypeDescr
    name: OutpParamTypeDescr
    typeKind: !_TypeKind Output
  !_Identifier OutpPrntGnrc: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpPrntGnrc
      type: !_OutputBase
        output: RefOutpPrntGnrc
        typeArgs:
        - !_OutputArg
          label: prnt_outpPrntGnrc
          typeKind: !_SimpleKind Enum
          typeName: EnumOutpPrntGnrc
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpPrntGnrc
        typeArgs:
        - !_OutputArg
          label: prnt_outpPrntGnrc
          typeKind: !_SimpleKind Enum
          typeName: EnumOutpPrntGnrc
    name: OutpPrntGnrc
    typeKind: !_TypeKind Output
  !_Identifier OutpPrntParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: PrntOutpPrntParam
      parameters:
      - !_InputParam
        input: PrntOutpPrntParamIn
      type: !_DualBase
        dual: FldOutpPrntParam
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpPrntParam
      parameters:
      - !_InputParam
        input: InOutpPrntParam
      type: !_DualBase
        dual: FldOutpPrntParam
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: InOutpPrntParam
      type: !_DualBase
        dual: FldOutpPrntParam
    name: OutpPrntParam
    parent: !_OutputBase
      output: PrntOutpPrntParam
    typeKind: !_TypeKind Output
  !_Identifier ParentGnrcPrntEnumChildDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildDual
      name: gnrcPrntEnumChildDualParent
    items:
    - !_Aliased
      name: gnrcPrntEnumChildDualParent
    name: ParentGnrcPrntEnumChildDual
    typeKind: !_TypeKind Enum
  !_Identifier ParentGnrcPrntEnumChildInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildInp
      name: gnrcPrntEnumChildInpParent
    items:
    - !_Aliased
      name: gnrcPrntEnumChildInpParent
    name: ParentGnrcPrntEnumChildInp
    typeKind: !_TypeKind Enum
  !_Identifier ParentGnrcPrntEnumChildOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildOutp
      name: gnrcPrntEnumChildOutpParent
    items:
    - !_Aliased
      name: gnrcPrntEnumChildOutpParent
    name: ParentGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Enum
  !_Identifier ParentGnrcPrntEnumPrntDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntDual
      name: gnrcPrntEnumPrntDualParent
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntDualParent
    name: ParentGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Enum
  !_Identifier ParentGnrcPrntEnumPrntInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntInp
      name: gnrcPrntEnumPrntInpParent
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntInpParent
    name: ParentGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Enum
  !_Identifier ParentGnrcPrntEnumPrntOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntOutp
      name: gnrcPrntEnumPrntOutpParent
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntOutpParent
    name: ParentGnrcPrntEnumPrntOutp
    typeKind: !_TypeKind Enum
  !_Identifier ParentOutpCnstEnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentOutpCnstEnumPrnt
      name: parentOutpCnstEnumPrnt
    items:
    - !_Aliased
      name: parentOutpCnstEnumPrnt
    name: ParentOutpCnstEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier ParentOutpCnstPrntEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentOutpCnstPrntEnum
      name: parentOutpCnstPrntEnum
    items:
    - !_Aliased
      name: parentOutpCnstPrntEnum
    name: ParentOutpCnstPrntEnum
    typeKind: !_TypeKind Enum
  !_Identifier PrntAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntAltDual
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualAlternate)
      object: PrntAltDual
      type: !_DualBase
        dual: Number
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntAltDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: Number
    name: PrntAltDual
    parent: !_DualBase
      dual: RefPrntAltDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntAltInp
      type: !_InputBase
        input: String
    - !_ObjectFor(_InputAlternate)
      object: PrntAltInp
      type: !_InputBase
        input: Number
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntAltInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: Number
    name: PrntAltInp
    parent: !_InputBase
      input: RefPrntAltInp
    typeKind: !_TypeKind Input
  !_Identifier PrntAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntAltOutp
      type: !_OutputBase
        output: String
    - !_ObjectFor(_OutputAlternate)
      object: PrntAltOutp
      type: !_OutputBase
        output: Number
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntAltOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: Number
    name: PrntAltOutp
    parent: !_OutputBase
      output: RefPrntAltOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntCnstAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstAltDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualInp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstAltDualInp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstAltDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualOutp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstAltDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstAltObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltObjDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstAltObjDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstAltObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstAltObjInp
      type: !_InputBase
        input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    name: PrntCnstAltObjInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstAltObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstAltObjOutp
      type: !_OutputBase
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    name: PrntCnstAltObjOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntCnstFieldDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstFieldDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualInp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstFieldDualInp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstFieldDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualOutp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstFieldDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstFieldObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldObjDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstFieldObjDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstFieldObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstFieldObjInp
      type: !_InputBase
        input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    name: PrntCnstFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstFieldObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstFieldObjOutp
      type: !_OutputBase
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    name: PrntCnstFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntCnstPrntDualPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstPrntDualPrntInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntInp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstPrntDualPrntInp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstPrntDualPrntOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntOutp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstPrntDualPrntOutp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstPrntObjPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntObjPrntDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstPrntObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstPrntObjPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstPrntObjPrntInp
      type: !_InputBase
        input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    name: PrntCnstPrntObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstPrntObjPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstPrntObjPrntOutp
      type: !_OutputBase
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    name: PrntCnstPrntObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntDescrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDescrDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDescrDual
      type: !_DualBase
        dual: Number
    name: PrntDescrDual
    parent: !_DualBase
      description: 'Test Descr'
      dual: RefPrntDescrDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntDescrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntDescrInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntDescrInp
      type: !_InputBase
        input: Number
    name: PrntDescrInp
    parent: !_InputBase
      description: 'Test Descr'
      input: RefPrntDescrInp
    typeKind: !_TypeKind Input
  !_Identifier PrntDescrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntDescrOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntDescrOutp
      type: !_OutputBase
        output: Number
    name: PrntDescrOutp
    parent: !_OutputBase
      description: 'Test Descr'
      output: RefPrntDescrOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDual
      type: !_DualBase
        dual: Number
    name: PrntDual
    parent: !_DualBase
      dual: RefPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualDual
      type: !_DualBase
        dual: Number
    name: PrntDualDual
    parent: !_DualBase
      dual: RefPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualInp
      type: !_DualBase
        dual: Number
    name: PrntDualInp
    parent: !_DualBase
      dual: RefPrntDualInp
    typeKind: !_TypeKind Input
  !_Identifier PrntDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualOutp
      type: !_DualBase
        dual: Number
    name: PrntDualOutp
    parent: !_DualBase
      dual: RefPrntDualOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntFieldDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntFieldDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntFieldDual
      type: !_DualBase
        dual: Number
    - !_ObjectFor(_DualField)
      name: field
      object: PrntFieldDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: PrntFieldDual
    parent: !_DualBase
      dual: RefPrntFieldDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntFieldInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntFieldInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntFieldInp
      type: !_InputBase
        input: Number
    - !_ObjectFor(_InputField)
      name: field
      object: PrntFieldInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: Number
    name: PrntFieldInp
    parent: !_InputBase
      input: RefPrntFieldInp
    typeKind: !_TypeKind Input
  !_Identifier PrntFieldOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntFieldOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntFieldOutp
      type: !_OutputBase
        output: Number
    - !_ObjectFor(_OutputField)
      name: field
      object: PrntFieldOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: Number
    name: PrntFieldOutp
    parent: !_OutputBase
      output: RefPrntFieldOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntInp
      type: !_InputBase
        input: Number
    name: PrntInp
    parent: !_InputBase
      input: RefPrntInp
    typeKind: !_TypeKind Input
  !_Identifier PrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntOutp
      type: !_OutputBase
        output: Number
    name: PrntOutp
    parent: !_OutputBase
      output: RefPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntOutpFieldEnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntOutpFieldEnumPrnt
      name: prnt_outpFieldEnumPrnt
    items:
    - !_Aliased
      name: prnt_outpFieldEnumPrnt
    name: PrntOutpFieldEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntOutpPrntGnrc: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntOutpPrntGnrc
      name: prnt_outpPrntGnrc
    items:
    - !_Aliased
      name: prnt_outpPrntGnrc
    name: PrntOutpPrntGnrc
    typeKind: !_TypeKind Enum
  !_Identifier PrntOutpPrntParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: PrntOutpPrntParam
      parameters:
      - !_InputParam
        input: PrntOutpPrntParamIn
      type: !_DualBase
        dual: FldOutpPrntParam
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: PrntOutpPrntParamIn
      type: !_DualBase
        dual: FldOutpPrntParam
    name: PrntOutpPrntParam
    typeKind: !_TypeKind Output
  !_Identifier PrntOutpPrntParamIn: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntOutpPrntParamIn
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: PrntOutpPrntParamIn
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: parent
      type: !_InputBase
        input: Number
    name: PrntOutpPrntParamIn
    typeKind: !_TypeKind Input
  !_Identifier PrntParamDiffDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntParamDiffDual
      type: !_DualBase
        typeParam: b
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: PrntParamDiffDual
      type: !_DualBase
        typeParam: a
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: a
    name: PrntParamDiffDual
    parent: !_DualBase
      dual: RefPrntParamDiffDual
      typeArgs:
      - !_DualArg
        typeParam: a
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntParamDiffInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntParamDiffInp
      type: !_InputBase
        typeParam: b
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: PrntParamDiffInp
      type: !_InputBase
        typeParam: a
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: a
    name: PrntParamDiffInp
    parent: !_InputBase
      input: RefPrntParamDiffInp
      typeArgs:
      - !_InputArg
        typeParam: a
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntParamDiffOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntParamDiffOutp
      type: !_OutputBase
        typeParam: b
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: PrntParamDiffOutp
      type: !_OutputBase
        typeParam: a
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: a
    name: PrntParamDiffOutp
    parent: !_OutputBase
      output: RefPrntParamDiffOutp
      typeArgs:
      - !_OutputArg
        typeParam: a
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntParamSameDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntParamSameDual
      type: !_DualBase
        typeParam: a
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: PrntParamSameDual
      type: !_DualBase
        typeParam: a
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: a
    name: PrntParamSameDual
    parent: !_DualBase
      dual: RefPrntParamSameDual
      typeArgs:
      - !_DualArg
        typeParam: a
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntParamSameInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntParamSameInp
      type: !_InputBase
        typeParam: a
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: PrntParamSameInp
      type: !_InputBase
        typeParam: a
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: a
    name: PrntParamSameInp
    parent: !_InputBase
      input: RefPrntParamSameInp
      typeArgs:
      - !_InputArg
        typeParam: a
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntParamSameOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntParamSameOutp
      type: !_OutputBase
        typeParam: a
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: PrntParamSameOutp
      type: !_OutputBase
        typeParam: a
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: a
    name: PrntParamSameOutp
    parent: !_OutputBase
      output: RefPrntParamSameOutp
      typeArgs:
      - !_OutputArg
        typeParam: a
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier RefCnstAltDmnDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefCnstAltDmnDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefCnstAltDmnDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstAltDmnInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefCnstAltDmnInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefCnstAltDmnInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstAltDmnOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefCnstAltDmnOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefCnstAltDmnOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefCnstAltDualDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefCnstAltDualDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstAltDualDual
      name: ref
  !_Identifier RefCnstAltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefCnstAltDualInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefCnstAltDualInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstAltDualInp
      name: ref
  !_Identifier RefCnstAltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefCnstAltDualOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefCnstAltDualOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstAltDualOutp
      name: ref
  !_Identifier RefCnstAltObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefCnstAltObjDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefCnstAltObjDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstAltObjDual
      name: ref
  !_Identifier RefCnstAltObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefCnstAltObjInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefCnstAltObjInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Input
        typeName: PrntCnstAltObjInp
      name: ref
  !_Identifier RefCnstAltObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefCnstAltObjOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefCnstAltObjOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: PrntCnstAltObjOutp
      name: ref
  !_Identifier RefCnstFieldDmnDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldDmnDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: RefCnstFieldDmnDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstFieldDmnInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldDmnInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: RefCnstFieldDmnInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstFieldDmnOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldDmnOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: RefCnstFieldDmnOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstFieldDualDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldDualDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: RefCnstFieldDualDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstFieldDualDual
      name: ref
  !_Identifier RefCnstFieldDualInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldDualInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: RefCnstFieldDualInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstFieldDualInp
      name: ref
  !_Identifier RefCnstFieldDualOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldDualOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: RefCnstFieldDualOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstFieldDualOutp
      name: ref
  !_Identifier RefCnstFieldObjDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldObjDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: RefCnstFieldObjDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstFieldObjDual
      name: ref
  !_Identifier RefCnstFieldObjInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldObjInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: RefCnstFieldObjInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Input
        typeName: PrntCnstFieldObjInp
      name: ref
  !_Identifier RefCnstFieldObjOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldObjOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: RefCnstFieldObjOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: PrntCnstFieldObjOutp
      name: ref
  !_Identifier RefCnstPrntDualPrntDual: !_TypeDual
    name: RefCnstPrntDualPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstPrntDualPrntDual
      name: ref
  !_Identifier RefCnstPrntDualPrntInp: !_TypeInput
    name: RefCnstPrntDualPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstPrntDualPrntInp
      name: ref
  !_Identifier RefCnstPrntDualPrntOutp: !_TypeOutput
    name: RefCnstPrntDualPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstPrntDualPrntOutp
      name: ref
  !_Identifier RefCnstPrntObjPrntDual: !_TypeDual
    name: RefCnstPrntObjPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstPrntObjPrntDual
      name: ref
  !_Identifier RefCnstPrntObjPrntInp: !_TypeInput
    name: RefCnstPrntObjPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Input
        typeName: PrntCnstPrntObjPrntInp
      name: ref
  !_Identifier RefCnstPrntObjPrntOutp: !_TypeOutput
    name: RefCnstPrntObjPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: PrntCnstPrntObjPrntOutp
      name: ref
  !_Identifier RefGnrcAltArgDescrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcAltArgDescrDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltArgDescrDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltArgDescrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcAltArgDescrInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltArgDescrInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltArgDescrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcAltArgDescrOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltArgDescrOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltArgDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcAltArgDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltArgDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltArgInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcAltArgInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltArgInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltArgOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcAltArgOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltArgOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcAltDualDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltDualDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcAltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcAltDualInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltDualInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcAltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcAltDualOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltDualOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcAltModParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: RefGnrcAltModParamDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltModParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier RefGnrcAltModParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: RefGnrcAltModParamInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltModParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier RefGnrcAltModParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: RefGnrcAltModParamOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltModParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier RefGnrcAltModStrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: RefGnrcAltModStrDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltModStrDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltModStrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: RefGnrcAltModStrInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltModStrInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltModStrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: RefGnrcAltModStrOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltModStrOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcAltParamDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcAltParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcAltParamInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Input
      name: ref
  !_Identifier RefGnrcAltParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcAltParamOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Output
      name: ref
  !_Identifier RefGnrcAltSmplDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcAltSmplDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltSmplDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltSmplInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcAltSmplInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltSmplInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltSmplOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcAltSmplOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltSmplOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcFieldArgDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcFieldArgDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcFieldArgDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcFieldArgInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcFieldArgInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcFieldArgInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcFieldArgOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcFieldArgOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcFieldArgOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcFieldDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcFieldDualDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcFieldDualDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcFieldDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcFieldDualInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcFieldDualInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcFieldDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcFieldDualOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcFieldDualOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcFieldParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcFieldParamDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcFieldParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcFieldParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcFieldParamInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcFieldParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Input
      name: ref
  !_Identifier RefGnrcFieldParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcFieldParamOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcFieldParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Output
      name: ref
  !_Identifier RefGnrcPrntArgDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntArgDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcPrntArgDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcPrntArgInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntArgInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcPrntArgInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcPrntArgOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntArgOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcPrntArgOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcPrntDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntDualDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcPrntDualDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntDualInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcPrntDualInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntDualOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcPrntDualOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntDualPrntDual: !_TypeDual
    name: RefGnrcPrntDualPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntDualPrntInp: !_TypeInput
    name: RefGnrcPrntDualPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntDualPrntOutp: !_TypeOutput
    name: RefGnrcPrntDualPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntParamDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcPrntParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntParamInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcPrntParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Input
      name: ref
  !_Identifier RefGnrcPrntParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntParamOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcPrntParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Output
      name: ref
  !_Identifier RefGnrcPrntParamPrntDual: !_TypeDual
    name: RefGnrcPrntParamPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntParamPrntInp: !_TypeInput
    name: RefGnrcPrntParamPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Input
      name: ref
  !_Identifier RefGnrcPrntParamPrntOutp: !_TypeOutput
    name: RefGnrcPrntParamPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Output
      name: ref
  !_Identifier RefOutpCnstDomEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpCnstDomEnum
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpCnstDomEnum
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Domain
        typeName: JustOutpCnstDomEnum
      name: type
  !_Identifier RefOutpCnstEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpCnstEnum
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpCnstEnum
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumOutpCnstEnum
      name: type
  !_Identifier RefOutpCnstEnumPrnt: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpCnstEnumPrnt
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpCnstEnumPrnt
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: ParentOutpCnstEnumPrnt
      name: type
  !_Identifier RefOutpCnstPrntEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpCnstPrntEnum
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpCnstPrntEnum
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumOutpCnstPrntEnum
      name: type
  !_Identifier RefOutpGnrcEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpGnrcEnum
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpGnrcEnum
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Enum
      name: type
  !_Identifier RefOutpGnrcValue: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpGnrcValue
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpGnrcValue
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Enum
      name: type
  !_Identifier RefOutpPrntGnrc: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpPrntGnrc
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpPrntGnrc
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: PrntOutpPrntGnrc
      name: type
  !_Identifier RefPrntAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntAltDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntAltDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntAltDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntAltInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntAltInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: parent
      type: !_InputBase
        input: Number
    name: RefPrntAltInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntAltOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntAltOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: parent
      type: !_OutputBase
        output: Number
    name: RefPrntAltOutp
    typeKind: !_TypeKind Output
  !_Identifier RefPrntDescrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDescrDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDescrDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntDescrDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntDescrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntDescrInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntDescrInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: parent
      type: !_InputBase
        input: Number
    name: RefPrntDescrInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntDescrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntDescrOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntDescrOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: parent
      type: !_OutputBase
        output: Number
    name: RefPrntDescrOutp
    typeKind: !_TypeKind Output
  !_Identifier RefPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntDualInp
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntFieldDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntFieldDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntFieldDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntFieldDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntFieldInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntFieldInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntFieldInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: parent
      type: !_InputBase
        input: Number
    name: RefPrntFieldInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntFieldOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntFieldOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntFieldOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: parent
      type: !_OutputBase
        output: Number
    name: RefPrntFieldOutp
    typeKind: !_TypeKind Output
  !_Identifier RefPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: parent
      type: !_InputBase
        input: Number
    name: RefPrntInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: parent
      type: !_OutputBase
        output: Number
    name: RefPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier RefPrntParamDiffDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntParamDiffDual
      type: !_DualBase
        typeParam: b
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: b
    name: RefPrntParamDiffDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: b
  !_Identifier RefPrntParamDiffInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntParamDiffInp
      type: !_InputBase
        typeParam: b
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: b
    name: RefPrntParamDiffInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: b
  !_Identifier RefPrntParamDiffOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntParamDiffOutp
      type: !_OutputBase
        typeParam: b
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: b
    name: RefPrntParamDiffOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: b
  !_Identifier RefPrntParamSameDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntParamSameDual
      type: !_DualBase
        typeParam: a
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: a
    name: RefPrntParamSameDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier RefPrntParamSameInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntParamSameInp
      type: !_InputBase
        typeParam: a
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: a
    name: RefPrntParamSameInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier RefPrntParamSameOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntParamSameOutp
      type: !_OutputBase
        typeParam: a
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: a
    name: RefPrntParamSameOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a