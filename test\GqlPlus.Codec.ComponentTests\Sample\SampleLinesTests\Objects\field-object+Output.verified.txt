﻿!_Schema
types: !_Map_Type
  !_Identifier FieldObjOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: FieldObjOutp
        type: !_OutputBase
          output: FldFieldObjOutp
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          output: FldFieldObjOutp
    name: FieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier FldFieldObjOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: FldFieldObjOutp
        type: !_OutputBase
          output: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: FldFieldObjOutp
        type: !_OutputBase
          output: Number
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: String
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          output: Number
    name: FldFieldObjOutp
    typeKind: !_TypeKind Output