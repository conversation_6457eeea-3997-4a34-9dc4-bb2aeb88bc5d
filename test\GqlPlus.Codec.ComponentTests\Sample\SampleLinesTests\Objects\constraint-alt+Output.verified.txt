﻿!_Schema
types: !_Map_Type
  !_Identifier CnstAltOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: CnstAltOutp
        type: !_OutputBase
          typeParam: type
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: type
    name: CnstAltOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: Number
        name: type