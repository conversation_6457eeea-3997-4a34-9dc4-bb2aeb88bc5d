﻿!_Schema
errors:
  - !_Error
    _kind: !_TokenKind End
    _message: In _Directive can't get model for type '_Aliased'
types: !_Map_Type
  !_Identifier _Directive: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: parameters
        object: _Directive
        type: !_OutputBase
          output: _InputParam
      - !_ObjectFor(_OutputField)
        name: repeatable
        object: _Directive
        type: !_OutputBase
          output: Boolean
      - !_ObjectFor(_OutputField)
        modifiers: [!_ModifierDictionary{by:_Location,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Enum}]
        name: locations
        object: _Directive
        type: !_OutputBase
          output: _
    fields:
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: parameters
        type: !_OutputBase
          output: _InputParam
      - !_OutputField
        name: repeatable
        type: !_OutputBase
          output: Boolean
      - !_OutputField
        modifiers: [!_ModifierDictionary{by:_Location,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Enum}]
        name: locations
        type: !_OutputBase
          output: _
    name: _Directive
    parent: !_OutputBase
      output: _Aliased
    typeKind: !_TypeKind Output
  !_Identifier _Directives: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _Directives
        type: !_OutputBase
          output: _Directive
      - !_ObjectFor(_OutputAlternate)
        object: _Directives
        type: !_OutputBase
          output: _Type
    allFields:
      - !_ObjectFor(_OutputField)
        name: directive
        object: _Directives
        type: !_OutputBase
          output: _Directive
      - !_ObjectFor(_OutputField)
        name: type
        object: _Directives
        type: !_OutputBase
          output: _Type
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _Directive
      - !_OutputAlternate
        type: !_OutputBase
          output: _Type
    fields:
      - !_OutputField
        name: directive
        type: !_OutputBase
          output: _Directive
      - !_OutputField
        name: type
        type: !_OutputBase
          output: _Type
    name: _Directives
    typeKind: !_TypeKind Output
  !_Identifier _Location: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: _Location
        name: Operation
      - !_EnumLabel
        enum: _Location
        name: Variable
      - !_EnumLabel
        enum: _Location
        name: Field
      - !_EnumLabel
        enum: _Location
        name: Inline
      - !_EnumLabel
        enum: _Location
        name: Spread
      - !_EnumLabel
        enum: _Location
        name: Fragment
    items:
      - !_Aliased
        name: Operation
      - !_Aliased
        name: Variable
      - !_Aliased
        name: Field
      - !_Aliased
        name: Inline
      - !_Aliased
        name: Spread
      - !_Aliased
        name: Fragment
    name: _Location
    typeKind: !_TypeKind Enum