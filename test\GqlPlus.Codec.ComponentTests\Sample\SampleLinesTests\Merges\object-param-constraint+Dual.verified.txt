﻿!_Schema
types: !_Map_Type
  !_Identifier ObjParamCnstDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: test
        object: ObjParamCnstDual
        type: !_DualBase
          typeParam: test
      - !_ObjectFor(_DualField)
        name: type
        object: ObjParamCnstDual
        type: !_DualBase
          typeParam: test
    fields:
      - !_DualField
        name: test
        type: !_DualBase
          typeParam: test
      - !_DualField
        name: type
        type: !_DualBase
          typeParam: test
    name: ObjParamCnstDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: test