﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcFieldArgInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: GnrcFieldArgInp
        type: !_InputBase
          input: RefGnrcFieldArgInp
          typeArgs:
            - !_InputArg
              typeParam: type
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          input: RefGnrcFieldArgInp
          typeArgs:
            - !_InputArg
              typeParam: type
    name: GnrcFieldArgInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type
  !_Identifier RefGnrcFieldArgInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefGnrcFieldArgInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefGnrcFieldArgInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref