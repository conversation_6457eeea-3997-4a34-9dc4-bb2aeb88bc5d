﻿!_Schema
types: !_Map_Type
  !_Identifier EnumGnrcPrntSmplEnumDual: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumGnrcPrntSmplEnumDual
        name: gnrcPrntSmplEnumDual
    items:
      - !_Aliased
        name: gnrcPrntSmplEnumDual
    name: EnumGnrcPrntSmplEnumDual
    typeKind: !_TypeKind Enum
  !_Identifier FieldGnrcPrntSmplEnumDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FieldGnrcPrntSmplEnumDual
        type: !_DualBase
          typeParam: ref
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          typeParam: ref
    name: FieldGnrcPrntSmplEnumDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Simple
        name: ref
  !_Identifier GnrcPrntSmplEnumDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FieldGnrcPrntSmplEnumDual
        type: !_DualBase
          dual: EnumGnrcPrntSmplEnumDual
    name: GnrcPrntSmplEnumDual
    parent: !_DualBase
      dual: FieldGnrcPrntSmplEnumDual
      typeArgs:
        - !_DualArg
          dual: EnumGnrcPrntSmplEnumDual
    typeKind: !_TypeKind Dual