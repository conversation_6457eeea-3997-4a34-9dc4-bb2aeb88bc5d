﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstPrntObjPrntOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: PrntCnstPrntObjPrntOutp
        type: !_OutputBase
          output: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: alt
        object: AltCnstPrntObjPrntOutp
        type: !_OutputBase
          output: Number
    fields:
      - !_OutputField
        name: alt
        type: !_OutputBase
          output: Number
    name: AltCnstPrntObjPrntOutp
    parent: !_OutputBase
      output: PrntCnstPrntObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstPrntObjPrntOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: PrntCnstPrntObjPrntOutp
        type: !_OutputBase
          output: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: alt
        object: AltCnstPrntObjPrntOutp
        type: !_OutputBase
          output: Number
    name: CnstPrntObjPrntOutp
    parent: !_OutputBase
      output: RefCnstPrntObjPrntOutp
      typeArgs:
        - !_OutputArg
          output: AltCnstPrntObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntCnstPrntObjPrntOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: PrntCnstPrntObjPrntOutp
        type: !_OutputBase
          output: String
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: String
    name: PrntCnstPrntObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier RefCnstPrntObjPrntOutp: !_TypeOutput
    name: RefCnstPrntObjPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: PrntCnstPrntObjPrntOutp
        name: ref