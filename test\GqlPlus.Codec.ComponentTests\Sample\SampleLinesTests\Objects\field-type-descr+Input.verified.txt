﻿!_Schema
types: !_Map_Type
  !_Identifier FieldTypeDescrInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: FieldTypeDescrInp
        type: !_InputBase
          description: 'Test Descr'
          input: Number
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          description: 'Test Descr'
          input: Number
    name: FieldTypeDescrInp
    typeKind: !_TypeKind Input