﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstPrntDualPrntOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstPrntDualPrntOutp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: alt
        object: AltCnstPrntDualPrntOutp
        type: !_OutputBase
          output: Number
    fields:
      - !_OutputField
        name: alt
        type: !_OutputBase
          output: Number
    name: AltCnstPrntDualPrntOutp
    parent: !_DualBase
      dual: PrntCnstPrntDualPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstPrntDualPrntOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstPrntDualPrntOutp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: alt
        object: AltCnstPrntDualPrntOutp
        type: !_OutputBase
          output: Number
    name: CnstPrntDualPrntOutp
    parent: !_OutputBase
      output: RefCnstPrntDualPrntOutp
      typeArgs:
        - !_OutputArg
          output: AltCnstPrntDualPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntCnstPrntDualPrntOutp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstPrntDualPrntOutp
        type: !_DualBase
          dual: String
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    name: PrntCnstPrntDualPrntOutp
    typeKind: !_TypeKind Dual
  !_Identifier RefCnstPrntDualPrntOutp: !_TypeOutput
    name: RefCnstPrntDualPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Dual
          typeName: PrntCnstPrntDualPrntOutp
        name: ref