﻿!_Schema
types: !_Map_Type
  !_Identifier CnstFieldDmnDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: RefCnstFieldDmnDual
        type: !_DualBase
          dual: DomCnstFieldDmnDual
    name: CnstFieldDmnDual
    parent: !_DualBase
      dual: RefCnstFieldDmnDual
      typeArgs:
        - !_DualArg
          dual: DomCnstFieldDmnDual
    typeKind: !_TypeKind Dual
  !_Identifier DomCnstFieldDmnDual: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: DomCnstFieldDmnDual
        exclude: false
        pattern: \w+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: \w+
    name: DomCnstFieldDmnDual
    typeKind: !_TypeKind Domain
  !_Identifier RefCnstFieldDmnDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: RefCnstFieldDmnDual
        type: !_DualBase
          typeParam: ref
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          typeParam: ref
    name: RefCnstFieldDmnDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref