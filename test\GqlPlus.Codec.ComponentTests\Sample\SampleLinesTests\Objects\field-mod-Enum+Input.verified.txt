﻿!_Schema
types: !_Map_Type
  !_Identifier EnumFieldModEnumInp: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumFieldModEnumInp
        name: value
    items:
      - !_Aliased
        name: value
    name: EnumFieldModEnumInp
    typeKind: !_TypeKind Enum
  !_Identifier FieldModEnumInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        modifiers: [!_ModifierDictionary{by:EnumFieldModEnumInp,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Enum}]
        name: field
        object: FieldModEnumInp
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        modifiers: [!_ModifierDictionary{by:EnumFieldModEnumInp,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Enum}]
        name: field
        type: !_InputBase
          input: String
    name: FieldModEnumInp
    typeKind: !_TypeKind Input