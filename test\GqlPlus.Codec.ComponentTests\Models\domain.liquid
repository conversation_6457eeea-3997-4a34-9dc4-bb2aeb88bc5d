﻿<table>
  <thead>
    <tr>
      <th>{{ domain.domainKind }}</th>
      <th>Excluded</th>
      <th class="from">Domain</th>
    </tr>
  </thead>
  {%- for item in domain.allItems %}
  <tr>
    <td>
      {%- case domain.domainKind -%}
      {%- when "Boolean" -%} {{ item.value }}
      {%- when "Enum" -%} {{ item.value.name }}.{{ item.value.label }}
      {%- when "Number" -%} {{ item.from }} ~ {{ item.to }}
      {%- when "String" -%} {{ item.pattern }}
      {%- endcase -%}
    </td>
    <td>{{ item.exclude }}</td>
    <td class="from">{{ item.domain }}</td>
  </tr>
  {%- endfor %}
</table>
