﻿!_Schema
types: !_Map_Type
  !_Identifier PrntAltDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefPrntAltDual
        type: !_DualBase
          dual: String
      - !_ObjectFor(_DualAlternate)
        object: PrntAltDual
        type: !_DualBase
          dual: Number
    allFields:
      - !_ObjectFor(_DualField)
        name: parent
        object: RefPrntAltDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: Number
    name: PrntAltDual
    parent: !_DualBase
      dual: RefPrntAltDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntAltDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefPrntAltDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: parent
        object: RefPrntAltDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: parent
        type: !_DualBase
          dual: Number
    name: RefPrntAltDual
    typeKind: !_TypeKind Dual