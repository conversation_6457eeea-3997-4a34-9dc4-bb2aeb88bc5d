﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcFieldDualInp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcFieldDualInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcFieldDualInp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcFieldDualInp
    typeKind: !_TypeKind Dual
  !_Identifier GnrcFieldDualInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: GnrcFieldDualInp
        type: !_InputBase
          input: RefGnrcFieldDualInp
          typeArgs:
            - !_DualArg
              dual: AltGnrcFieldDualInp
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          input: RefGnrcFieldDualInp
          typeArgs:
            - !_DualArg
              dual: AltGnrcFieldDualInp
    name: GnrcFieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier RefGnrcFieldDualInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefGnrcFieldDualInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefGnrcFieldDualInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref