﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstAltObjDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstAltObjDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltCnstAltObjDual
        type: !_DualBase
          dual: Number
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltCnstAltObjDual
    parent: !_DualBase
      dual: PrntCnstAltObjDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstAltObjDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: CnstAltObjDual
        type: !_DualBase
          dual: RefCnstAltObjDual
          typeArgs:
            - !_DualArg
              dual: AltCnstAltObjDual
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: RefCnstAltObjDual
          typeArgs:
            - !_DualArg
              dual: AltCnstAltObjDual
    name: CnstAltObjDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstAltObjDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstAltObjDual
        type: !_DualBase
          dual: String
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    name: PrntCnstAltObjDual
    typeKind: !_TypeKind Dual
  !_Identifier RefCnstAltObjDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefCnstAltObjDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefCnstAltObjDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Dual
          typeName: PrntCnstAltObjDual
        name: ref