﻿!_Schema
types: !_Map_Type
  !_Identifier EnumOutpCnstEnum: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumOutpCnstEnum
        name: outpCnstEnum
    items:
      - !_Aliased
        name: outpCnstEnum
    name: EnumOutpCnstEnum
    typeKind: !_TypeKind Enum
  !_Identifier OutpCnstEnum: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: OutpCnstEnum
        type: !_OutputBase
          output: RefOutpCnstEnum
          typeArgs:
            - !_OutputArg
              output: outpCnstEnum
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: RefOutpCnstEnum
          typeArgs:
            - !_OutputArg
              output: outpCnstEnum
    name: OutpCnstEnum
    typeKind: !_TypeKind Output
  !_Identifier RefOutpCnstEnum: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: RefOutpCnstEnum
        type: !_OutputBase
          typeParam: type
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: type
    name: RefOutpCnstEnum
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: EnumOutpCnstEnum
        name: type