﻿{
  "$tag": "_Schema",
  "types": {
    "$tag": "_Map_Type",
    "InpFieldStr": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": "default",
          "name": "field",
          "object": "InpFieldStr",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "default": "default",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "name": "InpFieldStr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    }
  }
}