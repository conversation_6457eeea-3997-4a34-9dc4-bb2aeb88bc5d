﻿!_Schema
types: !_Map_Type
  !_Identifier EnumGnrcPrntEnumChildInp: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentGnrcPrntEnumChildInp
        name: gnrcPrntEnumChildInpParent
      - !_EnumLabel
        enum: EnumGnrcPrntEnumChildInp
        name: gnrcPrntEnumChildInpLabel
    items:
      - !_Aliased
        name: gnrcPrntEnumChildInpLabel
    name: EnumGnrcPrntEnumChildInp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumChildInp
    typeKind: !_TypeKind Enum
  !_Identifier FieldGnrcPrntEnumChildInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: FieldGnrcPrntEnumChildInp
        type: !_InputBase
          typeParam: ref
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          typeParam: ref
    name: FieldGnrcPrntEnumChildInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: EnumGnrcPrntEnumChildInp
        name: ref
  !_Identifier GnrcPrntEnumChildInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: FieldGnrcPrntEnumChildInp
        type: !_InputBase
          input: ParentGnrcPrntEnumChildInp
    name: GnrcPrntEnumChildInp
    parent: !_InputBase
      input: FieldGnrcPrntEnumChildInp
      typeArgs:
        - !_InputArg
          input: ParentGnrcPrntEnumChildInp
    typeKind: !_TypeKind Input
  !_Identifier ParentGnrcPrntEnumChildInp: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentGnrcPrntEnumChildInp
        name: gnrcPrntEnumChildInpParent
    items:
      - !_Aliased
        name: gnrcPrntEnumChildInpParent
    name: ParentGnrcPrntEnumChildInp
    typeKind: !_TypeKind Enum