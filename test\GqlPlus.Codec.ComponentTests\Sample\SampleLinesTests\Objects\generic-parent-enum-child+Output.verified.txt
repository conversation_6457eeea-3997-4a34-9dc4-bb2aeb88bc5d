﻿!_Schema
types: !_Map_Type
  !_Identifier EnumGnrcPrntEnumChildOutp: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentGnrcPrntEnumChildOutp
        name: gnrcPrntEnumChildOutpParent
      - !_EnumLabel
        enum: EnumGnrcPrntEnumChildOutp
        name: gnrcPrntEnumChildOutpLabel
    items:
      - !_Aliased
        name: gnrcPrntEnumChildOutpLabel
    name: EnumGnrcPrntEnumChildOutp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Enum
  !_Identifier FieldGnrcPrntEnumChildOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: FieldGnrcPrntEnumChildOutp
        type: !_OutputBase
          typeParam: ref
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: ref
    name: FieldGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: EnumGnrcPrntEnumChildOutp
        name: ref
  !_Identifier GnrcPrntEnumChildOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: FieldGnrcPrntEnumChildOutp
        type: !_OutputBase
          output: ParentGnrcPrntEnumChildOutp
    name: GnrcPrntEnumChildOutp
    parent: !_OutputBase
      output: FieldGnrcPrntEnumChildOutp
      typeArgs:
        - !_OutputArg
          output: ParentGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Output
  !_Identifier ParentGnrcPrntEnumChildOutp: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentGnrcPrntEnumChildOutp
        name: gnrcPrntEnumChildOutpParent
    items:
      - !_Aliased
        name: gnrcPrntEnumChildOutpParent
    name: ParentGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Enum