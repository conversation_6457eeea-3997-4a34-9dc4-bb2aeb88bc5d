﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcFieldParamDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcFieldParamDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcFieldParamDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcFieldParamDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcFieldParamDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: GnrcFieldParamDual
        type: !_DualBase
          dual: RefGnrcFieldParamDual
          typeArgs:
            - !_DualArg
              dual: AltGnrcFieldParamDual
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          dual: RefGnrcFieldParamDual
          typeArgs:
            - !_DualArg
              dual: AltGnrcFieldParamDual
    name: GnrcFieldParamDual
    typeKind: !_TypeKind Dual
  !_Identifier RefGnrcFieldParamDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefGnrcFieldParamDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefGnrcFieldParamDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref