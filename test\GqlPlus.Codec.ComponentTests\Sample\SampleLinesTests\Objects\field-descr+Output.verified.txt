﻿!_Schema
types: !_Map_Type
  !_Identifier FieldDescrOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        description: 'Test Descr'
        name: field
        object: FieldDescrOutp
        type: !_OutputBase
          output: String
    fields:
      - !_OutputField
        description: 'Test Descr'
        name: field
        type: !_OutputBase
          output: String
    name: FieldDescrOutp
    typeKind: !_TypeKind Output