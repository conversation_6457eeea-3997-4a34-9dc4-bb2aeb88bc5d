﻿!_Schema
types: !_Map_Type
  !_Identifier AltDescrDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltDescrDual
        type: !_DualBase
          description: 'Test Descr'
          dual: String
    alternates:
      - !_DualAlternate
        type: !_DualBase
          description: 'Test Descr'
          dual: String
    name: AltDescrDual
    typeKind: !_TypeKind Dual