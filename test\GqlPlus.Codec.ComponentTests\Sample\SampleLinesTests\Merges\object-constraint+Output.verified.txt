﻿!_Schema
types: !_Map_Type
  !_Identifier ObjCnstOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: ObjCnstOutp
        type: !_OutputBase
          typeParam: type
      - !_ObjectFor(_OutputField)
        name: str
        object: ObjCnstOutp
        type: !_OutputBase
          typeParam: type
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: type
      - !_OutputField
        name: str
        type: !_OutputBase
          typeParam: type
    name: ObjCnstOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type