﻿!_Schema
types: !_Map_Type
  !_Identifier AltAltModParamOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: AltAltModParamOutp
        type: !_OutputBase
          output: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: alt
        object: AltAltModParamOutp
        type: !_OutputBase
          output: Number
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: String
    fields:
      - !_OutputField
        name: alt
        type: !_OutputBase
          output: Number
    name: AltAltModParamOutp
    typeKind: !_TypeKind Output
  !_Identifier AltModParamOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        object: AltModParamOutp
        type: !_OutputBase
          output: AltAltModParamOutp
    alternates:
      - !_OutputAlternate
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        type: !_OutputBase
          output: AltAltModParamOutp
    name: AltModParamOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: mod