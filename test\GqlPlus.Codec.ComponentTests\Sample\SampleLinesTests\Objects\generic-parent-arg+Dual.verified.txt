﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcPrntArgDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefGnrcPrntArgDual
        type: !_DualBase
          typeParam: ref
    name: GnrcPrntArgDual
    parent: !_DualBase
      dual: RefGnrcPrntArgDual
      typeArgs:
        - !_DualArg
          typeParam: type
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type
  !_Identifier RefGnrcPrntArgDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefGnrcPrntArgDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefGnrcPrntArgDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref