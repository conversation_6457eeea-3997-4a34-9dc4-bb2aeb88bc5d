﻿{
  "$tag": "_Schema",
  "types": {
    "$tag": "_Map_Type",
    "AltAltDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltAltDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltAltDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltAltDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltAltInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltAltInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltAltInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltAltInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltAltModBoolDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltAltModBoolDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltAltModBoolDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltAltModBoolDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltAltModBoolInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltAltModBoolInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltAltModBoolInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltAltModBoolInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltAltModBoolOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltAltModBoolOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltAltModBoolOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltAltModBoolOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltAltModParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltAltModParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltAltModParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltAltModParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltAltModParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltAltModParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltAltModParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltAltModParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltAltModParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltAltModParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltAltModParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltAltModParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltAltOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltAltOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltAltOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltAltOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltCnstAltDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltCnstAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltCnstAltDualDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstAltDualDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltCnstAltDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstAltDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltCnstAltDualInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltCnstAltDualInp",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstAltDualInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltCnstAltDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstAltDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltCnstAltDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltCnstAltDualOutp",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstAltDualOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltCnstAltObjDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstAltObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltCnstAltObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltCnstAltObjDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstAltObjDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltCnstAltObjInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "PrntCnstAltObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltCnstAltObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltCnstAltObjInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "PrntCnstAltObjInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltCnstAltObjOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "PrntCnstAltObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltCnstAltObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltCnstAltObjOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "PrntCnstAltObjOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltCnstFieldDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltCnstFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltCnstFieldDualDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstFieldDualDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltCnstFieldDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstFieldDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltCnstFieldDualInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltCnstFieldDualInp",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstFieldDualInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltCnstFieldDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstFieldDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltCnstFieldDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltCnstFieldDualOutp",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstFieldDualOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltCnstFieldObjDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstFieldObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltCnstFieldObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltCnstFieldObjDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstFieldObjDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltCnstFieldObjInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "PrntCnstFieldObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltCnstFieldObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltCnstFieldObjInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "PrntCnstFieldObjInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltCnstFieldObjOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "PrntCnstFieldObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltCnstFieldObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltCnstFieldObjOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "PrntCnstFieldObjOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltCnstPrntDualPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntDualPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltCnstPrntDualPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltCnstPrntDualPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstPrntDualPrntDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltCnstPrntDualPrntInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntDualPrntInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltCnstPrntDualPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltCnstPrntDualPrntInp",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstPrntDualPrntInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltCnstPrntDualPrntOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntDualPrntOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltCnstPrntDualPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltCnstPrntDualPrntOutp",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstPrntDualPrntOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltCnstPrntObjPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntObjPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltCnstPrntObjPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltCnstPrntObjPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "PrntCnstPrntObjPrntDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltCnstPrntObjPrntInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "PrntCnstPrntObjPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltCnstPrntObjPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltCnstPrntObjPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "PrntCnstPrntObjPrntInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltCnstPrntObjPrntOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "PrntCnstPrntObjPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltCnstPrntObjPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltCnstPrntObjPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "PrntCnstPrntObjPrntOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltDescrDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltDescrDual",
          "type": {
            "$tag": "_DualBase",
            "description": "Test Descr",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "description": "Test Descr",
            "dual": "String"
          }
        }
      ],
      "name": "AltDescrDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltDescrInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltDescrInp",
          "type": {
            "$tag": "_InputBase",
            "description": "Test Descr",
            "input": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "description": "Test Descr",
            "input": "String"
          }
        }
      ],
      "name": "AltDescrInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltDescrOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltDescrOutp",
          "type": {
            "$tag": "_OutputBase",
            "description": "Test Descr",
            "output": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "description": "Test Descr",
            "output": "String"
          }
        }
      ],
      "name": "AltDescrOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "AltAltDual"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "AltAltDual"
          }
        }
      ],
      "name": "AltDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "ObjDualAltDualDual"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "ObjDualAltDualDual"
          }
        }
      ],
      "name": "AltDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "ObjDualAltDualInp"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "ObjDualAltDualInp"
          }
        }
      ],
      "name": "AltDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "ObjDualAltDualOutp"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "ObjDualAltDualOutp"
          }
        }
      ],
      "name": "AltDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltGnrcAltDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcAltDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcAltDualInp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcAltDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcAltDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcAltDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcAltDualOutp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcAltDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcAltDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcAltDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcAltParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcAltParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcAltParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcAltParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcAltParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltGnrcAltParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltGnrcAltParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltGnrcAltParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltGnrcAltParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltGnrcAltParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltGnrcAltParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltGnrcAltParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltGnrcFieldDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcFieldDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcFieldDualInp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcFieldDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcFieldDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcFieldDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcFieldDualOutp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcFieldDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcFieldDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcFieldDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcFieldParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcFieldParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcFieldParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcFieldParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcFieldParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltGnrcFieldParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltGnrcFieldParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltGnrcFieldParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltGnrcFieldParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltGnrcFieldParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltGnrcFieldParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltGnrcFieldParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltGnrcPrntDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcPrntDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcPrntDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcPrntDualInp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcPrntDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcPrntDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcPrntDualOutp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcPrntDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcPrntDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcPrntDualPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcPrntDualPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcPrntDualPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntDualPrntDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcPrntDualPrntInp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcPrntDualPrntInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcPrntDualPrntInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntDualPrntInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcPrntDualPrntOutp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcPrntDualPrntOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcPrntDualPrntOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntDualPrntOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcPrntParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcPrntParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcPrntParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcPrntParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltGnrcPrntParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltGnrcPrntParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltGnrcPrntParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltGnrcPrntParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltGnrcPrntParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltGnrcPrntParamPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcPrntParamPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcPrntParamPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntParamPrntDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltGnrcPrntParamPrntInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltGnrcPrntParamPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltGnrcPrntParamPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "alt",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntParamPrntInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltGnrcPrntParamPrntOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltGnrcPrntParamPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltGnrcPrntParamPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "alt",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "AltGnrcPrntParamPrntOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltInp",
          "type": {
            "$tag": "_InputBase",
            "input": "AltAltInp"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "AltAltInp"
          }
        }
      ],
      "name": "AltInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltModBoolDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "^",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "object": "AltModBoolDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "AltAltModBoolDual"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "^",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "AltAltModBoolDual"
          }
        }
      ],
      "name": "AltModBoolDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltModBoolInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "^",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "object": "AltModBoolInp",
          "type": {
            "$tag": "_InputBase",
            "input": "AltAltModBoolInp"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "^",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "type": {
            "$tag": "_InputBase",
            "input": "AltAltModBoolInp"
          }
        }
      ],
      "name": "AltModBoolInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltModBoolOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "^",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "object": "AltModBoolOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "AltAltModBoolOutp"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "^",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "AltAltModBoolOutp"
          }
        }
      ],
      "name": "AltModBoolOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltModParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "object": "AltModParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "AltAltModParamDual"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "AltAltModParamDual"
          }
        }
      ],
      "name": "AltModParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "mod"
        }
      ]
    },
    "AltModParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "object": "AltModParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "AltAltModParamInp"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "type": {
            "$tag": "_InputBase",
            "input": "AltAltModParamInp"
          }
        }
      ],
      "name": "AltModParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "mod"
        }
      ]
    },
    "AltModParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "object": "AltModParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "AltAltModParamOutp"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "AltAltModParamOutp"
          }
        }
      ],
      "name": "AltModParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "mod"
        }
      ]
    },
    "AltOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "AltAltOutp"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "AltAltOutp"
          }
        }
      ],
      "name": "AltOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "AltSmplDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltSmplDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "AltSmplDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "AltSmplInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltSmplInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "name": "AltSmplInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "AltSmplOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltSmplOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "name": "AltSmplOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CnstAltDmnDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "CnstAltDmnDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefCnstAltDmnDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "DomCnstAltDmnDual"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefCnstAltDmnDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "DomCnstAltDmnDual"
              }
            ]
          }
        }
      ],
      "name": "CnstAltDmnDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "CnstAltDmnInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "CnstAltDmnInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefCnstAltDmnInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "DomCnstAltDmnInp"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "RefCnstAltDmnInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "DomCnstAltDmnInp"
              }
            ]
          }
        }
      ],
      "name": "CnstAltDmnInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "CnstAltDmnOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "CnstAltDmnOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefCnstAltDmnOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "DomCnstAltDmnOutp"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefCnstAltDmnOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "DomCnstAltDmnOutp"
              }
            ]
          }
        }
      ],
      "name": "CnstAltDmnOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CnstAltDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "CnstAltDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "CnstAltDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "Number"
          },
          "name": "type"
        }
      ]
    },
    "CnstAltDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "CnstAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefCnstAltDualDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltCnstAltDualDual"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefCnstAltDualDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltCnstAltDualDual"
              }
            ]
          }
        }
      ],
      "name": "CnstAltDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "CnstAltDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "CnstAltDualInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefCnstAltDualInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "AltCnstAltDualInp"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "RefCnstAltDualInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "AltCnstAltDualInp"
              }
            ]
          }
        }
      ],
      "name": "CnstAltDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "CnstAltDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "CnstAltDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefCnstAltDualOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "AltCnstAltDualOutp"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefCnstAltDualOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "AltCnstAltDualOutp"
              }
            ]
          }
        }
      ],
      "name": "CnstAltDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CnstAltInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "CnstAltInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "CnstAltInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "Number"
          },
          "name": "type"
        }
      ]
    },
    "CnstAltObjDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "CnstAltObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefCnstAltObjDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltCnstAltObjDual"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefCnstAltObjDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltCnstAltObjDual"
              }
            ]
          }
        }
      ],
      "name": "CnstAltObjDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "CnstAltObjInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "CnstAltObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefCnstAltObjInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "AltCnstAltObjInp"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "RefCnstAltObjInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "AltCnstAltObjInp"
              }
            ]
          }
        }
      ],
      "name": "CnstAltObjInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "CnstAltObjOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "CnstAltObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefCnstAltObjOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "AltCnstAltObjOutp"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefCnstAltObjOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "AltCnstAltObjOutp"
              }
            ]
          }
        }
      ],
      "name": "CnstAltObjOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CnstAltOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "CnstAltOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "CnstAltOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "Number"
          },
          "name": "type"
        }
      ]
    },
    "CnstFieldDmnDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "RefCnstFieldDmnDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "DomCnstFieldDmnDual"
          }
        }
      ],
      "name": "CnstFieldDmnDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefCnstFieldDmnDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "DomCnstFieldDmnDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "CnstFieldDmnInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "RefCnstFieldDmnInp",
          "type": {
            "$tag": "_InputBase",
            "input": "DomCnstFieldDmnInp"
          }
        }
      ],
      "name": "CnstFieldDmnInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefCnstFieldDmnInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "DomCnstFieldDmnInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "CnstFieldDmnOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefCnstFieldDmnOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "DomCnstFieldDmnOutp"
          }
        }
      ],
      "name": "CnstFieldDmnOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefCnstFieldDmnOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "DomCnstFieldDmnOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CnstFieldDualDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "RefCnstFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "AltCnstFieldDualDual"
          }
        }
      ],
      "name": "CnstFieldDualDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefCnstFieldDualDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltCnstFieldDualDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "CnstFieldDualInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "RefCnstFieldDualInp",
          "type": {
            "$tag": "_InputBase",
            "input": "AltCnstFieldDualInp"
          }
        }
      ],
      "name": "CnstFieldDualInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefCnstFieldDualInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "AltCnstFieldDualInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "CnstFieldDualOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefCnstFieldDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "AltCnstFieldDualOutp"
          }
        }
      ],
      "name": "CnstFieldDualOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefCnstFieldDualOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "AltCnstFieldDualOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CnstFieldObjDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "RefCnstFieldObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "AltCnstFieldObjDual"
          }
        }
      ],
      "name": "CnstFieldObjDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefCnstFieldObjDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltCnstFieldObjDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "CnstFieldObjInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "RefCnstFieldObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "AltCnstFieldObjInp"
          }
        }
      ],
      "name": "CnstFieldObjInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefCnstFieldObjInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "AltCnstFieldObjInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "CnstFieldObjOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefCnstFieldObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "AltCnstFieldObjOutp"
          }
        }
      ],
      "name": "CnstFieldObjOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefCnstFieldObjOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "AltCnstFieldObjOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CnstPrntDualPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntDualPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltCnstPrntDualPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "CnstPrntDualPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefCnstPrntDualPrntDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltCnstPrntDualPrntDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "CnstPrntDualPrntInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntDualPrntInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltCnstPrntDualPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "CnstPrntDualPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefCnstPrntDualPrntInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "AltCnstPrntDualPrntInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "CnstPrntDualPrntOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntDualPrntOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltCnstPrntDualPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "CnstPrntDualPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefCnstPrntDualPrntOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "AltCnstPrntDualPrntOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CnstPrntObjPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntObjPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltCnstPrntObjPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "CnstPrntObjPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefCnstPrntObjPrntDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltCnstPrntObjPrntDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "CnstPrntObjPrntInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "PrntCnstPrntObjPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltCnstPrntObjPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "CnstPrntObjPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefCnstPrntObjPrntInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "AltCnstPrntObjPrntInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "CnstPrntObjPrntOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "PrntCnstPrntObjPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltCnstPrntObjPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "CnstPrntObjPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefCnstPrntObjPrntOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "AltCnstPrntObjPrntOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "DomCnstAltDmnDual": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DomCnstAltDmnDual",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "name": "DomCnstAltDmnDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomCnstAltDmnInp": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DomCnstAltDmnInp",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "name": "DomCnstAltDmnInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomCnstAltDmnOutp": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DomCnstAltDmnOutp",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "name": "DomCnstAltDmnOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomCnstFieldDmnDual": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DomCnstFieldDmnDual",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "name": "DomCnstFieldDmnDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomCnstFieldDmnInp": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DomCnstFieldDmnInp",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "name": "DomCnstFieldDmnInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomCnstFieldDmnOutp": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DomCnstFieldDmnOutp",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "name": "DomCnstFieldDmnOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomGnrcPrntEnumDomDual": {
      "$tag": "_DomainEnum",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainLabel)",
          "domain": "DomGnrcPrntEnumDomDual",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "gnrcPrntEnumDomDualLabel",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumDomDual"
          }
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Enum"
      },
      "items": [
        {
          "$tag": "_DomainLabel",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "gnrcPrntEnumDomDualLabel",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumDomDual"
          }
        }
      ],
      "name": "DomGnrcPrntEnumDomDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomGnrcPrntEnumDomInp": {
      "$tag": "_DomainEnum",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainLabel)",
          "domain": "DomGnrcPrntEnumDomInp",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "gnrcPrntEnumDomInpLabel",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumDomInp"
          }
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Enum"
      },
      "items": [
        {
          "$tag": "_DomainLabel",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "gnrcPrntEnumDomInpLabel",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumDomInp"
          }
        }
      ],
      "name": "DomGnrcPrntEnumDomInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomGnrcPrntEnumDomOutp": {
      "$tag": "_DomainEnum",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainLabel)",
          "domain": "DomGnrcPrntEnumDomOutp",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "gnrcPrntEnumDomOutpLabel",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumDomOutp"
          }
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Enum"
      },
      "items": [
        {
          "$tag": "_DomainLabel",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "gnrcPrntEnumDomOutpLabel",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumDomOutp"
          }
        }
      ],
      "name": "DomGnrcPrntEnumDomOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomGnrcPrntStrDomDual": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DomGnrcPrntStrDomDual",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "name": "DomGnrcPrntStrDomDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomGnrcPrntStrDomInp": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DomGnrcPrntStrDomInp",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "name": "DomGnrcPrntStrDomInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomGnrcPrntStrDomOutp": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DomGnrcPrntStrDomOutp",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "\\w\u002B"
        }
      ],
      "name": "DomGnrcPrntStrDomOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomOutpParamModDmn": {
      "$tag": "_DomainNumber",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRange)",
          "domain": "DomOutpParamModDmn",
          "exclude": false,
          "from": 1,
          "to": 10
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Number"
      },
      "items": [
        {
          "$tag": "_DomainRange",
          "exclude": false,
          "from": 1,
          "to": 10
        }
      ],
      "name": "DomOutpParamModDmn",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DomOutpParamModParam": {
      "$tag": "_DomainNumber",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRange)",
          "domain": "DomOutpParamModParam",
          "exclude": false,
          "from": 1,
          "to": 10
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Number"
      },
      "items": [
        {
          "$tag": "_DomainRange",
          "exclude": false,
          "from": 1,
          "to": 10
        }
      ],
      "name": "DomOutpParamModParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "EnumFieldModEnumDual": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumFieldModEnumDual",
          "name": "value"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "value"
        }
      ],
      "name": "EnumFieldModEnumDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumFieldModEnumInp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumFieldModEnumInp",
          "name": "value"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "value"
        }
      ],
      "name": "EnumFieldModEnumInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumFieldModEnumOutp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumFieldModEnumOutp",
          "name": "value"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "value"
        }
      ],
      "name": "EnumFieldModEnumOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntEnumChildDual": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumChildDual",
          "name": "gnrcPrntEnumChildDualParent"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumChildDual",
          "name": "gnrcPrntEnumChildDualLabel"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumChildDualLabel"
        }
      ],
      "name": "EnumGnrcPrntEnumChildDual",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "ParentGnrcPrntEnumChildDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntEnumChildInp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumChildInp",
          "name": "gnrcPrntEnumChildInpParent"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumChildInp",
          "name": "gnrcPrntEnumChildInpLabel"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumChildInpLabel"
        }
      ],
      "name": "EnumGnrcPrntEnumChildInp",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "ParentGnrcPrntEnumChildInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntEnumChildOutp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumChildOutp",
          "name": "gnrcPrntEnumChildOutpParent"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumChildOutp",
          "name": "gnrcPrntEnumChildOutpLabel"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumChildOutpLabel"
        }
      ],
      "name": "EnumGnrcPrntEnumChildOutp",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "ParentGnrcPrntEnumChildOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntEnumDomDual": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumDomDual",
          "name": "gnrcPrntEnumDomDualLabel"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumDomDual",
          "name": "gnrcPrntEnumDomDualOther"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumDomDualLabel"
        },
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumDomDualOther"
        }
      ],
      "name": "EnumGnrcPrntEnumDomDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntEnumDomInp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumDomInp",
          "name": "gnrcPrntEnumDomInpLabel"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumDomInp",
          "name": "gnrcPrntEnumDomInpOther"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumDomInpLabel"
        },
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumDomInpOther"
        }
      ],
      "name": "EnumGnrcPrntEnumDomInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntEnumDomOutp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumDomOutp",
          "name": "gnrcPrntEnumDomOutpLabel"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumDomOutp",
          "name": "gnrcPrntEnumDomOutpOther"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumDomOutpLabel"
        },
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumDomOutpOther"
        }
      ],
      "name": "EnumGnrcPrntEnumDomOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntEnumPrntDual": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumPrntDual",
          "name": "gnrcPrntEnumPrntDualParent"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumPrntDual",
          "name": "gnrcPrntEnumPrntDualLabel"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumPrntDualLabel"
        }
      ],
      "name": "EnumGnrcPrntEnumPrntDual",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "ParentGnrcPrntEnumPrntDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntEnumPrntInp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumPrntInp",
          "name": "gnrcPrntEnumPrntInpParent"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumPrntInp",
          "name": "gnrcPrntEnumPrntInpLabel"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumPrntInpLabel"
        }
      ],
      "name": "EnumGnrcPrntEnumPrntInp",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "ParentGnrcPrntEnumPrntInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntEnumPrntOutp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumPrntOutp",
          "name": "gnrcPrntEnumPrntOutpParent"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntEnumPrntOutp",
          "name": "gnrcPrntEnumPrntOutpLabel"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumPrntOutpLabel"
        }
      ],
      "name": "EnumGnrcPrntEnumPrntOutp",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "ParentGnrcPrntEnumPrntOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntSmplEnumDual": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntSmplEnumDual",
          "name": "gnrcPrntSmplEnumDual"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntSmplEnumDual"
        }
      ],
      "name": "EnumGnrcPrntSmplEnumDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntSmplEnumInp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntSmplEnumInp",
          "name": "gnrcPrntSmplEnumInp"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntSmplEnumInp"
        }
      ],
      "name": "EnumGnrcPrntSmplEnumInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumGnrcPrntSmplEnumOutp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumGnrcPrntSmplEnumOutp",
          "name": "gnrcPrntSmplEnumOutp"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntSmplEnumOutp"
        }
      ],
      "name": "EnumGnrcPrntSmplEnumOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumInpFieldEnum": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumInpFieldEnum",
          "name": "inpFieldEnum"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "inpFieldEnum"
        }
      ],
      "name": "EnumInpFieldEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpCnstDomEnum": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpCnstDomEnum",
          "name": "outpCnstDomEnum"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpCnstDomEnum",
          "name": "other"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpCnstDomEnum"
        },
        {
          "$tag": "_Aliased",
          "name": "other"
        }
      ],
      "name": "EnumOutpCnstDomEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpCnstEnum": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpCnstEnum",
          "name": "outpCnstEnum"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpCnstEnum"
        }
      ],
      "name": "EnumOutpCnstEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpCnstEnumPrnt": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentOutpCnstEnumPrnt",
          "name": "parentOutpCnstEnumPrnt"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpCnstEnumPrnt",
          "name": "outpCnstEnumPrnt"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpCnstEnumPrnt"
        }
      ],
      "name": "EnumOutpCnstEnumPrnt",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "ParentOutpCnstEnumPrnt"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpCnstPrntEnum": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentOutpCnstPrntEnum",
          "name": "parentOutpCnstPrntEnum"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpCnstPrntEnum",
          "name": "outpCnstPrntEnum"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpCnstPrntEnum"
        }
      ],
      "name": "EnumOutpCnstPrntEnum",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "ParentOutpCnstPrntEnum"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpFieldEnum": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpFieldEnum",
          "name": "outpFieldEnum"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpFieldEnum"
        }
      ],
      "name": "EnumOutpFieldEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpFieldEnumPrnt": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "PrntOutpFieldEnumPrnt",
          "name": "prnt_outpFieldEnumPrnt"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpFieldEnumPrnt",
          "name": "outpFieldEnumPrnt"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpFieldEnumPrnt"
        }
      ],
      "name": "EnumOutpFieldEnumPrnt",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "PrntOutpFieldEnumPrnt"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpFieldValue": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpFieldValue",
          "name": "outpFieldValue"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpFieldValue"
        }
      ],
      "name": "EnumOutpFieldValue",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpFieldValueDescr": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpFieldValueDescr",
          "name": "outpFieldValueDescr"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpFieldValueDescr"
        }
      ],
      "name": "EnumOutpFieldValueDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpGnrcEnum": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpGnrcEnum",
          "name": "outpGnrcEnum"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpGnrcEnum"
        }
      ],
      "name": "EnumOutpGnrcEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpGnrcValue": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpGnrcValue",
          "name": "outpGnrcValue"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpGnrcValue"
        }
      ],
      "name": "EnumOutpGnrcValue",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumOutpPrntGnrc": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "PrntOutpPrntGnrc",
          "name": "prnt_outpPrntGnrc"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumOutpPrntGnrc",
          "name": "outpPrntGnrc"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "outpPrntGnrc"
        }
      ],
      "name": "EnumOutpPrntGnrc",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "PrntOutpPrntGnrc"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "FieldDescrDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "description": "Test Descr",
          "name": "field",
          "object": "FieldDescrDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "description": "Test Descr",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "FieldDescrDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FieldDescrInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "description": "Test Descr",
          "name": "field",
          "object": "FieldDescrInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "description": "Test Descr",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "name": "FieldDescrInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FieldDescrOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "description": "Test Descr",
          "name": "field",
          "object": "FieldDescrOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "description": "Test Descr",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "name": "FieldDescrOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FieldDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "FieldDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FieldDualDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldFieldDualDual"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldFieldDualDual"
          }
        }
      ],
      "name": "FieldDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FieldDualInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldFieldDualInp"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldFieldDualInp"
          }
        }
      ],
      "name": "FieldDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FieldDualOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldFieldDualOutp"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldFieldDualOutp"
          }
        }
      ],
      "name": "FieldDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FieldGnrcPrntEnumChildDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumChildDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntEnumChildDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumChildDual"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntEnumChildInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumChildInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntEnumChildInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumChildInp"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntEnumChildOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumChildOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntEnumChildOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumChildOutp"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntEnumDomDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumDomDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntEnumDomDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumDomDual"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntEnumDomInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumDomInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntEnumDomInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumDomInp"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntEnumDomOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumDomOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntEnumDomOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "EnumGnrcPrntEnumDomOutp"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntEnumPrntDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumPrntDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntEnumPrntDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "ParentGnrcPrntEnumPrntDual"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntEnumPrntInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumPrntInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntEnumPrntInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "ParentGnrcPrntEnumPrntInp"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntEnumPrntOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntEnumPrntOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "ParentGnrcPrntEnumPrntOutp"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntSmplEnumDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldGnrcPrntSmplEnumDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntSmplEnumDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Simple"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntSmplEnumInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldGnrcPrntSmplEnumInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntSmplEnumInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Simple"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntSmplEnumOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldGnrcPrntSmplEnumOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntSmplEnumOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Simple"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntStrDomDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldGnrcPrntStrDomDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntStrDomDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntStrDomInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldGnrcPrntStrDomInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntStrDomInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "FieldGnrcPrntStrDomOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldGnrcPrntStrDomOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "FieldGnrcPrntStrDomOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "FieldInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "name": "FieldInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FieldModEnumDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "EnumFieldModEnumDual",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Enum"
              }
            }
          ],
          "name": "field",
          "object": "FieldModEnumDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "EnumFieldModEnumDual",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Enum"
              }
            }
          ],
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "FieldModEnumDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FieldModEnumInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "EnumFieldModEnumInp",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Enum"
              }
            }
          ],
          "name": "field",
          "object": "FieldModEnumInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "EnumFieldModEnumInp",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Enum"
              }
            }
          ],
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "name": "FieldModEnumInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FieldModEnumOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "EnumFieldModEnumOutp",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Enum"
              }
            }
          ],
          "name": "field",
          "object": "FieldModEnumOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "EnumFieldModEnumOutp",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Enum"
              }
            }
          ],
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "name": "FieldModEnumOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FieldModParamDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "modifiers": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "name": "field",
          "object": "FieldModParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldFieldModParamDual"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "modifiers": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldFieldModParamDual"
          }
        }
      ],
      "name": "FieldModParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "mod"
        }
      ]
    },
    "FieldModParamInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "modifiers": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "name": "field",
          "object": "FieldModParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "FldFieldModParamInp"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "modifiers": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "FldFieldModParamInp"
          }
        }
      ],
      "name": "FieldModParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "mod"
        }
      ]
    },
    "FieldModParamOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "name": "field",
          "object": "FieldModParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "FldFieldModParamOutp"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "FldFieldModParamOutp"
          }
        }
      ],
      "name": "FieldModParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "mod"
        }
      ]
    },
    "FieldObjDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldFieldObjDual"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldFieldObjDual"
          }
        }
      ],
      "name": "FieldObjDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FieldObjInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "FldFieldObjInp"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "FldFieldObjInp"
          }
        }
      ],
      "name": "FieldObjInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FieldObjOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "FldFieldObjOutp"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "FldFieldObjOutp"
          }
        }
      ],
      "name": "FieldObjOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FieldOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "name": "FieldOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FieldSmplDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldSmplDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "FieldSmplDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FieldSmplInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldSmplInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "FieldSmplInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FieldSmplOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldSmplOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "FieldSmplOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FieldTypeDescrDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldTypeDescrDual",
          "type": {
            "$tag": "_DualBase",
            "description": "Test Descr",
            "dual": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "description": "Test Descr",
            "dual": "Number"
          }
        }
      ],
      "name": "FieldTypeDescrDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FieldTypeDescrInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldTypeDescrInp",
          "type": {
            "$tag": "_InputBase",
            "description": "Test Descr",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "description": "Test Descr",
            "input": "Number"
          }
        }
      ],
      "name": "FieldTypeDescrInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FieldTypeDescrOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldTypeDescrOutp",
          "type": {
            "$tag": "_OutputBase",
            "description": "Test Descr",
            "output": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "description": "Test Descr",
            "output": "Number"
          }
        }
      ],
      "name": "FieldTypeDescrOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FldFieldDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "FldFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FldFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "FldFieldDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldFieldDualInp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "FldFieldDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FldFieldDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "FldFieldDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldFieldDualOutp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "FldFieldDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FldFieldDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "FldFieldDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldFieldModParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "FldFieldModParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FldFieldModParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "FldFieldModParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldFieldModParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "FldFieldModParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FldFieldModParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "FldFieldModParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FldFieldModParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "FldFieldModParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FldFieldModParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "FldFieldModParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FldFieldObjDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "FldFieldObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FldFieldObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "FldFieldObjDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldFieldObjInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "FldFieldObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FldFieldObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "FldFieldObjInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FldFieldObjOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "FldFieldObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FldFieldObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "FldFieldObjOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FldInpFieldNull": {
      "$tag": "_TypeDual",
      "name": "FldInpFieldNull",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldOutpDescrParam": {
      "$tag": "_TypeDual",
      "name": "FldOutpDescrParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldOutpParam": {
      "$tag": "_TypeDual",
      "name": "FldOutpParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldOutpParamDescr": {
      "$tag": "_TypeDual",
      "name": "FldOutpParamDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldOutpParamTypeDescr": {
      "$tag": "_TypeDual",
      "name": "FldOutpParamTypeDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldOutpPrntParam": {
      "$tag": "_TypeDual",
      "name": "FldOutpPrntParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcAltArgDescrDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "GnrcAltArgDescrDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcAltArgDescrDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "description": "Test Descr",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcAltArgDescrDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "description": "Test Descr",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltArgDescrDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcAltArgDescrInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "GnrcAltArgDescrInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcAltArgDescrInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "description": "Test Descr",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcAltArgDescrInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "description": "Test Descr",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltArgDescrInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcAltArgDescrOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "GnrcAltArgDescrOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcAltArgDescrOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "description": "Test Descr",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcAltArgDescrOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "description": "Test Descr",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltArgDescrOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcAltArgDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "GnrcAltArgDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcAltArgDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcAltArgDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltArgDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcAltArgInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "GnrcAltArgInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcAltArgInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcAltArgInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltArgInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcAltArgOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "GnrcAltArgOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcAltArgOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcAltArgOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltArgOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcAltDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "GnrcAltDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "GnrcAltDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcAltDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "GnrcAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcAltDualDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcAltDualDual"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcAltDualDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcAltDualDual"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcAltDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "GnrcAltDualInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcAltDualInp",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcAltDualInp"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcAltDualInp",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcAltDualInp"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcAltDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "GnrcAltDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcAltDualOutp",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcAltDualOutp"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcAltDualOutp",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcAltDualOutp"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcAltInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "GnrcAltInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "GnrcAltInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcAltOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "GnrcAltOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "GnrcAltOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcAltParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "GnrcAltParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcAltParamDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcAltParamDual"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcAltParamDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcAltParamDual"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcAltParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "GnrcAltParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcAltParamInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "AltGnrcAltParamInp"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcAltParamInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "AltGnrcAltParamInp"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcAltParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "GnrcAltParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcAltParamOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "AltGnrcAltParamOutp"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcAltParamOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "AltGnrcAltParamOutp"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcAltSmplDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "GnrcAltSmplDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcAltSmplDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "String"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcAltSmplDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "String"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltSmplDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcAltSmplInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "GnrcAltSmplInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcAltSmplInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "String"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcAltSmplInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "String"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltSmplInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcAltSmplOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "GnrcAltSmplOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcAltSmplOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "String"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcAltSmplOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "String"
              }
            ]
          }
        }
      ],
      "name": "GnrcAltSmplOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcDescrDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "GnrcDescrDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "GnrcDescrDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "description": "Test Descr",
          "name": "type"
        }
      ]
    },
    "GnrcDescrInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "GnrcDescrInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "GnrcDescrInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "description": "Test Descr",
          "name": "type"
        }
      ]
    },
    "GnrcDescrOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "GnrcDescrOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "GnrcDescrOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "description": "Test Descr",
          "name": "type"
        }
      ]
    },
    "GnrcFieldArgDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "GnrcFieldArgDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcFieldArgDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcFieldArgDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "name": "GnrcFieldArgDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcFieldArgInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "GnrcFieldArgInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcFieldArgInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcFieldArgInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "name": "GnrcFieldArgInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcFieldArgOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "GnrcFieldArgOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcFieldArgOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcFieldArgOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "typeParam": "type"
              }
            ]
          }
        }
      ],
      "name": "GnrcFieldArgOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcFieldDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "GnrcFieldDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "GnrcFieldDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcFieldDualDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "GnrcFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcFieldDualDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcFieldDualDual"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcFieldDualDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcFieldDualDual"
              }
            ]
          }
        }
      ],
      "name": "GnrcFieldDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcFieldDualInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "GnrcFieldDualInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcFieldDualInp",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcFieldDualInp"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcFieldDualInp",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcFieldDualInp"
              }
            ]
          }
        }
      ],
      "name": "GnrcFieldDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcFieldDualOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "GnrcFieldDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcFieldDualOutp",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcFieldDualOutp"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcFieldDualOutp",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcFieldDualOutp"
              }
            ]
          }
        }
      ],
      "name": "GnrcFieldDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcFieldInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "GnrcFieldInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "GnrcFieldInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcFieldOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "GnrcFieldOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "GnrcFieldOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcFieldParamDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "GnrcFieldParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcFieldParamDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcFieldParamDual"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "RefGnrcFieldParamDual",
            "typeArgs": [
              {
                "$tag": "_DualArg",
                "dual": "AltGnrcFieldParamDual"
              }
            ]
          }
        }
      ],
      "name": "GnrcFieldParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcFieldParamInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "GnrcFieldParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcFieldParamInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "AltGnrcFieldParamInp"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "RefGnrcFieldParamInp",
            "typeArgs": [
              {
                "$tag": "_InputArg",
                "input": "AltGnrcFieldParamInp"
              }
            ]
          }
        }
      ],
      "name": "GnrcFieldParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcFieldParamOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "GnrcFieldParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcFieldParamOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "AltGnrcFieldParamOutp"
              }
            ]
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefGnrcFieldParamOutp",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "AltGnrcFieldParamOutp"
              }
            ]
          }
        }
      ],
      "name": "GnrcFieldParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcPrntArgDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcPrntArgDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "GnrcPrntArgDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefGnrcPrntArgDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "typeParam": "type"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcPrntArgInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcPrntArgInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "GnrcPrntArgInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefGnrcPrntArgInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "typeParam": "type"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcPrntArgOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcPrntArgOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "GnrcPrntArgOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefGnrcPrntArgOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "typeParam": "type"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcPrntDescrDual": {
      "$tag": "_TypeDual",
      "name": "GnrcPrntDescrDual",
      "parent": {
        "$tag": "_DualBase",
        "description": "Parent comment",
        "typeParam": "type"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcPrntDescrInp": {
      "$tag": "_TypeInput",
      "name": "GnrcPrntDescrInp",
      "parent": {
        "$tag": "_InputBase",
        "description": "Parent comment",
        "typeParam": "type"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcPrntDescrOutp": {
      "$tag": "_TypeOutput",
      "name": "GnrcPrntDescrOutp",
      "parent": {
        "$tag": "_OutputBase",
        "description": "Parent comment",
        "typeParam": "type"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcPrntDual": {
      "$tag": "_TypeDual",
      "name": "GnrcPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "typeParam": "type"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcPrntDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcPrntDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "AltGnrcPrntDualDual"
          }
        }
      ],
      "name": "GnrcPrntDualDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefGnrcPrntDualDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltGnrcPrntDualDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcPrntDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcPrntDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "AltGnrcPrntDualInp"
          }
        }
      ],
      "name": "GnrcPrntDualInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefGnrcPrntDualInp",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltGnrcPrntDualInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcPrntDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcPrntDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "AltGnrcPrntDualOutp"
          }
        }
      ],
      "name": "GnrcPrntDualOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefGnrcPrntDualOutp",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltGnrcPrntDualOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcPrntDualPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcPrntDualPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcPrntDualPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "GnrcPrntDualPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefGnrcPrntDualPrntDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltGnrcPrntDualPrntDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcPrntDualPrntInp": {
      "$tag": "_TypeInput",
      "name": "GnrcPrntDualPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefGnrcPrntDualPrntInp",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltGnrcPrntDualPrntInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcPrntDualPrntOutp": {
      "$tag": "_TypeOutput",
      "name": "GnrcPrntDualPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefGnrcPrntDualPrntOutp",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltGnrcPrntDualPrntOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcPrntEnumChildDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumChildDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "ParentGnrcPrntEnumChildDual"
          }
        }
      ],
      "name": "GnrcPrntEnumChildDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "FieldGnrcPrntEnumChildDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "ParentGnrcPrntEnumChildDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcPrntEnumChildInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumChildInp",
          "type": {
            "$tag": "_InputBase",
            "input": "ParentGnrcPrntEnumChildInp"
          }
        }
      ],
      "name": "GnrcPrntEnumChildInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "FieldGnrcPrntEnumChildInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "ParentGnrcPrntEnumChildInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcPrntEnumChildOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumChildOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "ParentGnrcPrntEnumChildOutp"
          }
        }
      ],
      "name": "GnrcPrntEnumChildOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "FieldGnrcPrntEnumChildOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "ParentGnrcPrntEnumChildOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcPrntEnumDomDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumDomDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "DomGnrcPrntEnumDomDual"
          }
        }
      ],
      "name": "GnrcPrntEnumDomDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "FieldGnrcPrntEnumDomDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "DomGnrcPrntEnumDomDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcPrntEnumDomInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumDomInp",
          "type": {
            "$tag": "_InputBase",
            "input": "DomGnrcPrntEnumDomInp"
          }
        }
      ],
      "name": "GnrcPrntEnumDomInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "FieldGnrcPrntEnumDomInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "DomGnrcPrntEnumDomInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcPrntEnumDomOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumDomOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "DomGnrcPrntEnumDomOutp"
          }
        }
      ],
      "name": "GnrcPrntEnumDomOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "FieldGnrcPrntEnumDomOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "DomGnrcPrntEnumDomOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcPrntEnumPrntDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "EnumGnrcPrntEnumPrntDual"
          }
        }
      ],
      "name": "GnrcPrntEnumPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "FieldGnrcPrntEnumPrntDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "EnumGnrcPrntEnumPrntDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcPrntEnumPrntInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "EnumGnrcPrntEnumPrntInp"
          }
        }
      ],
      "name": "GnrcPrntEnumPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "FieldGnrcPrntEnumPrntInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "EnumGnrcPrntEnumPrntInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcPrntEnumPrntOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldGnrcPrntEnumPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "EnumGnrcPrntEnumPrntOutp"
          }
        }
      ],
      "name": "GnrcPrntEnumPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "FieldGnrcPrntEnumPrntOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "EnumGnrcPrntEnumPrntOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcPrntInp": {
      "$tag": "_TypeInput",
      "name": "GnrcPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "typeParam": "type"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcPrntOutp": {
      "$tag": "_TypeOutput",
      "name": "GnrcPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "typeParam": "type"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "GnrcPrntParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcPrntParamDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "AltGnrcPrntParamDual"
          }
        }
      ],
      "name": "GnrcPrntParamDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefGnrcPrntParamDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltGnrcPrntParamDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcPrntParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcPrntParamInp",
          "type": {
            "$tag": "_InputBase",
            "input": "AltGnrcPrntParamInp"
          }
        }
      ],
      "name": "GnrcPrntParamInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefGnrcPrntParamInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "AltGnrcPrntParamInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcPrntParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcPrntParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "AltGnrcPrntParamOutp"
          }
        }
      ],
      "name": "GnrcPrntParamOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefGnrcPrntParamOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "AltGnrcPrntParamOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcPrntParamPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "AltGnrcPrntParamPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "AltGnrcPrntParamPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "GnrcPrntParamPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefGnrcPrntParamPrntDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "AltGnrcPrntParamPrntDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcPrntParamPrntInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "AltGnrcPrntParamPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "alt",
          "object": "AltGnrcPrntParamPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "GnrcPrntParamPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefGnrcPrntParamPrntInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "AltGnrcPrntParamPrntInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcPrntParamPrntOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "AltGnrcPrntParamPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "alt",
          "object": "AltGnrcPrntParamPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "GnrcPrntParamPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefGnrcPrntParamPrntOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "AltGnrcPrntParamPrntOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcPrntSmplEnumDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldGnrcPrntSmplEnumDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "EnumGnrcPrntSmplEnumDual"
          }
        }
      ],
      "name": "GnrcPrntSmplEnumDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "FieldGnrcPrntSmplEnumDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "EnumGnrcPrntSmplEnumDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcPrntSmplEnumInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldGnrcPrntSmplEnumInp",
          "type": {
            "$tag": "_InputBase",
            "input": "EnumGnrcPrntSmplEnumInp"
          }
        }
      ],
      "name": "GnrcPrntSmplEnumInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "FieldGnrcPrntSmplEnumInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "EnumGnrcPrntSmplEnumInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcPrntSmplEnumOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldGnrcPrntSmplEnumOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "EnumGnrcPrntSmplEnumOutp"
          }
        }
      ],
      "name": "GnrcPrntSmplEnumOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "FieldGnrcPrntSmplEnumOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "EnumGnrcPrntSmplEnumOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "GnrcPrntStrDomDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "FieldGnrcPrntStrDomDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "DomGnrcPrntStrDomDual"
          }
        }
      ],
      "name": "GnrcPrntStrDomDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "FieldGnrcPrntStrDomDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "dual": "DomGnrcPrntStrDomDual"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "GnrcPrntStrDomInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "FieldGnrcPrntStrDomInp",
          "type": {
            "$tag": "_InputBase",
            "input": "DomGnrcPrntStrDomInp"
          }
        }
      ],
      "name": "GnrcPrntStrDomInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "FieldGnrcPrntStrDomInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "input": "DomGnrcPrntStrDomInp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "GnrcPrntStrDomOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "FieldGnrcPrntStrDomOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "DomGnrcPrntStrDomOutp"
          }
        }
      ],
      "name": "GnrcPrntStrDomOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "FieldGnrcPrntStrDomOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "output": "DomGnrcPrntStrDomOutp"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "InOutpDescrParam": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "InOutpDescrParam",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "param",
          "object": "InOutpDescrParam",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "param",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InOutpDescrParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InOutpParam": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "InOutpParam",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "param",
          "object": "InOutpParam",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "param",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InOutpParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InOutpParamDescr": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "InOutpParamDescr",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "param",
          "object": "InOutpParamDescr",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "param",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InOutpParamDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InOutpParamModDmn": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "InOutpParamModDmn",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "param",
          "object": "InOutpParamModDmn",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "param",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InOutpParamModDmn",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InOutpParamModParam": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "InOutpParamModParam",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "param",
          "object": "InOutpParamModParam",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "param",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InOutpParamModParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InOutpParamTypeDescr": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "InOutpParamTypeDescr",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "param",
          "object": "InOutpParamTypeDescr",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "param",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InOutpParamTypeDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InOutpPrntParam": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "InOutpPrntParam",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "param",
          "object": "InOutpPrntParam",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "param",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InOutpPrntParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InpFieldDescrNmbr": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": 42,
          "description": "Test Descr",
          "name": "field",
          "object": "InpFieldDescrNmbr",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "default": 42,
          "description": "Test Descr",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InpFieldDescrNmbr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InpFieldEnum": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": "inpFieldEnum",
          "name": "field",
          "object": "InpFieldEnum",
          "type": {
            "$tag": "_InputBase",
            "input": "EnumInpFieldEnum"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "default": "inpFieldEnum",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "EnumInpFieldEnum"
          }
        }
      ],
      "name": "InpFieldEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InpFieldNmbr": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": 42,
          "name": "field",
          "object": "InpFieldNmbr",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "default": 42,
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InpFieldNmbr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InpFieldNmbrDescr": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": 42,
          "name": "field",
          "object": "InpFieldNmbrDescr",
          "type": {
            "$tag": "_InputBase",
            "description": "Test Descr",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "default": 42,
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "description": "Test Descr",
            "input": "Number"
          }
        }
      ],
      "name": "InpFieldNmbrDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InpFieldNull": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": {
            "$tag": "Null",
            "value": "null"
          },
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "field",
          "object": "InpFieldNull",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldInpFieldNull"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "default": {
            "$tag": "Null",
            "value": "null"
          },
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Opt"
              }
            }
          ],
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldInpFieldNull"
          }
        }
      ],
      "name": "InpFieldNull",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "InpFieldStr": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": "default",
          "name": "field",
          "object": "InpFieldStr",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "default": "default",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "name": "InpFieldStr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "JustOutpCnstDomEnum": {
      "$tag": "_DomainEnum",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainLabel)",
          "domain": "JustOutpCnstDomEnum",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "outpCnstDomEnum",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "EnumOutpCnstDomEnum"
          }
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Enum"
      },
      "items": [
        {
          "$tag": "_DomainLabel",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "outpCnstDomEnum",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "EnumOutpCnstDomEnum"
          }
        }
      ],
      "name": "JustOutpCnstDomEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "ObjDualAltDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "ObjDualAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "ObjDualAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "ObjDualAltDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "ObjDualAltDualInp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "ObjDualAltDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "ObjDualAltDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "ObjDualAltDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "ObjDualAltDualOutp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "ObjDualAltDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "alt",
          "object": "ObjDualAltDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "alt",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "ObjDualAltDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "OutpCnstDomEnum": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "OutpCnstDomEnum",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpCnstDomEnum",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "outpCnstDomEnum"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpCnstDomEnum",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "outpCnstDomEnum"
              }
            ]
          }
        }
      ],
      "name": "OutpCnstDomEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpCnstEnum": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "OutpCnstEnum",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpCnstEnum",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "outpCnstEnum"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpCnstEnum",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "outpCnstEnum"
              }
            ]
          }
        }
      ],
      "name": "OutpCnstEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpCnstEnumPrnt": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "OutpCnstEnumPrnt",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpCnstEnumPrnt",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "outpCnstEnumPrnt"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpCnstEnumPrnt",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "outpCnstEnumPrnt"
              }
            ]
          }
        }
      ],
      "name": "OutpCnstEnumPrnt",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpCnstPrntEnum": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "OutpCnstPrntEnum",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpCnstPrntEnum",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "parentOutpCnstPrntEnum"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpCnstPrntEnum",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "parentOutpCnstPrntEnum"
              }
            ]
          }
        }
      ],
      "name": "OutpCnstPrntEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpDescrParam": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "description": "Test Descr",
          "name": "field",
          "object": "OutpDescrParam",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpDescrParam"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpDescrParam"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "description": "Test Descr",
          "name": "field",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpDescrParam"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpDescrParam"
          }
        }
      ],
      "name": "OutpDescrParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpFieldEnum": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "field": "field",
          "label": "outpFieldEnum",
          "object": "OutpFieldEnum",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "EnumOutpFieldEnum"
        }
      ],
      "fields": [
        {
          "$tag": "_OutputEnum",
          "field": "field",
          "label": "outpFieldEnum",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "EnumOutpFieldEnum"
        }
      ],
      "name": "OutpFieldEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpFieldEnumPrnt": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "field": "field",
          "label": "prnt_outpFieldEnumPrnt",
          "object": "OutpFieldEnumPrnt",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "EnumOutpFieldEnumPrnt"
        }
      ],
      "fields": [
        {
          "$tag": "_OutputEnum",
          "field": "field",
          "label": "prnt_outpFieldEnumPrnt",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "EnumOutpFieldEnumPrnt"
        }
      ],
      "name": "OutpFieldEnumPrnt",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpFieldValue": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "field": "field",
          "label": "outpFieldValue",
          "object": "OutpFieldValue",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "EnumOutpFieldValue"
        }
      ],
      "fields": [
        {
          "$tag": "_OutputEnum",
          "field": "field",
          "label": "outpFieldValue",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "EnumOutpFieldValue"
        }
      ],
      "name": "OutpFieldValue",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpFieldValueDescr": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "description": "Test Descr",
          "field": "field",
          "label": "outpFieldValueDescr",
          "object": "OutpFieldValueDescr",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "EnumOutpFieldValueDescr"
        }
      ],
      "fields": [
        {
          "$tag": "_OutputEnum",
          "description": "Test Descr",
          "field": "field",
          "label": "outpFieldValueDescr",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "EnumOutpFieldValueDescr"
        }
      ],
      "name": "OutpFieldValueDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpGnrcEnum": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "OutpGnrcEnum",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpGnrcEnum",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "outpGnrcEnum",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "EnumOutpGnrcEnum"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpGnrcEnum",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "outpGnrcEnum",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "EnumOutpGnrcEnum"
              }
            ]
          }
        }
      ],
      "name": "OutpGnrcEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpGnrcValue": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "OutpGnrcValue",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpGnrcValue",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "outpGnrcValue"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpGnrcValue",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "output": "outpGnrcValue"
              }
            ]
          }
        }
      ],
      "name": "OutpGnrcValue",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpParam": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "OutpParam",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpParam"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpParam"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpParam"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpParam"
          }
        }
      ],
      "name": "OutpParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpParamDescr": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "OutpParamDescr",
          "parameters": [
            {
              "$tag": "_InputParam",
              "description": "Test Descr",
              "input": "InOutpParamDescr"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpParamDescr"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "parameters": [
            {
              "$tag": "_InputParam",
              "description": "Test Descr",
              "input": "InOutpParamDescr"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpParamDescr"
          }
        }
      ],
      "name": "OutpParamDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpParamModDmn": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "OutpParamModDmn",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpParamModDmn",
              "modifiers": [
                {
                  "$tag": "_ModifierDictionary",
                  "by": "DomOutpParamModDmn",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Dict"
                  },
                  "typeKind": {
                    "$tag": "_SimpleKind",
                    "value": "Domain"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "DomOutpParamModDmn"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpParamModDmn",
              "modifiers": [
                {
                  "$tag": "_ModifierDictionary",
                  "by": "DomOutpParamModDmn",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Dict"
                  },
                  "typeKind": {
                    "$tag": "_SimpleKind",
                    "value": "Domain"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "DomOutpParamModDmn"
          }
        }
      ],
      "name": "OutpParamModDmn",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpParamModParam": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "OutpParamModParam",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpParamModParam",
              "modifiers": [
                {
                  "$tag": "_ModifierTypeParam",
                  "by": "mod",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Param"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "DomOutpParamModParam"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpParamModParam",
              "modifiers": [
                {
                  "$tag": "_ModifierTypeParam",
                  "by": "mod",
                  "modifierKind": {
                    "$tag": "_ModifierKind",
                    "value": "Param"
                  }
                }
              ]
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "output": "DomOutpParamModParam"
          }
        }
      ],
      "name": "OutpParamModParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "mod"
        }
      ]
    },
    "OutpParamTypeDescr": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "OutpParamTypeDescr",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpParamTypeDescr"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "description": "Test Descr",
            "dual": "FldOutpParamTypeDescr"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpParamTypeDescr"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "description": "Test Descr",
            "dual": "FldOutpParamTypeDescr"
          }
        }
      ],
      "name": "OutpParamTypeDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpPrntGnrc": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "OutpPrntGnrc",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpPrntGnrc",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "prnt_outpPrntGnrc",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "EnumOutpPrntGnrc"
              }
            ]
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "RefOutpPrntGnrc",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "prnt_outpPrntGnrc",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "EnumOutpPrntGnrc"
              }
            ]
          }
        }
      ],
      "name": "OutpPrntGnrc",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpPrntParam": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "PrntOutpPrntParam",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "PrntOutpPrntParamIn"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpPrntParam"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "OutpPrntParam",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpPrntParam"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpPrntParam"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "InOutpPrntParam"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpPrntParam"
          }
        }
      ],
      "name": "OutpPrntParam",
      "parent": {
        "$tag": "_OutputBase",
        "output": "PrntOutpPrntParam"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "ParentGnrcPrntEnumChildDual": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumChildDual",
          "name": "gnrcPrntEnumChildDualParent"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumChildDualParent"
        }
      ],
      "name": "ParentGnrcPrntEnumChildDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "ParentGnrcPrntEnumChildInp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumChildInp",
          "name": "gnrcPrntEnumChildInpParent"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumChildInpParent"
        }
      ],
      "name": "ParentGnrcPrntEnumChildInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "ParentGnrcPrntEnumChildOutp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumChildOutp",
          "name": "gnrcPrntEnumChildOutpParent"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumChildOutpParent"
        }
      ],
      "name": "ParentGnrcPrntEnumChildOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "ParentGnrcPrntEnumPrntDual": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumPrntDual",
          "name": "gnrcPrntEnumPrntDualParent"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumPrntDualParent"
        }
      ],
      "name": "ParentGnrcPrntEnumPrntDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "ParentGnrcPrntEnumPrntInp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumPrntInp",
          "name": "gnrcPrntEnumPrntInpParent"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumPrntInpParent"
        }
      ],
      "name": "ParentGnrcPrntEnumPrntInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "ParentGnrcPrntEnumPrntOutp": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentGnrcPrntEnumPrntOutp",
          "name": "gnrcPrntEnumPrntOutpParent"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "gnrcPrntEnumPrntOutpParent"
        }
      ],
      "name": "ParentGnrcPrntEnumPrntOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "ParentOutpCnstEnumPrnt": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentOutpCnstEnumPrnt",
          "name": "parentOutpCnstEnumPrnt"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "parentOutpCnstEnumPrnt"
        }
      ],
      "name": "ParentOutpCnstEnumPrnt",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "ParentOutpCnstPrntEnum": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "ParentOutpCnstPrntEnum",
          "name": "parentOutpCnstPrntEnum"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "parentOutpCnstPrntEnum"
        }
      ],
      "name": "ParentOutpCnstPrntEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "PrntAltDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntAltDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntAltDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntAltDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "PrntAltDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefPrntAltDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntAltInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntAltInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "PrntAltInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "parent",
          "object": "RefPrntAltInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "PrntAltInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefPrntAltInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "PrntAltOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntAltOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "PrntAltOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "RefPrntAltOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "PrntAltOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefPrntAltOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "PrntCnstAltDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstAltDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstAltDualInp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstAltDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstAltDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstAltDualOutp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstAltDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstAltDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstAltObjDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstAltObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstAltObjDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstAltObjInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "PrntCnstAltObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "name": "PrntCnstAltObjInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "PrntCnstAltObjOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "PrntCnstAltObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "name": "PrntCnstAltObjOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "PrntCnstFieldDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstFieldDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstFieldDualInp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstFieldDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstFieldDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstFieldDualOutp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstFieldDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstFieldDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstFieldObjDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstFieldObjDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstFieldObjDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstFieldObjInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "PrntCnstFieldObjInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "name": "PrntCnstFieldObjInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "PrntCnstFieldObjOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "PrntCnstFieldObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "name": "PrntCnstFieldObjOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "PrntCnstPrntDualPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntDualPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstPrntDualPrntDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstPrntDualPrntInp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntDualPrntInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstPrntDualPrntInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstPrntDualPrntOutp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntDualPrntOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstPrntDualPrntOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstPrntObjPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "PrntCnstPrntObjPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "PrntCnstPrntObjPrntDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntCnstPrntObjPrntInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "PrntCnstPrntObjPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "name": "PrntCnstPrntObjPrntInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "PrntCnstPrntObjPrntOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "PrntCnstPrntObjPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "name": "PrntCnstPrntObjPrntOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "PrntDescrDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntDescrDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntDescrDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "PrntDescrDual",
      "parent": {
        "$tag": "_DualBase",
        "description": "Test Descr",
        "dual": "RefPrntDescrDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntDescrInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntDescrInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "parent",
          "object": "RefPrntDescrInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "PrntDescrInp",
      "parent": {
        "$tag": "_InputBase",
        "description": "Test Descr",
        "input": "RefPrntDescrInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "PrntDescrOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntDescrOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "RefPrntDescrOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "PrntDescrOutp",
      "parent": {
        "$tag": "_OutputBase",
        "description": "Test Descr",
        "output": "RefPrntDescrOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "PrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "PrntDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefPrntDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "PrntDualDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefPrntDualDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "PrntDualInp",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefPrntDualInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "PrntDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "PrntDualOutp",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefPrntDualOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "PrntFieldDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntFieldDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntFieldDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "PrntFieldDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "PrntFieldDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefPrntFieldDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "PrntFieldInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntFieldInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "parent",
          "object": "RefPrntFieldInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "PrntFieldInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "PrntFieldInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefPrntFieldInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "PrntFieldOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntFieldOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "RefPrntFieldOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "PrntFieldOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "PrntFieldOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefPrntFieldOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "PrntInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "parent",
          "object": "RefPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "PrntInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefPrntInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "PrntOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "RefPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "PrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefPrntOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "PrntOutpFieldEnumPrnt": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "PrntOutpFieldEnumPrnt",
          "name": "prnt_outpFieldEnumPrnt"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "prnt_outpFieldEnumPrnt"
        }
      ],
      "name": "PrntOutpFieldEnumPrnt",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "PrntOutpPrntGnrc": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "PrntOutpPrntGnrc",
          "name": "prnt_outpPrntGnrc"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "prnt_outpPrntGnrc"
        }
      ],
      "name": "PrntOutpPrntGnrc",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "PrntOutpPrntParam": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "PrntOutpPrntParam",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "PrntOutpPrntParamIn"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpPrntParam"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "PrntOutpPrntParamIn"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpPrntParam"
          }
        }
      ],
      "name": "PrntOutpPrntParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "PrntOutpPrntParamIn": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "PrntOutpPrntParamIn",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "parent",
          "object": "PrntOutpPrntParamIn",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "parent",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "PrntOutpPrntParamIn",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "PrntParamDiffDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntParamDiffDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "b"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "PrntParamDiffDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "a"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "a"
          }
        }
      ],
      "name": "PrntParamDiffDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefPrntParamDiffDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "typeParam": "a"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "a"
        }
      ]
    },
    "PrntParamDiffInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntParamDiffInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "b"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "PrntParamDiffInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "a"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "a"
          }
        }
      ],
      "name": "PrntParamDiffInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefPrntParamDiffInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "typeParam": "a"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "a"
        }
      ]
    },
    "PrntParamDiffOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntParamDiffOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "b"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "PrntParamDiffOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "a"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "a"
          }
        }
      ],
      "name": "PrntParamDiffOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefPrntParamDiffOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "typeParam": "a"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "a"
        }
      ]
    },
    "PrntParamSameDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntParamSameDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "a"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "PrntParamSameDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "a"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "a"
          }
        }
      ],
      "name": "PrntParamSameDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefPrntParamSameDual",
        "typeArgs": [
          {
            "$tag": "_DualArg",
            "typeParam": "a"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "a"
        }
      ]
    },
    "PrntParamSameInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntParamSameInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "a"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "PrntParamSameInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "a"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "a"
          }
        }
      ],
      "name": "PrntParamSameInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefPrntParamSameInp",
        "typeArgs": [
          {
            "$tag": "_InputArg",
            "typeParam": "a"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "a"
        }
      ]
    },
    "PrntParamSameOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntParamSameOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "a"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "PrntParamSameOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "a"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "a"
          }
        }
      ],
      "name": "PrntParamSameOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefPrntParamSameOutp",
        "typeArgs": [
          {
            "$tag": "_OutputArg",
            "typeParam": "a"
          }
        ]
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "a"
        }
      ]
    },
    "RefCnstAltDmnDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefCnstAltDmnDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstAltDmnDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstAltDmnInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefCnstAltDmnInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstAltDmnInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstAltDmnOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefCnstAltDmnOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstAltDmnOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstAltDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefCnstAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstAltDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstAltDualDual"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstAltDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefCnstAltDualInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstAltDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstAltDualInp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstAltDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefCnstAltDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstAltDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstAltDualOutp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstAltObjDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefCnstAltObjDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstAltObjDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstAltObjDual"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstAltObjInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefCnstAltObjInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstAltObjInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Input"
            },
            "typeName": "PrntCnstAltObjInp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstAltObjOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefCnstAltObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstAltObjOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "PrntCnstAltObjOutp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstFieldDmnDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "RefCnstFieldDmnDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstFieldDmnDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstFieldDmnInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "RefCnstFieldDmnInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstFieldDmnInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstFieldDmnOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefCnstFieldDmnOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstFieldDmnOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstFieldDualDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "RefCnstFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstFieldDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstFieldDualDual"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstFieldDualInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "RefCnstFieldDualInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstFieldDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstFieldDualInp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstFieldDualOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefCnstFieldDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstFieldDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstFieldDualOutp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstFieldObjDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "RefCnstFieldObjDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstFieldObjDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstFieldObjDual"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstFieldObjInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "RefCnstFieldObjInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstFieldObjInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Input"
            },
            "typeName": "PrntCnstFieldObjInp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstFieldObjOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefCnstFieldObjOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefCnstFieldObjOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "PrntCnstFieldObjOutp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstPrntDualPrntDual": {
      "$tag": "_TypeDual",
      "name": "RefCnstPrntDualPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstPrntDualPrntDual"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstPrntDualPrntInp": {
      "$tag": "_TypeInput",
      "name": "RefCnstPrntDualPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstPrntDualPrntInp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstPrntDualPrntOutp": {
      "$tag": "_TypeOutput",
      "name": "RefCnstPrntDualPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstPrntDualPrntOutp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstPrntObjPrntDual": {
      "$tag": "_TypeDual",
      "name": "RefCnstPrntObjPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Dual"
            },
            "typeName": "PrntCnstPrntObjPrntDual"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstPrntObjPrntInp": {
      "$tag": "_TypeInput",
      "name": "RefCnstPrntObjPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Input"
            },
            "typeName": "PrntCnstPrntObjPrntInp"
          },
          "name": "ref"
        }
      ]
    },
    "RefCnstPrntObjPrntOutp": {
      "$tag": "_TypeOutput",
      "name": "RefCnstPrntObjPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Output"
            },
            "typeName": "PrntCnstPrntObjPrntOutp"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltArgDescrDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcAltArgDescrDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltArgDescrDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltArgDescrInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcAltArgDescrInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltArgDescrInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltArgDescrOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcAltArgDescrOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltArgDescrOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltArgDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcAltArgDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltArgDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltArgInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcAltArgInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltArgInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltArgOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcAltArgOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltArgOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcAltDualDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcAltDualInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcAltDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltModParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "object": "RefGnrcAltModParamDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltModParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "mod"
        }
      ]
    },
    "RefGnrcAltModParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "object": "RefGnrcAltModParamInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltModParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "mod"
        }
      ]
    },
    "RefGnrcAltModParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "object": "RefGnrcAltModParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "collections": [
            {
              "$tag": "_ModifierTypeParam",
              "by": "mod",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Param"
              }
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltModParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "mod"
        }
      ]
    },
    "RefGnrcAltModStrDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "*",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "object": "RefGnrcAltModStrDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "*",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltModStrDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltModStrInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "*",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "object": "RefGnrcAltModStrInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "*",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltModStrInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltModStrOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "*",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "object": "RefGnrcAltModStrOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "collections": [
            {
              "$tag": "_ModifierDictionary",
              "by": "*",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Basic"
              }
            }
          ],
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltModStrOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcAltParamDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcAltParamInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Input"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcAltParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Output"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltSmplDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcAltSmplDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltSmplDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltSmplInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcAltSmplInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltSmplInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcAltSmplOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcAltSmplOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcAltSmplOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcFieldArgDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcFieldArgDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcFieldArgDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcFieldArgInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcFieldArgInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcFieldArgInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcFieldArgOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcFieldArgOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcFieldArgOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcFieldDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcFieldDualDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcFieldDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcFieldDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcFieldDualInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcFieldDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcFieldDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcFieldDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcFieldDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcFieldParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcFieldParamDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcFieldParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcFieldParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcFieldParamInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcFieldParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Input"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcFieldParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcFieldParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcFieldParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Output"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntArgDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcPrntArgDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcPrntArgDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntArgInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcPrntArgInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcPrntArgInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntArgOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcPrntArgOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcPrntArgOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcPrntDualDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcPrntDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntDualInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcPrntDualInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcPrntDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntDualOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcPrntDualOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcPrntDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntDualPrntDual": {
      "$tag": "_TypeDual",
      "name": "RefGnrcPrntDualPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntDualPrntInp": {
      "$tag": "_TypeInput",
      "name": "RefGnrcPrntDualPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntDualPrntOutp": {
      "$tag": "_TypeOutput",
      "name": "RefGnrcPrntDualPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntParamDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefGnrcPrntParamDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcPrntParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntParamInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefGnrcPrntParamInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcPrntParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Input"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntParamOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefGnrcPrntParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "ref"
          }
        }
      ],
      "name": "RefGnrcPrntParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Output"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntParamPrntDual": {
      "$tag": "_TypeDual",
      "name": "RefGnrcPrntParamPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Dual"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntParamPrntInp": {
      "$tag": "_TypeInput",
      "name": "RefGnrcPrntParamPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Input"
          },
          "name": "ref"
        }
      ]
    },
    "RefGnrcPrntParamPrntOutp": {
      "$tag": "_TypeOutput",
      "name": "RefGnrcPrntParamPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "typeParam": "ref"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Output"
          },
          "name": "ref"
        }
      ]
    },
    "RefOutpCnstDomEnum": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefOutpCnstDomEnum",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "RefOutpCnstDomEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Domain"
            },
            "typeName": "JustOutpCnstDomEnum"
          },
          "name": "type"
        }
      ]
    },
    "RefOutpCnstEnum": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefOutpCnstEnum",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "RefOutpCnstEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "EnumOutpCnstEnum"
          },
          "name": "type"
        }
      ]
    },
    "RefOutpCnstEnumPrnt": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefOutpCnstEnumPrnt",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "RefOutpCnstEnumPrnt",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "ParentOutpCnstEnumPrnt"
          },
          "name": "type"
        }
      ]
    },
    "RefOutpCnstPrntEnum": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefOutpCnstPrntEnum",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "RefOutpCnstPrntEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "EnumOutpCnstPrntEnum"
          },
          "name": "type"
        }
      ]
    },
    "RefOutpGnrcEnum": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefOutpGnrcEnum",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "RefOutpGnrcEnum",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Enum"
          },
          "name": "type"
        }
      ]
    },
    "RefOutpGnrcValue": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefOutpGnrcValue",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "RefOutpGnrcValue",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Internal"
            },
            "typeName": "_Enum"
          },
          "name": "type"
        }
      ]
    },
    "RefOutpPrntGnrc": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "RefOutpPrntGnrc",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "RefOutpPrntGnrc",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Enum"
            },
            "typeName": "PrntOutpPrntGnrc"
          },
          "name": "type"
        }
      ]
    },
    "RefPrntAltDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntAltDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntAltDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "parent",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "RefPrntAltDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "RefPrntAltInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntAltInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "parent",
          "object": "RefPrntAltInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "parent",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "RefPrntAltInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "RefPrntAltOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntAltOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "RefPrntAltOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "parent",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "RefPrntAltOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "RefPrntDescrDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntDescrDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntDescrDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "parent",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "RefPrntDescrDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "RefPrntDescrInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntDescrInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "parent",
          "object": "RefPrntDescrInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "parent",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "RefPrntDescrInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "RefPrntDescrOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntDescrOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "RefPrntDescrOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "parent",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "RefPrntDescrOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "RefPrntDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "parent",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "RefPrntDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "RefPrntDualDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntDualDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "parent",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "RefPrntDualDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "RefPrntDualInp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntDualInp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "parent",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "RefPrntDualInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "RefPrntDualOutp": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntDualOutp",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "parent",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "RefPrntDualOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "RefPrntFieldDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntFieldDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "parent",
          "object": "RefPrntFieldDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "parent",
          "type": {
            "$tag": "_DualBase",
            "dual": "Number"
          }
        }
      ],
      "name": "RefPrntFieldDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "RefPrntFieldInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntFieldInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "parent",
          "object": "RefPrntFieldInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "parent",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "RefPrntFieldInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "RefPrntFieldOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntFieldOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "RefPrntFieldOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "parent",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "RefPrntFieldOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "RefPrntInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "parent",
          "object": "RefPrntInp",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "parent",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "RefPrntInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "RefPrntOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "parent",
          "object": "RefPrntOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "parent",
          "type": {
            "$tag": "_OutputBase",
            "output": "Number"
          }
        }
      ],
      "name": "RefPrntOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "RefPrntParamDiffDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntParamDiffDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "b"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "b"
          }
        }
      ],
      "name": "RefPrntParamDiffDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "b"
        }
      ]
    },
    "RefPrntParamDiffInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntParamDiffInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "b"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "b"
          }
        }
      ],
      "name": "RefPrntParamDiffInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "b"
        }
      ]
    },
    "RefPrntParamDiffOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntParamDiffOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "b"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "b"
          }
        }
      ],
      "name": "RefPrntParamDiffOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "b"
        }
      ]
    },
    "RefPrntParamSameDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "RefPrntParamSameDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "a"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "a"
          }
        }
      ],
      "name": "RefPrntParamSameDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "a"
        }
      ]
    },
    "RefPrntParamSameInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "RefPrntParamSameInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "a"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "a"
          }
        }
      ],
      "name": "RefPrntParamSameInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "a"
        }
      ]
    },
    "RefPrntParamSameOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "RefPrntParamSameOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "a"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "a"
          }
        }
      ],
      "name": "RefPrntParamSameOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "a"
        }
      ]
    }
  }
}