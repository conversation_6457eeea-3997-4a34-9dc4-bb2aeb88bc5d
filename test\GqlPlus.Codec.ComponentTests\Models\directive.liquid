﻿<article>
  {%- assign dir = directive[1] %}
  {%- render 'heading', dec: dir, label: "Directive" -%}
  {%- if dir.parameters -%}
  <p>
    <sub>Params:</sub>
    <ul>
      {%- for parameter in dir.parameters %}
      <li>{{ parameter.type.input }}</li>
      {%- endfor %}
    </ul>
  </p>
  {%- endif -%}
  <p>
    <sub>Locations:</sub>
    {%- assign locs = dir.locations | sort %}
    {%- for loc in locs %} {{ loc[0] }}
    {%- unless forloop.last -%},{%- endunless -%}
    {%- endfor %}
  </p>
  <p> Repeatable: {{ dir.repeatable }} </p>
</article>