﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstPrntDualPrntInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstPrntDualPrntInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltCnstPrntDualPrntInp
        type: !_InputBase
          input: Number
    fields:
      - !_InputField
        name: alt
        type: !_InputBase
          input: Number
    name: AltCnstPrntDualPrntInp
    parent: !_DualBase
      dual: PrntCnstPrntDualPrntInp
    typeKind: !_TypeKind Input
  !_Identifier CnstPrntDualPrntInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstPrntDualPrntInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltCnstPrntDualPrntInp
        type: !_InputBase
          input: Number
    name: CnstPrntDualPrntInp
    parent: !_InputBase
      input: RefCnstPrntDualPrntInp
      typeArgs:
        - !_InputArg
          input: AltCnstPrntDualPrntInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstPrntDualPrntInp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstPrntDualPrntInp
        type: !_DualBase
          dual: String
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    name: PrntCnstPrntDualPrntInp
    typeKind: !_TypeKind Dual
  !_Identifier RefCnstPrntDualPrntInp: !_TypeInput
    name: RefCnstPrntDualPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Dual
          typeName: PrntCnstPrntDualPrntInp
        name: ref