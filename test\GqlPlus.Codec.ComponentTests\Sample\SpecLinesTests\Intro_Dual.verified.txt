﻿!_Schema
errors:
  - !_Error
    _kind: !_TokenKind End
    _message: In _TypeDual can't get model for type '_TypeObject'
  - !_Error
    _kind: !_TokenKind End
    _message: In _DualBase can't get model for type '_ObjBase'
  - !_Error
    _kind: !_TokenKind End
    _message: In _DualTypeParam can't get model for type '_ObjTypeParam'
  - !_Error
    _kind: !_TokenKind End
    _message: In _DualField can't get model for type '_Field'
  - !_Error
    _kind: !_TokenKind End
    _message: In _DualAlternate can't get model for type '_Alternate'
  - !_Error
    _kind: !_TokenKind End
    _message: In _DualType<PERSON>rg can't get model for type '_ObjTypeArg'
types: !_Map_Type
  !_Identifier _DualAlternate: !_TypeOutput
    name: _DualAlternate
    parent: !_OutputBase
      output: _Alternate
      typeArgs:
        - !_OutputArg
          output: _DualBase
    typeKind: !_TypeKind Output
  !_Identifier _DualBase: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: dual
        object: _DualBase
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: dual
        type: !_OutputBase
          output: _Identifier
    name: _DualBase
    parent: !_OutputBase
      output: _ObjBase
      typeArgs:
        - !_OutputArg
          output: _DualTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _DualField: !_TypeOutput
    name: _DualField
    parent: !_OutputBase
      output: _Field
      typeArgs:
        - !_OutputArg
          output: _DualBase
    typeKind: !_TypeKind Output
  !_Identifier _DualTypeArg: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: dual
        object: _DualTypeArg
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: dual
        type: !_OutputBase
          output: _Identifier
    name: _DualTypeArg
    parent: !_OutputBase
      output: _ObjTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _DualTypeParam: !_TypeOutput
    name: _DualTypeParam
    parent: !_OutputBase
      output: _ObjTypeParam
      typeArgs:
        - !_OutputArg
          label: Dual
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _TypeDual: !_TypeOutput
    name: _TypeDual
    parent: !_OutputBase
      output: _TypeObject
      typeArgs:
        - !_OutputArg
          label: Dual
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
        - !_OutputArg
          output: _DualBase
        - !_OutputArg
          output: _DualTypeParam
        - !_OutputArg
          output: _DualField
        - !_OutputArg
          output: _DualAlternate
    typeKind: !_TypeKind Output