﻿!_Schema
types: !_Map_Type
  !_Identifier EnumOutpGnrcEnum: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumOutpGnrcEnum
        name: outpGnrcEnum
    items:
      - !_Aliased
        name: outpGnrcEnum
    name: EnumOutpGnrcEnum
    typeKind: !_TypeKind Enum
  !_Identifier OutpGnrcEnum: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: OutpGnrcEnum
        type: !_OutputBase
          output: RefOutpGnrcEnum
          typeArgs:
            - !_OutputArg
              label: outpGnrcEnum
              typeKind: !_SimpleKind Enum
              typeName: EnumOutpGnrcEnum
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: RefOutpGnrcEnum
          typeArgs:
            - !_OutputArg
              label: outpGnrcEnum
              typeKind: !_SimpleKind Enum
              typeName: EnumOutpGnrcEnum
    name: OutpGnrcEnum
    typeKind: !_TypeKind Output
  !_Identifier RefOutpGnrcEnum: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: RefOutpGnrcEnum
        type: !_OutputBase
          typeParam: type
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: type
    name: RefOutpGnrcEnum
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Enum
        name: type