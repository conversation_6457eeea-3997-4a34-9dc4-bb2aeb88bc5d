﻿{
  "$tag": "_Schema",
  "types": {
    "$tag": "_Map_Type",
    "InpFieldDescrNmbr": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": 42,
          "description": "Test Descr",
          "name": "field",
          "object": "InpFieldDescrNmbr",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "default": 42,
          "description": "Test Descr",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InpFieldDescrNmbr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    }
  }
}