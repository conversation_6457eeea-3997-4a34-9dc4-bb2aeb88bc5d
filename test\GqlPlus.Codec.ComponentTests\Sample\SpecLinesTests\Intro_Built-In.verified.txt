﻿!_Schema
types: !_Map_Type
  !_Identifier _Collections: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _Collections
        type: !_OutputBase
          output: _Modifier
          typeArgs:
            - !_OutputArg
              label: List
              typeKind: !_SimpleKind Enum
              typeName: _ModifierKind
      - !_ObjectFor(_OutputAlternate)
        object: _Collections
        type: !_OutputBase
          output: _ModifierKeyed
          typeArgs:
            - !_OutputArg
              label: Dictionary
              typeKind: !_SimpleKind Enum
              typeName: _ModifierKind
      - !_ObjectFor(_OutputAlternate)
        object: _Collections
        type: !_OutputBase
          output: _ModifierKeyed
          typeArgs:
            - !_OutputArg
              label: TypeParam
              typeKind: !_SimpleKind Enum
              typeName: _ModifierKind
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _Modifier
          typeArgs:
            - !_OutputArg
              label: List
              typeKind: !_SimpleKind Enum
              typeName: _ModifierKind
      - !_OutputAlternate
        type: !_OutputBase
          output: _ModifierKeyed
          typeArgs:
            - !_OutputArg
              label: Dictionary
              typeKind: !_SimpleKind Enum
              typeName: _ModifierKind
      - !_OutputAlternate
        type: !_OutputBase
          output: _ModifierKeyed
          typeArgs:
            - !_OutputArg
              label: TypeParam
              typeKind: !_SimpleKind Enum
              typeName: _ModifierKind
    name: _Collections
    typeKind: !_TypeKind Output
  !_Identifier _Constant: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _Constant
        type: !_OutputBase
          output: _SimpleValue
      - !_ObjectFor(_OutputAlternate)
        object: _Constant
        type: !_OutputBase
          output: _ConstantList
      - !_ObjectFor(_OutputAlternate)
        object: _Constant
        type: !_OutputBase
          output: _ConstantMap
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _SimpleValue
      - !_OutputAlternate
        type: !_OutputBase
          output: _ConstantList
      - !_OutputAlternate
        type: !_OutputBase
          output: _ConstantMap
    name: _Constant
    typeKind: !_TypeKind Output
  !_Identifier _ConstantList: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        collections:
          - !_Modifier
            modifierKind: !_ModifierKind List
        object: _ConstantList
        type: !_OutputBase
          output: _Constant
    alternates:
      - !_OutputAlternate
        collections:
          - !_Modifier
            modifierKind: !_ModifierKind List
        type: !_OutputBase
          output: _Constant
    name: _ConstantList
    typeKind: !_TypeKind Output
  !_Identifier _ConstantMap: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        collections:
          - !_ModifierDictionary
            by: Simple
            modifierKind: !_ModifierKind Dict
            typeKind: !_SimpleKind Internal
        object: _ConstantMap
        type: !_OutputBase
          output: _Constant
    alternates:
      - !_OutputAlternate
        collections:
          - !_ModifierDictionary
            by: Simple
            modifierKind: !_ModifierKind Dict
            typeKind: !_SimpleKind Internal
        type: !_OutputBase
          output: _Constant
    name: _ConstantMap
    typeKind: !_TypeKind Output
  !_Identifier _Internal: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _Internal
        type: !_OutputBase
          output: Null
      - !_ObjectFor(_OutputAlternate)
        object: _Internal
        type: !_OutputBase
          output: Object
      - !_ObjectFor(_OutputAlternate)
        object: _Internal
        type: !_OutputBase
          output: Void
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: Null
      - !_OutputAlternate
        type: !_OutputBase
          output: Object
      - !_OutputAlternate
        type: !_OutputBase
          output: Void
    name: _Internal
    typeKind: !_TypeKind Output
  !_Identifier _Modifier: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: modifierKind
        object: _Modifier
        type: !_OutputBase
          typeParam: kind
    fields:
      - !_OutputField
        name: modifierKind
        type: !_OutputBase
          typeParam: kind
    name: _Modifier
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: _ModifierKind
        name: kind
  !_Identifier _ModifierKeyed: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: modifierKind
        object: _Modifier
        type: !_OutputBase
          typeParam: kind
      - !_ObjectFor(_OutputField)
        name: by
        object: _ModifierKeyed
        type: !_OutputBase
          output: _TypeSimple
      - !_ObjectFor(_OutputField)
        name: optional
        object: _ModifierKeyed
        type: !_OutputBase
          output: Boolean
    fields:
      - !_OutputField
        name: by
        type: !_OutputBase
          output: _TypeSimple
      - !_OutputField
        name: optional
        type: !_OutputBase
          output: Boolean
    name: _ModifierKeyed
    parent: !_OutputBase
      output: _Modifier
      typeArgs:
        - !_OutputArg
          typeParam: kind
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: _ModifierKind
        name: kind
  !_Identifier _ModifierKind: !_TypeEnum
    allItems:
      - !_EnumLabel
        aliases: [Optional]
        enum: _ModifierKind
        name: Opt
      - !_EnumLabel
        enum: _ModifierKind
        name: List
      - !_EnumLabel
        aliases: [Dictionary]
        enum: _ModifierKind
        name: Dict
      - !_EnumLabel
        aliases: [TypeParam]
        enum: _ModifierKind
        name: Param
    items:
      - !_Aliased
        aliases: [Optional]
        name: Opt
      - !_Aliased
        name: List
      - !_Aliased
        aliases: [Dictionary]
        name: Dict
      - !_Aliased
        aliases: [TypeParam]
        name: Param
    name: _ModifierKind
    typeKind: !_TypeKind Enum
  !_Identifier _Modifiers: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _Modifiers
        type: !_OutputBase
          output: _Modifier
          typeArgs:
            - !_OutputArg
              label: Optional
              typeKind: !_SimpleKind Enum
              typeName: _ModifierKind
      - !_ObjectFor(_OutputAlternate)
        object: _Modifiers
        type: !_OutputBase
          output: _Collections
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _Modifier
          typeArgs:
            - !_OutputArg
              label: Optional
              typeKind: !_SimpleKind Enum
              typeName: _ModifierKind
      - !_OutputAlternate
        type: !_OutputBase
          output: _Collections
    name: _Modifiers
    typeKind: !_TypeKind Output
  !_Identifier _SimpleValue: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _SimpleValue
        type: !_OutputBase
          output: _DomainValue
          typeArgs:
            - !_OutputArg
              label: Boolean
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_OutputArg
              output: Boolean
      - !_ObjectFor(_OutputAlternate)
        object: _SimpleValue
        type: !_OutputBase
          output: _DomainValue
          typeArgs:
            - !_OutputArg
              label: Enum
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_OutputArg
              output: _EnumValue
      - !_ObjectFor(_OutputAlternate)
        object: _SimpleValue
        type: !_OutputBase
          output: _DomainValue
          typeArgs:
            - !_OutputArg
              label: Number
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_OutputArg
              output: Number
      - !_ObjectFor(_OutputAlternate)
        object: _SimpleValue
        type: !_OutputBase
          output: _DomainValue
          typeArgs:
            - !_OutputArg
              label: String
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_OutputArg
              output: String
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _DomainValue
          typeArgs:
            - !_OutputArg
              label: Boolean
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_OutputArg
              output: Boolean
      - !_OutputAlternate
        type: !_OutputBase
          output: _DomainValue
          typeArgs:
            - !_OutputArg
              label: Enum
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_OutputArg
              output: _EnumValue
      - !_OutputAlternate
        type: !_OutputBase
          output: _DomainValue
          typeArgs:
            - !_OutputArg
              label: Number
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_OutputArg
              output: Number
      - !_OutputAlternate
        type: !_OutputBase
          output: _DomainValue
          typeArgs:
            - !_OutputArg
              label: String
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_OutputArg
              output: String
    name: _SimpleValue
    typeKind: !_TypeKind Output