﻿!_Schema
types: !_Map_Type
  !_Identifier PrntParamDiffDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefPrntParamDiffDual
        type: !_DualBase
          typeParam: b
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: PrntParamDiffDual
        type: !_DualBase
          typeParam: a
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          typeParam: a
    name: PrntParamDiffDual
    parent: !_DualBase
      dual: RefPrntParamDiffDual
      typeArgs:
        - !_DualArg
          typeParam: a
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: a
  !_Identifier RefPrntParamDiffDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefPrntParamDiffDual
        type: !_DualBase
          typeParam: b
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: b
    name: RefPrntParamDiffDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: b