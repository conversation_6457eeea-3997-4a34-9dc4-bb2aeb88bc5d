﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstAltDualInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstAltDualInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltCnstAltDualInp
        type: !_InputBase
          input: Number
    fields:
      - !_InputField
        name: alt
        type: !_InputBase
          input: Number
    name: AltCnstAltDualInp
    parent: !_DualBase
      dual: PrntCnstAltDualInp
    typeKind: !_TypeKind Input
  !_Identifier CnstAltDualInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: CnstAltDualInp
        type: !_InputBase
          input: RefCnstAltDualInp
          typeArgs:
            - !_InputArg
              input: AltCnstAltDualInp
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: RefCnstAltDualInp
          typeArgs:
            - !_InputArg
              input: AltCnstAltDualInp
    name: CnstAltDualInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstAltDualInp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstAltDualInp
        type: !_DualBase
          dual: String
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    name: PrntCnstAltDualInp
    typeKind: !_TypeKind Dual
  !_Identifier RefCnstAltDualInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefCnstAltDualInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefCnstAltDualInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Dual
          typeName: PrntCnstAltDualInp
        name: ref