﻿{
  "$tag": "_Schema",
  "types": {
    "$tag": "_Map_Type",
    "InpFieldNmbr": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "default": 42,
          "name": "field",
          "object": "InpFieldNmbr",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "default": 42,
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "Number"
          }
        }
      ],
      "name": "InpFieldNmbr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    }
  }
}