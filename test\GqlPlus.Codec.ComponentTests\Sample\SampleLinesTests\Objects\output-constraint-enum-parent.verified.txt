﻿!_Schema
types: !_Map_Type
  !_Identifier EnumOutpCnstEnumPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentOutpCnstEnumPrnt
        name: parentOutpCnstEnumPrnt
      - !_EnumLabel
        enum: EnumOutpCnstEnumPrnt
        name: outpCnstEnumPrnt
    items:
      - !_Aliased
        name: outpCnstEnumPrnt
    name: EnumOutpCnstEnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentOutpCnstEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier OutpCnstEnumPrnt: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: OutpCnstEnumPrnt
        type: !_OutputBase
          output: RefOutpCnstEnumPrnt
          typeArgs:
            - !_OutputArg
              output: outpCnstEnumPrnt
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: RefOutpCnstEnumPrnt
          typeArgs:
            - !_OutputArg
              output: outpCnstEnumPrnt
    name: OutpCnstEnumPrnt
    typeKind: !_TypeKind Output
  !_Identifier ParentOutpCnstEnumPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentOutpCnstEnumPrnt
        name: parentOutpCnstEnumPrnt
    items:
      - !_Aliased
        name: parentOutpCnstEnumPrnt
    name: ParentOutpCnstEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier RefOutpCnstEnumPrnt: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: RefOutpCnstEnumPrnt
        type: !_OutputBase
          typeParam: type
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: type
    name: RefOutpCnstEnumPrnt
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: ParentOutpCnstEnumPrnt
        name: type