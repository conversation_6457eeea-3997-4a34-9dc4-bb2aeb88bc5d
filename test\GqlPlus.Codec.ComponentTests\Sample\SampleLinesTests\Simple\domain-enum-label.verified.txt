﻿!_Schema
types: !_Map_Type
  !_Identifier DmnEnumLabel: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumLabel
        exclude: false
        value: !_EnumValue
          label: dmnEnumLabel
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumLabel
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: dmnEnumLabel
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumLabel
    name: DmnEnumLabel
    typeKind: !_TypeKind Domain
  !_Identifier EnumDmnEnumLabel: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumLabel
        name: dmnEnumLabel
    items:
      - !_Aliased
        name: dmnEnumLabel
    name: EnumDmnEnumLabel
    typeKind: !_TypeKind Enum