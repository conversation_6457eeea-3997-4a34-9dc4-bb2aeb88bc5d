﻿!_Schema
types: !_Map_Type
  !_Identifier DmnStrPrnt: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: PrntDmnStrPrnt
        exclude: false
        pattern: b+
      - !_DomainItem(_DomainRegex)
        domain: DmnStrPrnt
        exclude: false
        pattern: a+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: a+
    name: DmnStrPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnStrPrnt
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnStrPrnt: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: PrntDmnStrPrnt
        exclude: false
        pattern: b+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: b+
    name: PrntDmnStrPrnt
    typeKind: !_TypeKind Domain