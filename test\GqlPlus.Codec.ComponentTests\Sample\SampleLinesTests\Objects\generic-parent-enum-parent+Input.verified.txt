﻿!_Schema
types: !_Map_Type
  !_Identifier EnumGnrcPrntEnumPrntInp: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentGnrcPrntEnumPrntInp
        name: gnrcPrntEnumPrntInpParent
      - !_EnumLabel
        enum: EnumGnrcPrntEnumPrntInp
        name: gnrcPrntEnumPrntInpLabel
    items:
      - !_Aliased
        name: gnrcPrntEnumPrntInpLabel
    name: EnumGnrcPrntEnumPrntInp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Enum
  !_Identifier FieldGnrcPrntEnumPrntInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: FieldGnrcPrntEnumPrntInp
        type: !_InputBase
          typeParam: ref
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          typeParam: ref
    name: FieldGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: ParentGnrcPrntEnumPrntInp
        name: ref
  !_Identifier GnrcPrntEnumPrntInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: FieldGnrcPrntEnumPrntInp
        type: !_InputBase
          input: EnumGnrcPrntEnumPrntInp
    name: GnrcPrntEnumPrntInp
    parent: !_InputBase
      input: FieldGnrcPrntEnumPrntInp
      typeArgs:
        - !_InputArg
          input: EnumGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Input
  !_Identifier ParentGnrcPrntEnumPrntInp: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentGnrcPrntEnumPrntInp
        name: gnrcPrntEnumPrntInpParent
    items:
      - !_Aliased
        name: gnrcPrntEnumPrntInpParent
    name: ParentGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Enum