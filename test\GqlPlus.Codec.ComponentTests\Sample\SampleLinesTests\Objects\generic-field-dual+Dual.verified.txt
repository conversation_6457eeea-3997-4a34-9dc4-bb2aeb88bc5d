﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcFieldDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcFieldDualDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcFieldDualDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcFieldDualDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: GnrcFieldDualDual
        type: !_DualBase
          dual: RefGnrcFieldDualDual
          typeArgs:
            - !_DualArg
              dual: AltGnrcFieldDualDual
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          dual: RefGnrcFieldDualDual
          typeArgs:
            - !_DualArg
              dual: AltGnrcFieldDualDual
    name: GnrcFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier RefGnrcFieldDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefGnrcFieldDualDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefGnrcFieldDualDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref