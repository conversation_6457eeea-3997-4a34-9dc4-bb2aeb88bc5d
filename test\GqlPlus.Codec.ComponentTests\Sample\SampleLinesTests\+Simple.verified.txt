﻿!_Schema
types: !_Map_Type
  !_Identifier DmnBoolDescr: !_DomainBoolean
    allItems:
      - !_DomainItem(_DomainTrueFalse)
        domain: DmnBoolDescr
        exclude: false
        value: true
    domainKind: !_DomainKind Boolean
    items:
      - !_DomainTrueFalse
        exclude: false
        value: true
    name: DmnBoolDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnBoolPrnt: !_DomainBoolean
    allItems:
      - !_DomainItem(_DomainTrueFalse)
        domain: PrntDmnBoolPrnt
        exclude: false
        value: true
      - !_DomainItem(_DomainTrueFalse)
        domain: DmnBoolPrnt
        exclude: false
        value: false
    domainKind: !_DomainKind Boolean
    items:
      - !_DomainTrueFalse
        exclude: false
        value: false
    name: DmnBoolPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnBoolPrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnBoolPrntDescr: !_DomainBoolean
    allItems:
      - !_DomainItem(_DomainTrueFalse)
        domain: PrntDmnBoolPrntDescr
        exclude: false
        value: true
      - !_DomainItem(_DomainTrueFalse)
        domain: DmnBoolPrntDescr
        exclude: false
        value: false
    domainKind: !_DomainKind Boolean
    items:
      - !_DomainTrueFalse
        exclude: false
        value: false
    name: DmnBoolPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnBoolPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumAll: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumAll
        exclude: false
        value: !_EnumValue
          label: '*'
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumAll
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: '*'
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumAll
    name: DmnEnumAll
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumAllDescr: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumAllDescr
        exclude: false
        value: !_EnumValue
          label: '*'
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumAllDescr
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: '*'
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumAllDescr
    name: DmnEnumAllDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumAllPrnt: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumAllPrnt
        exclude: false
        value: !_EnumValue
          label: '*'
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumAllPrnt
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: '*'
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumAllPrnt
    name: DmnEnumAllPrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumDescr: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumDescr
        exclude: false
        value: !_EnumValue
          label: dmnEnumDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumDescr
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: dmnEnumDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumDescr
    name: DmnEnumDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumLabel: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumLabel
        exclude: false
        value: !_EnumValue
          label: dmnEnumLabel
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumLabel
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: dmnEnumLabel
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumLabel
    name: DmnEnumLabel
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumPrnt: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: PrntDmnEnumPrnt
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumPrnt
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrnt
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumPrnt
        exclude: false
        value: !_EnumValue
          label: enum_dmnEnumPrnt
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrnt
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: enum_dmnEnumPrnt
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrnt
    name: DmnEnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnEnumPrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumPrntDescr: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: PrntDmnEnumPrntDescr
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumPrntDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrntDescr
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumPrntDescr
        exclude: false
        value: !_EnumValue
          label: enum_dmnEnumPrntDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrntDescr
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: enum_dmnEnumPrntDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrntDescr
    name: DmnEnumPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnEnumPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumValue: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumValue
        exclude: false
        value: !_EnumValue
          label: dmnEnumValue
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumValue
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: dmnEnumValue
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumValue
    name: DmnEnumValue
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumValuePrnt: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumValuePrnt
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumValuePrnt
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumValuePrnt
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumValuePrnt
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumValuePrnt
    name: DmnEnumValuePrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbrDescr: !_DomainNumber
    allItems:
      - !_DomainItem(_DomainRange)
        domain: DmnNmbrDescr
        exclude: false
        to: 2
    domainKind: !_DomainKind Number
    items:
      - !_DomainRange
        exclude: false
        to: 2
    name: DmnNmbrDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbrPrnt: !_DomainNumber
    allItems:
      - !_DomainItem(_DomainRange)
        domain: PrntDmnNmbrPrnt
        exclude: false
        to: 2
      - !_DomainItem(_DomainRange)
        domain: DmnNmbrPrnt
        exclude: false
        from: 2
    domainKind: !_DomainKind Number
    items:
      - !_DomainRange
        exclude: false
        from: 2
    name: DmnNmbrPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnNmbrPrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbrPrntDescr: !_DomainNumber
    allItems:
      - !_DomainItem(_DomainRange)
        domain: PrntDmnNmbrPrntDescr
        exclude: false
        to: 2
      - !_DomainItem(_DomainRange)
        domain: DmnNmbrPrntDescr
        exclude: false
        from: 2
    domainKind: !_DomainKind Number
    items:
      - !_DomainRange
        exclude: false
        from: 2
    name: DmnNmbrPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnNmbrPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnStrDescr: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: DmnStrDescr
        exclude: false
        pattern: a+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: a+
    name: DmnStrDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnStrPrnt: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: PrntDmnStrPrnt
        exclude: false
        pattern: b+
      - !_DomainItem(_DomainRegex)
        domain: DmnStrPrnt
        exclude: false
        pattern: a+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: a+
    name: DmnStrPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnStrPrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnStrPrntDescr: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: PrntDmnStrPrntDescr
        exclude: false
        pattern: b+
      - !_DomainItem(_DomainRegex)
        domain: DmnStrPrntDescr
        exclude: false
        pattern: a+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: a+
    name: DmnStrPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnStrPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier DupDmnEnumUnqPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: DupDmnEnumUnqPrnt
        name: dmnEnumUnqPrnt
      - !_EnumLabel
        enum: DupDmnEnumUnqPrnt
        name: dup_dmnEnumUnqPrnt
    items:
      - !_Aliased
        name: dmnEnumUnqPrnt
      - !_Aliased
        name: dup_dmnEnumUnqPrnt
    name: DupDmnEnumUnqPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumDescr: !_TypeEnum
    allItems:
      - !_EnumLabel
        description: 'Enum Descr'
        enum: EnumDescr
        name: enumDescr
    items:
      - !_Aliased
        description: 'Enum Descr'
        name: enumDescr
    name: EnumDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumAll: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumAll
        name: dmnEnumAll
      - !_EnumLabel
        enum: EnumDmnEnumAll
        name: enum_dmnEnumAll
    items:
      - !_Aliased
        name: dmnEnumAll
      - !_Aliased
        name: enum_dmnEnumAll
    name: EnumDmnEnumAll
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumAllDescr: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumAllDescr
        name: dmnEnumAllDescr
      - !_EnumLabel
        enum: EnumDmnEnumAllDescr
        name: enum_dmnEnumAllDescr
    items:
      - !_Aliased
        name: dmnEnumAllDescr
      - !_Aliased
        name: enum_dmnEnumAllDescr
    name: EnumDmnEnumAllDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumAllPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntDmnEnumAllPrnt
        name: prnt_dmnEnumAllPrnt
      - !_EnumLabel
        enum: EnumDmnEnumAllPrnt
        name: dmnEnumAllPrnt
    items:
      - !_Aliased
        name: dmnEnumAllPrnt
    name: EnumDmnEnumAllPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntDmnEnumAllPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumDescr: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumDescr
        name: dmnEnumDescr
    items:
      - !_Aliased
        name: dmnEnumDescr
    name: EnumDmnEnumDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumLabel: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumLabel
        name: dmnEnumLabel
    items:
      - !_Aliased
        name: dmnEnumLabel
    name: EnumDmnEnumLabel
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumPrnt
        name: enum_dmnEnumPrnt
      - !_EnumLabel
        enum: EnumDmnEnumPrnt
        name: prnt_dmnEnumPrnt
    items:
      - !_Aliased
        name: enum_dmnEnumPrnt
      - !_Aliased
        name: prnt_dmnEnumPrnt
    name: EnumDmnEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumPrntDescr: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumPrntDescr
        name: enum_dmnEnumPrntDescr
      - !_EnumLabel
        enum: EnumDmnEnumPrntDescr
        name: prnt_dmnEnumPrntDescr
    items:
      - !_Aliased
        name: enum_dmnEnumPrntDescr
      - !_Aliased
        name: prnt_dmnEnumPrntDescr
    name: EnumDmnEnumPrntDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumUnq: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumUnq
        name: enum_dmnEnumUnq
      - !_EnumLabel
        enum: EnumDmnEnumUnq
        name: dmnEnumUnq
    items:
      - !_Aliased
        name: enum_dmnEnumUnq
      - !_Aliased
        name: dmnEnumUnq
    name: EnumDmnEnumUnq
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumUnqPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntDmnEnumUnqPrnt
        name: dmnEnumUnqPrnt
      - !_EnumLabel
        enum: PrntDmnEnumUnqPrnt
        name: prnt_dmnEnumUnqPrnt
      - !_EnumLabel
        enum: EnumDmnEnumUnqPrnt
        name: enum_dmnEnumUnqPrnt
    items:
      - !_Aliased
        name: enum_dmnEnumUnqPrnt
    name: EnumDmnEnumUnqPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntDmnEnumUnqPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumValue: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumValue
        name: dmnEnumValue
    items:
      - !_Aliased
        name: dmnEnumValue
    name: EnumDmnEnumValue
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumValuePrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntDmnEnumValuePrnt
        name: prnt_dmnEnumValuePrnt
      - !_EnumLabel
        enum: EnumDmnEnumValuePrnt
        name: dmnEnumValuePrnt
    items:
      - !_Aliased
        name: dmnEnumValuePrnt
    name: EnumDmnEnumValuePrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntDmnEnumValuePrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumDomDup: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDomDup
        name: dmnEnumUnq
      - !_EnumLabel
        enum: EnumDomDup
        name: dup_dmnEnumUnq
    items:
      - !_Aliased
        name: dmnEnumUnq
      - !_Aliased
        name: dup_dmnEnumUnq
    name: EnumDomDup
    typeKind: !_TypeKind Enum
  !_Identifier EnumPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntEnumPrnt
        name: prnt_enumPrnt
      - !_EnumLabel
        enum: EnumPrnt
        name: enumPrnt
    items:
      - !_Aliased
        name: enumPrnt
    name: EnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumPrntAlias: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntEnumPrntAlias
        name: prnt_enumPrntAlias
      - !_EnumLabel
        enum: EnumPrntAlias
        name: val_enumPrntAlias
      - !_EnumLabel
        aliases: [enumPrntAlias]
        enum: EnumPrntAlias
        name: prnt_enumPrntAlias
    items:
      - !_Aliased
        name: val_enumPrntAlias
      - !_Aliased
        aliases: [enumPrntAlias]
        name: prnt_enumPrntAlias
    name: EnumPrntAlias
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumPrntAlias
    typeKind: !_TypeKind Enum
  !_Identifier EnumPrntDescr: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntEnumPrntDescr
        name: prnt_enumPrntDescr
      - !_EnumLabel
        enum: EnumPrntDescr
        name: enumPrntDescr
    items:
      - !_Aliased
        name: enumPrntDescr
    name: EnumPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumPrntDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumPrntDup: !_TypeEnum
    allItems:
      - !_EnumLabel
        aliases: [enumPrntDup]
        enum: PrntEnumPrntDup
        name: prnt_enumPrntDup
      - !_EnumLabel
        enum: EnumPrntDup
        name: enumPrntDup
    items:
      - !_Aliased
        name: enumPrntDup
    name: EnumPrntDup
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumPrntDup
    typeKind: !_TypeKind Enum
  !_Identifier PrntDmnBoolPrnt: !_DomainBoolean
    allItems:
      - !_DomainItem(_DomainTrueFalse)
        domain: PrntDmnBoolPrnt
        exclude: false
        value: true
    domainKind: !_DomainKind Boolean
    items:
      - !_DomainTrueFalse
        exclude: false
        value: true
    name: PrntDmnBoolPrnt
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnBoolPrntDescr: !_DomainBoolean
    allItems:
      - !_DomainItem(_DomainTrueFalse)
        domain: PrntDmnBoolPrntDescr
        exclude: false
        value: true
    domainKind: !_DomainKind Boolean
    items:
      - !_DomainTrueFalse
        exclude: false
        value: true
    name: PrntDmnBoolPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnEnumAllPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntDmnEnumAllPrnt
        name: prnt_dmnEnumAllPrnt
    items:
      - !_Aliased
        name: prnt_dmnEnumAllPrnt
    name: PrntDmnEnumAllPrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntDmnEnumPrnt: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: PrntDmnEnumPrnt
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumPrnt
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrnt
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumPrnt
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrnt
    name: PrntDmnEnumPrnt
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnEnumPrntDescr: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: PrntDmnEnumPrntDescr
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumPrntDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrntDescr
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: prnt_dmnEnumPrntDescr
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumPrntDescr
    name: PrntDmnEnumPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnEnumUnqPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntDmnEnumUnqPrnt
        name: dmnEnumUnqPrnt
      - !_EnumLabel
        enum: PrntDmnEnumUnqPrnt
        name: prnt_dmnEnumUnqPrnt
    items:
      - !_Aliased
        name: dmnEnumUnqPrnt
      - !_Aliased
        name: prnt_dmnEnumUnqPrnt
    name: PrntDmnEnumUnqPrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntDmnEnumValuePrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntDmnEnumValuePrnt
        name: prnt_dmnEnumValuePrnt
    items:
      - !_Aliased
        name: prnt_dmnEnumValuePrnt
    name: PrntDmnEnumValuePrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntDmnNmbrPrnt: !_DomainNumber
    allItems:
      - !_DomainItem(_DomainRange)
        domain: PrntDmnNmbrPrnt
        exclude: false
        to: 2
    domainKind: !_DomainKind Number
    items:
      - !_DomainRange
        exclude: false
        to: 2
    name: PrntDmnNmbrPrnt
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnNmbrPrntDescr: !_DomainNumber
    allItems:
      - !_DomainItem(_DomainRange)
        domain: PrntDmnNmbrPrntDescr
        exclude: false
        to: 2
    domainKind: !_DomainKind Number
    items:
      - !_DomainRange
        exclude: false
        to: 2
    name: PrntDmnNmbrPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnStrPrnt: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: PrntDmnStrPrnt
        exclude: false
        pattern: b+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: b+
    name: PrntDmnStrPrnt
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnStrPrntDescr: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: PrntDmnStrPrntDescr
        exclude: false
        pattern: b+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: b+
    name: PrntDmnStrPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier PrntEnumPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntEnumPrnt
        name: prnt_enumPrnt
    items:
      - !_Aliased
        name: prnt_enumPrnt
    name: PrntEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntEnumPrntAlias: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntEnumPrntAlias
        name: prnt_enumPrntAlias
    items:
      - !_Aliased
        name: prnt_enumPrntAlias
    name: PrntEnumPrntAlias
    typeKind: !_TypeKind Enum
  !_Identifier PrntEnumPrntDescr: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntEnumPrntDescr
        name: prnt_enumPrntDescr
    items:
      - !_Aliased
        name: prnt_enumPrntDescr
    name: PrntEnumPrntDescr
    typeKind: !_TypeKind Enum
  !_Identifier PrntEnumPrntDup: !_TypeEnum
    allItems:
      - !_EnumLabel
        aliases: [enumPrntDup]
        enum: PrntEnumPrntDup
        name: prnt_enumPrntDup
    items:
      - !_Aliased
        aliases: [enumPrntDup]
        name: prnt_enumPrntDup
    name: PrntEnumPrntDup
    typeKind: !_TypeKind Enum
  !_Identifier PrntUnionPrnt: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrnt
    items:
      - !_Aliased
        name: Number
    name: PrntUnionPrnt
    typeKind: !_TypeKind Union
  !_Identifier PrntUnionPrntDescr: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrntDescr
    items:
      - !_Aliased
        name: Number
    name: PrntUnionPrntDescr
    typeKind: !_TypeKind Union
  !_Identifier PrntUnionPrntDup: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrntDup
    items:
      - !_Aliased
        name: Number
    name: PrntUnionPrntDup
    typeKind: !_TypeKind Union
  !_Identifier UnionDescr: !_TypeUnion
    allItems:
      - !_UnionMember
        description: 'Union Descr'
        name: Number
        union: UnionDescr
    items:
      - !_Aliased
        description: 'Union Descr'
        name: Number
    name: UnionDescr
    typeKind: !_TypeKind Union
  !_Identifier UnionPrnt: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrnt
      - !_UnionMember
        name: String
        union: UnionPrnt
    items:
      - !_Aliased
        name: String
    name: UnionPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Union
      typeName: PrntUnionPrnt
    typeKind: !_TypeKind Union
  !_Identifier UnionPrntDescr: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrntDescr
      - !_UnionMember
        name: Number
        union: UnionPrntDescr
    items:
      - !_Aliased
        name: Number
    name: UnionPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Union
      typeName: PrntUnionPrntDescr
    typeKind: !_TypeKind Union
  !_Identifier UnionPrntDup: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrntDup
      - !_UnionMember
        name: Number
        union: UnionPrntDup
    items:
      - !_Aliased
        name: Number
    name: UnionPrntDup
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Union
      typeName: PrntUnionPrntDup
    typeKind: !_TypeKind Union