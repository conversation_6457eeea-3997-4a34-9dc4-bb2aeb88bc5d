﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstFieldObjInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: PrntCnstFieldObjInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltCnstFieldObjInp
        type: !_InputBase
          input: Number
    fields:
      - !_InputField
        name: alt
        type: !_InputBase
          input: Number
    name: AltCnstFieldObjInp
    parent: !_InputBase
      input: PrntCnstFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier CnstFieldObjInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: RefCnstFieldObjInp
        type: !_InputBase
          input: AltCnstFieldObjInp
    name: CnstFieldObjInp
    parent: !_InputBase
      input: RefCnstFieldObjInp
      typeArgs:
        - !_InputArg
          input: AltCnstFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstFieldObjInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: PrntCnstFieldObjInp
        type: !_InputBase
          input: String
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    name: PrntCnstFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier RefCnstFieldObjInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: RefCnstFieldObjInp
        type: !_InputBase
          typeParam: ref
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          typeParam: ref
    name: RefCnstFieldObjInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Input
          typeName: PrntCnstFieldObjInp
        name: ref