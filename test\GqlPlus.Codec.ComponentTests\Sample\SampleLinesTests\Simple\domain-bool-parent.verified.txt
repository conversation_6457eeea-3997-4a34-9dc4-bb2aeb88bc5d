﻿!_Schema
types: !_Map_Type
  !_Identifier DmnBoolPrnt: !_DomainBoolean
    allItems:
      - !_DomainItem(_DomainTrueFalse)
        domain: PrntDmnBoolPrnt
        exclude: false
        value: true
      - !_DomainItem(_DomainTrueFalse)
        domain: DmnBoolPrnt
        exclude: false
        value: false
    domainKind: !_DomainKind Boolean
    items:
      - !_DomainTrueFalse
        exclude: false
        value: false
    name: DmnBoolPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnBoolPrnt
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnBoolPrnt: !_DomainBoolean
    allItems:
      - !_DomainItem(_DomainTrueFalse)
        domain: PrntDmnBoolPrnt
        exclude: false
        value: true
    domainKind: !_DomainKind Boolean
    items:
      - !_DomainTrueFalse
        exclude: false
        value: true
    name: PrntDmnBoolPrnt
    typeKind: !_TypeKind Domain