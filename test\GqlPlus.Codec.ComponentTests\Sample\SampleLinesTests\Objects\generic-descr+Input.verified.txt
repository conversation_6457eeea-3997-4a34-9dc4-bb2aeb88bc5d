﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcDescrInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: GnrcDescrInp
        type: !_InputBase
          typeParam: type
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          typeParam: type
    name: GnrcDescrInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        description: 'Test Descr'
        name: type