﻿!_Schema
types: !_Map_Type
  !_Identifier PrntFieldDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefPrntFieldDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: parent
        object: RefPrntFieldDual
        type: !_DualBase
          dual: Number
      - !_ObjectFor(_DualField)
        name: field
        object: PrntFieldDual
        type: !_DualBase
          dual: Number
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          dual: Number
    name: PrntFieldDual
    parent: !_DualBase
      dual: RefPrntFieldDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntFieldDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefPrntFieldDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: parent
        object: RefPrntFieldDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: parent
        type: !_DualBase
          dual: Number
    name: RefPrntFieldDual
    typeKind: !_TypeKind Dual