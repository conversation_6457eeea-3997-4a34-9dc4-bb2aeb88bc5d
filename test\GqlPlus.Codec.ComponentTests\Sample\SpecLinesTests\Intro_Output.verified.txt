﻿!_Schema
errors:
  - !_Error
    _kind: !_TokenKind End
    _message: In _TypeOutput can't get model for type '_TypeObject'
  - !_Error
    _kind: !_TokenKind End
    _message: In _OutputBase can't get model for type '_ObjBase'
  - !_Error
    _kind: !_TokenKind End
    _message: In _OutputTypeParam can't get model for type '_ObjTypeParam'
  - !_Error
    _kind: !_TokenKind End
    _message: In _OutputField can't get model for type '_Field'
  - !_Error
    _kind: !_TokenKind End
    _message: In _OutputAlternate can't get model for type '_Alternate'
  - !_Error
    _kind: !_TokenKind End
    _message: In _OutputTypeArg can't get model for type '_ObjTypeArg'
  - !_Error
    _kind: !_TokenKind End
    _message: In _OutputEnum can't get model for type '_TypeRef'
types: !_Map_Type
  !_Identifier _OutputAlternate: !_TypeOutput
    name: _OutputAlternate
    parent: !_OutputBase
      output: _Alternate
      typeArgs:
        - !_OutputArg
          output: _OutputBase
    typeKind: !_TypeKind Output
  !_Identifier _OutputBase: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _OutputBase
        type: !_OutputBase
          output: _DualBase
    allFields:
      - !_ObjectFor(_OutputField)
        name: output
        object: _OutputBase
        type: !_OutputBase
          output: _Identifier
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _DualBase
    fields:
      - !_OutputField
        name: output
        type: !_OutputBase
          output: _Identifier
    name: _OutputBase
    parent: !_OutputBase
      output: _ObjBase
      typeArgs:
        - !_OutputArg
          output: _OutputTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _OutputEnum: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: _OutputEnum
        type: !_OutputBase
          output: _Identifier
      - !_ObjectFor(_OutputField)
        name: label
        object: _OutputEnum
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          output: _Identifier
      - !_OutputField
        name: label
        type: !_OutputBase
          output: _Identifier
    name: _OutputEnum
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _OutputField: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _OutputField
        type: !_OutputBase
          output: _OutputEnum
    allFields:
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: parameters
        object: _OutputField
        type: !_OutputBase
          output: _InputParam
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _OutputEnum
    fields:
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: parameters
        type: !_OutputBase
          output: _InputParam
    name: _OutputField
    parent: !_OutputBase
      output: _Field
      typeArgs:
        - !_OutputArg
          output: _OutputBase
    typeKind: !_TypeKind Output
  !_Identifier _OutputTypeArg: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: output
        object: _OutputTypeArg
        type: !_OutputBase
          output: _Identifier
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: label
        object: _OutputTypeArg
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: output
        type: !_OutputBase
          output: _Identifier
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: label
        type: !_OutputBase
          output: _Identifier
    name: _OutputTypeArg
    parent: !_OutputBase
      output: _ObjTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _OutputTypeParam: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _OutputTypeParam
        type: !_OutputBase
          output: _TypeRef
          typeArgs:
            - !_OutputArg
              label: Dual
              typeKind: !_SimpleKind Enum
              typeName: _TypeKind
      - !_ObjectFor(_OutputAlternate)
        object: _OutputTypeParam
        type: !_OutputBase
          output: _TypeRef
          typeArgs:
            - !_OutputArg
              label: Enum
              typeKind: !_SimpleKind Enum
              typeName: _TypeKind
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _TypeRef
          typeArgs:
            - !_OutputArg
              label: Dual
              typeKind: !_SimpleKind Enum
              typeName: _TypeKind
      - !_OutputAlternate
        type: !_OutputBase
          output: _TypeRef
          typeArgs:
            - !_OutputArg
              label: Enum
              typeKind: !_SimpleKind Enum
              typeName: _TypeKind
    name: _OutputTypeParam
    parent: !_OutputBase
      output: _ObjTypeParam
      typeArgs:
        - !_OutputArg
          label: Output
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _TypeOutput: !_TypeOutput
    name: _TypeOutput
    parent: !_OutputBase
      output: _TypeObject
      typeArgs:
        - !_OutputArg
          label: Output
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
        - !_OutputArg
          output: _OutputBase
        - !_OutputArg
          output: _OutputTypeParam
        - !_OutputArg
          output: _OutputField
        - !_OutputArg
          output: _OutputAlternate
    typeKind: !_TypeKind Output