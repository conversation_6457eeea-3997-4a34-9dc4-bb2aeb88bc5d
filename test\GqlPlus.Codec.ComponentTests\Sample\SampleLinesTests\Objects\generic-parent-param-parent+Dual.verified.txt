﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcPrntParamPrntDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcPrntParamPrntDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcPrntParamPrntDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcPrntParamPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntParamPrntDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcPrntParamPrntDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcPrntParamPrntDual
        type: !_DualBase
          dual: Number
    name: GnrcPrntParamPrntDual
    parent: !_DualBase
      dual: RefGnrcPrntParamPrntDual
      typeArgs:
        - !_DualArg
          dual: AltGnrcPrntParamPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier RefGnrcPrntParamPrntDual: !_TypeDual
    name: RefGnrcPrntParamPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref