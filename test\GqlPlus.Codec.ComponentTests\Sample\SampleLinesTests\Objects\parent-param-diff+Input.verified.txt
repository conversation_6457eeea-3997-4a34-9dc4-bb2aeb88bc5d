﻿!_Schema
types: !_Map_Type
  !_Identifier PrntParamDiffInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefPrntParamDiffInp
        type: !_InputBase
          typeParam: b
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: PrntParamDiffInp
        type: !_InputBase
          typeParam: a
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          typeParam: a
    name: PrntParamDiffInp
    parent: !_InputBase
      input: RefPrntParamDiffInp
      typeArgs:
        - !_InputArg
          typeParam: a
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: a
  !_Identifier RefPrntParamDiffInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefPrntParamDiffInp
        type: !_InputBase
          typeParam: b
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: b
    name: RefPrntParamDiffInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: b