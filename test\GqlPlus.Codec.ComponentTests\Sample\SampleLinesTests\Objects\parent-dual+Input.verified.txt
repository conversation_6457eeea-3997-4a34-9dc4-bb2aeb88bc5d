﻿!_Schema
types: !_Map_Type
  !_Identifier PrntDualInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefPrntDualInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: parent
        object: RefPrntDualInp
        type: !_DualBase
          dual: Number
    name: PrntDualInp
    parent: !_DualBase
      dual: RefPrntDualInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntDualInp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefPrntDualInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: parent
        object: RefPrntDualInp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: parent
        type: !_DualBase
          dual: Number
    name: RefPrntDualInp
    typeKind: !_TypeKind Dual