﻿!_Schema
types: !_Map_Type
  !_Identifier FieldDualInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: FieldDualInp
        type: !_DualBase
          dual: FldFieldDualInp
    fields:
      - !_InputField
        name: field
        type: !_DualBase
          dual: FldFieldDualInp
    name: FieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier FldFieldDualInp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: FldFieldDualInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FldFieldDualInp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          dual: Number
    name: FldFieldDualInp
    typeKind: !_TypeKind Dual