﻿!_Schema
types: !_Map_Type
  !_Identifier EnumGnrcPrntEnumPrntDual: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentGnrcPrntEnumPrntDual
        name: gnrcPrntEnumPrntDualParent
      - !_EnumLabel
        enum: EnumGnrcPrntEnumPrntDual
        name: gnrcPrntEnumPrntDualLabel
    items:
      - !_Aliased
        name: gnrcPrntEnumPrntDualLabel
    name: EnumGnrcPrntEnumPrntDual
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Enum
  !_Identifier FieldGnrcPrntEnumPrntDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FieldGnrcPrntEnumPrntDual
        type: !_DualBase
          typeParam: ref
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          typeParam: ref
    name: FieldGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: ParentGnrcPrntEnumPrntDual
        name: ref
  !_Identifier GnrcPrntEnumPrntDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FieldGnrcPrntEnumPrntDual
        type: !_DualBase
          dual: EnumGnrcPrntEnumPrntDual
    name: GnrcPrntEnumPrntDual
    parent: !_DualBase
      dual: FieldGnrcPrntEnumPrntDual
      typeArgs:
        - !_DualArg
          dual: EnumGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier ParentGnrcPrntEnumPrntDual: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentGnrcPrntEnumPrntDual
        name: gnrcPrntEnumPrntDualParent
    items:
      - !_Aliased
        name: gnrcPrntEnumPrntDualParent
    name: ParentGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Enum