﻿[  
!Sc S@001/0001
Success
{
  'A Category described'
  !Ca P@010/0003
  ctgrDscrs
  (Parallel)
  !Tr I@012/0003
  CtgrDscrs
}
{
  !Ca P@010/0001
  ctgrOutp
  (Parallel)
  !Tr I@012/0001
  CtgrOutp
}
{
  !Ca P@010/0001
  ctgrOutpDescr
  (Parallel)
  'Output comment'
  !Tr I@029/0001
  CtgrOutpDescr
}
{
  !Ca P@010/0001
  ctgrOutpDict
  (Parallel)
  !Tr I@012/0001
  CtgrOutpDict
  [*]
}
{
  !Ca P@010/0001
  ctgrOutpList
  (Parallel)
  !Tr I@012/0001
  CtgrOutpList
  []
}
{
  !Ca P@010/0001
  ctgrOutpOptl
  (Parallel)
  !Tr I@012/0001
  CtgrOutpOptl
  ?
}
{
  !Ca P@010/0001
  descrBtwn
  (Parallel)
  !Tr I@012/0001
  DescrBtwn
}
{
  !Ca P@010/0001
  ctgr
  (Parallel)
  !Tr I@012/0001
  Ctgr
}
{
  !Ca P@010/0001
  ctgrAlias
  [
    CatA1
    CatA2
  ]
  (Parallel)
  !Tr I@020/0001
  CtgrAlias
}
{
  'First category Second category'
  !Ca P@010/0002
  ctgrDescr
  (Parallel)
  !Tr I@012/0002
  CtgrDescr
}
{
  !Ca P@010/0001
  ctgrMod
  [
    CatM1
    CatM2
  ]
  (Parallel)
  !Tr I@020/0001
  CtgrMod
  ?
}
{
  'A directive described'
  !Di I@012/0002
  DrctDescr
  (Unique)
  All
}
{
  !Di I@012/0001
  DrctNoParam
  (Unique)
  All
}
{
  !Di I@012/0001
  DrctParamDict
  (
    !Pa
    I@026/0001
    InDrctParamDict
    [String]
  )
  (Unique)
  All
}
{
  !Di I@012/0001
  DrctParamIn
  (
    !Pa
    I@024/0001
    InDrctParamIn
  )
  (Unique)
  All
}
{
  !Di I@012/0001
  DrctParamList
  (
    !Pa
    I@026/0001
    InDrctParamList
    []
  )
  (Unique)
  All
}
{
  !Di I@012/0001
  DrctParamOpt
  (
    !Pa
    I@025/0001
    InDrctParamOpt
    ?
  )
  (Unique)
  All
}
{
  !Di I@012/0001
  Drct
  (Unique)
  Inline, Spread
}
{
  !Di I@012/0001
  DrctAlias
  [
    DirA1
    DirA2
  ]
  (Unique)
  Variable, Field
}
{
  !Di I@012/0001
  DrctParam
  (
    !Pa
    I@022/0001
    InDrctParam
  )
  (Unique)
  Operation, Fragment
}
{
  !Op I@008/0001
  Schema
  [
    Alias
    Opt1
    Opt2
  ]
  {
    !OS I@017/0001
    global
    =( !k I@024/0001 Boolean.true )
    'Option Descr'
    !OS S@017/0001
    descr
    =( !k I@040/0001 Boolean.true )
    !OS I@017/0001
    merged
    =( !c P@024/0002 [ !k I@024/0001 Boolean.true !k N@025/0002 0 ] )
  }
}
{
  !Ou I@008/0004
  CtgrDscrs
}
{
  !Ou I@008/0002
  CtgrOutp
}
{
  !Ou I@008/0002
  CtgrOutpDescr
}
{
  !Ou I@008/0002
  CtgrOutpDict
}
{
  !Ou I@008/0002
  CtgrOutpList
}
{
  !Ou I@008/0002
  CtgrOutpOptl
}
{
  'A simple description'
  !Ou I@008/0002
  Descr
}
{
  'A backslash ("\\") description'
  !Ou I@008/0002
  DescrBcks
}
{
  'A description between'
  !Ou I@008/0003
  DescrBtwn
}
{
  'A "more" \'Complicated\' \\ description'
  !Ou I@008/0002
  DescrCmpl
}
{
  'A \'double-quoted\' description'
  !Ou I@008/0002
  DescrDbl
}
{
  'A "single-quoted" description'
  !Ou I@008/0002
  DescrSngl
}
{
  'A simple description With extra'
  !Ou I@008/0003
  Dscrs
}
{
  !In I@007/0002
  InDrctParamDict
}
{
  !In I@007/0002
  InDrctParamIn
}
{
  !In I@007/0002
  InDrctParamList
}
{
  !In I@007/0002
  InDrctParamOpt
}
{
  !Du I@006/0001
  AltDual
  |
  I@018/0001
  AltAltDual
}
{
  !Du I@006/0002
  AltAltDual
  {
    !DF I@019/0002
    alt
    :
    I@024/0002
    Number
  }
  |
  I@033/0002
  String
}
{
  !In I@007/0001
  AltInp
  |
  I@018/0001
  AltAltInp
}
{
  !In I@007/0002
  AltAltInp
  {
    !IF I@019/0002
    alt
    :
    I@024/0002
    Number
  }
  |
  I@033/0002
  String
}
{
  !Ou I@008/0001
  AltOutp
  |
  I@020/0001
  AltAltOutp
}
{
  !Ou I@008/0002
  AltAltOutp
  {
    !OF I@021/0002
    alt
    :
    I@026/0002
    Number
  }
  |
  I@035/0002
  String
}
{
  !Du I@006/0001
  AltDescrDual
  |
  'Test Descr'
  S@023/0001
  String
}
{
  !In I@007/0001
  AltDescrInp
  |
  'Test Descr'
  S@023/0001
  String
}
{
  !Ou I@008/0001
  AltDescrOutp
  |
  'Test Descr'
  S@025/0001
  String
}
{
  !Du I@006/0001
  AltDualDual
  |
  I@022/0001
  ObjDualAltDualDual
}
{
  !Du I@006/0002
  ObjDualAltDualDual
  {
    !DF I@027/0002
    alt
    :
    I@032/0002
    Number
  }
  |
  I@041/0002
  String
}
{
  !In I@007/0001
  AltDualInp
  |
  I@022/0001
  ObjDualAltDualInp
}
{
  !Du I@006/0002
  ObjDualAltDualInp
  {
    !DF I@026/0002
    alt
    :
    I@031/0002
    Number
  }
  |
  I@040/0002
  String
}
{
  !Ou I@008/0001
  AltDualOutp
  |
  I@024/0001
  ObjDualAltDualOutp
}
{
  !Du I@006/0002
  ObjDualAltDualOutp
  {
    !DF I@027/0002
    alt
    :
    I@032/0002
    Number
  }
  |
  I@041/0002
  String
}
{
  !Du I@006/0001
  AltModBoolDual
  |
  I@025/0001
  AltAltModBoolDual
  [^]
}
{
  !Du I@006/0002
  AltAltModBoolDual
  {
    !DF I@026/0002
    alt
    :
    I@031/0002
    Number
  }
  |
  I@040/0002
  String
}
{
  !In I@007/0001
  AltModBoolInp
  |
  I@025/0001
  AltAltModBoolInp
  [^]
}
{
  !In I@007/0002
  AltAltModBoolInp
  {
    !IF I@026/0002
    alt
    :
    I@031/0002
    Number
  }
  |
  I@040/0002
  String
}
{
  !Ou I@008/0001
  AltModBoolOutp
  |
  I@027/0001
  AltAltModBoolOutp
  [^]
}
{
  !Ou I@008/0002
  AltAltModBoolOutp
  {
    !OF I@028/0002
    alt
    :
    I@033/0002
    Number
  }
  |
  I@042/0002
  String
}
{
  !Du I@006/0001
  AltModParamDual
  <
    I@023/0001
    $mod
    :String
  >
  |
  I@039/0001
  AltAltModParamDual
  [$mod]
}
{
  !Du I@006/0002
  AltAltModParamDual
  {
    !DF I@027/0002
    alt
    :
    I@032/0002
    Number
  }
  |
  I@041/0002
  String
}
{
  !In I@007/0001
  AltModParamInp
  <
    I@023/0001
    $mod
    :String
  >
  |
  I@039/0001
  AltAltModParamInp
  [$mod]
}
{
  !In I@007/0002
  AltAltModParamInp
  {
    !IF I@027/0002
    alt
    :
    I@032/0002
    Number
  }
  |
  I@041/0002
  String
}
{
  !Ou I@008/0001
  AltModParamOutp
  <
    I@025/0001
    $mod
    :String
  >
  |
  I@041/0001
  AltAltModParamOutp
  [$mod]
}
{
  !Ou I@008/0002
  AltAltModParamOutp
  {
    !OF I@029/0002
    alt
    :
    I@034/0002
    Number
  }
  |
  I@043/0002
  String
}
{
  !Du I@006/0001
  AltSmplDual
  |
  I@022/0001
  String
}
{
  !In I@007/0001
  AltSmplInp
  |
  I@022/0001
  String
}
{
  !Ou I@008/0001
  AltSmplOutp
  |
  I@024/0001
  String
}
{
  !Du I@006/0001
  CnstAltDual
  <
    I@019/0001
    $type
    :Number
  >
  |
  P@036/0001
  $type
}
{
  !In I@007/0001
  CnstAltInp
  <
    I@019/0001
    $type
    :Number
  >
  |
  P@036/0001
  $type
}
{
  !Ou I@008/0001
  CnstAltOutp
  <
    I@021/0001
    $type
    :Number
  >
  |
  P@038/0001
  $type
}
{
  !Du I@006/0001
  CnstAltDmnDual
  |
  I@025/0001
  RefCnstAltDmnDual
  <
    I@043/0001
    DomCnstAltDmnDual
  >
}
{
  !Du I@006/0002
  RefCnstAltDmnDual
  <
    I@025/0002
    $ref
    :String
  >
  |
  P@041/0002
  $ref
}
{
  !Do I@008/0003
  DomCnstAltDmnDual
  String
  !DX R@035/0003
  /\\w+/
}
{
  !In I@007/0001
  CnstAltDmnInp
  |
  I@025/0001
  RefCnstAltDmnInp
  <
    I@042/0001
    DomCnstAltDmnInp
  >
}
{
  !In I@007/0002
  RefCnstAltDmnInp
  <
    I@025/0002
    $ref
    :String
  >
  |
  P@041/0002
  $ref
}
{
  !Do I@008/0003
  DomCnstAltDmnInp
  String
  !DX R@034/0003
  /\\w+/
}
{
  !Ou I@008/0001
  CnstAltDmnOutp
  |
  I@027/0001
  RefCnstAltDmnOutp
  <
    I@045/0001
    DomCnstAltDmnOutp
  >
}
{
  !Ou I@008/0002
  RefCnstAltDmnOutp
  <
    I@027/0002
    $ref
    :String
  >
  |
  P@043/0002
  $ref
}
{
  !Do I@008/0003
  DomCnstAltDmnOutp
  String
  !DX R@035/0003
  /\\w+/
}
{
  !Du I@006/0001
  CnstAltDualDual
  |
  I@026/0001
  RefCnstAltDualDual
  <
    I@045/0001
    AltCnstAltDualDual
  >
}
{
  !Du I@006/0002
  RefCnstAltDualDual
  <
    I@026/0002
    $ref
    :PrntCnstAltDualDual
  >
  |
  P@055/0002
  $ref
}
{
  !Du I@006/0003
  PrntCnstAltDualDual
  |
  I@030/0003
  String
}
{
  !Du I@006/0004
  AltCnstAltDualDual
  :
  I@028/0004
  PrntCnstAltDualDual
  {
    !DF I@048/0004
    alt
    :
    I@053/0004
    Number
  }
}
{
  !In I@007/0001
  CnstAltDualInp
  |
  I@026/0001
  RefCnstAltDualInp
  <
    I@044/0001
    AltCnstAltDualInp
  >
}
{
  !In I@007/0002
  RefCnstAltDualInp
  <
    I@026/0002
    $ref
    :PrntCnstAltDualInp
  >
  |
  P@054/0002
  $ref
}
{
  !Du I@006/0003
  PrntCnstAltDualInp
  |
  I@029/0003
  String
}
{
  !In I@007/0004
  AltCnstAltDualInp
  :
  I@028/0004
  PrntCnstAltDualInp
  {
    !IF I@047/0004
    alt
    :
    I@052/0004
    Number
  }
}
{
  !Ou I@008/0001
  CnstAltDualOutp
  |
  I@028/0001
  RefCnstAltDualOutp
  <
    I@047/0001
    AltCnstAltDualOutp
  >
}
{
  !Ou I@008/0002
  RefCnstAltDualOutp
  <
    I@028/0002
    $ref
    :PrntCnstAltDualOutp
  >
  |
  P@057/0002
  $ref
}
{
  !Du I@006/0003
  PrntCnstAltDualOutp
  |
  I@030/0003
  String
}
{
  !Ou I@008/0004
  AltCnstAltDualOutp
  :
  I@030/0004
  PrntCnstAltDualOutp
  {
    !OF I@050/0004
    alt
    :
    I@055/0004
    Number
  }
}
{
  !Du I@006/0001
  CnstAltObjDual
  |
  I@025/0001
  RefCnstAltObjDual
  <
    I@043/0001
    AltCnstAltObjDual
  >
}
{
  !Du I@006/0002
  RefCnstAltObjDual
  <
    I@025/0002
    $ref
    :PrntCnstAltObjDual
  >
  |
  P@053/0002
  $ref
}
{
  !Du I@006/0003
  PrntCnstAltObjDual
  |
  I@029/0003
  String
}
{
  !Du I@006/0004
  AltCnstAltObjDual
  :
  I@027/0004
  PrntCnstAltObjDual
  {
    !DF I@046/0004
    alt
    :
    I@051/0004
    Number
  }
}
{
  !In I@007/0001
  CnstAltObjInp
  |
  I@025/0001
  RefCnstAltObjInp
  <
    I@042/0001
    AltCnstAltObjInp
  >
}
{
  !In I@007/0002
  RefCnstAltObjInp
  <
    I@025/0002
    $ref
    :PrntCnstAltObjInp
  >
  |
  P@052/0002
  $ref
}
{
  !In I@007/0003
  PrntCnstAltObjInp
  |
  I@029/0003
  String
}
{
  !In I@007/0004
  AltCnstAltObjInp
  :
  I@027/0004
  PrntCnstAltObjInp
  {
    !IF I@045/0004
    alt
    :
    I@050/0004
    Number
  }
}
{
  !Ou I@008/0001
  CnstAltObjOutp
  |
  I@027/0001
  RefCnstAltObjOutp
  <
    I@045/0001
    AltCnstAltObjOutp
  >
}
{
  !Ou I@008/0002
  RefCnstAltObjOutp
  <
    I@027/0002
    $ref
    :PrntCnstAltObjOutp
  >
  |
  P@055/0002
  $ref
}
{
  !Ou I@008/0003
  PrntCnstAltObjOutp
  |
  I@031/0003
  String
}
{
  !Ou I@008/0004
  AltCnstAltObjOutp
  :
  I@029/0004
  PrntCnstAltObjOutp
  {
    !OF I@048/0004
    alt
    :
    I@053/0004
    Number
  }
}
{
  !Du I@006/0001
  CnstFieldDmnDual
  :
  I@026/0001
  RefCnstFieldDmnDual
  <
    I@046/0001
    DomCnstFieldDmnDual
  >
}
{
  !Du I@006/0002
  RefCnstFieldDmnDual
  <
    I@027/0002
    $ref
    :String
  >
  {
    !DF I@041/0002
    field
    :
    I@049/0002
    $ref
  }
}
{
  !Do I@008/0003
  DomCnstFieldDmnDual
  String
  !DX R@037/0003
  /\\w+/
}
{
  !In I@007/0001
  CnstFieldDmnInp
  :
  I@026/0001
  RefCnstFieldDmnInp
  <
    I@045/0001
    DomCnstFieldDmnInp
  >
}
{
  !In I@007/0002
  RefCnstFieldDmnInp
  <
    I@027/0002
    $ref
    :String
  >
  {
    !IF I@041/0002
    field
    :
    I@049/0002
    $ref
  }
}
{
  !Do I@008/0003
  DomCnstFieldDmnInp
  String
  !DX R@036/0003
  /\\w+/
}
{
  !Ou I@008/0001
  CnstFieldDmnOutp
  :
  I@028/0001
  RefCnstFieldDmnOutp
  <
    I@048/0001
    DomCnstFieldDmnOutp
  >
}
{
  !Ou I@008/0002
  RefCnstFieldDmnOutp
  <
    I@029/0002
    $ref
    :String
  >
  {
    !OF I@043/0002
    field
    :
    I@051/0002
    $ref
  }
}
{
  !Do I@008/0003
  DomCnstFieldDmnOutp
  String
  !DX R@037/0003
  /\\w+/
}
{
  !Du I@006/0001
  CnstFieldDualDual
  :
  I@027/0001
  RefCnstFieldDualDual
  <
    I@048/0001
    AltCnstFieldDualDual
  >
}
{
  !Du I@006/0002
  RefCnstFieldDualDual
  <
    I@028/0002
    $ref
    :PrntCnstFieldDualDual
  >
  {
    !DF I@057/0002
    field
    :
    I@065/0002
    $ref
  }
}
{
  !Du I@006/0003
  PrntCnstFieldDualDual
  |
  I@032/0003
  String
}
{
  !Du I@006/0004
  AltCnstFieldDualDual
  :
  I@030/0004
  PrntCnstFieldDualDual
  {
    !DF I@052/0004
    alt
    :
    I@057/0004
    Number
  }
}
{
  !In I@007/0001
  CnstFieldDualInp
  :
  I@027/0001
  RefCnstFieldDualInp
  <
    I@047/0001
    AltCnstFieldDualInp
  >
}
{
  !In I@007/0002
  RefCnstFieldDualInp
  <
    I@028/0002
    $ref
    :PrntCnstFieldDualInp
  >
  {
    !IF I@056/0002
    field
    :
    I@064/0002
    $ref
  }
}
{
  !Du I@006/0003
  PrntCnstFieldDualInp
  |
  I@031/0003
  String
}
{
  !In I@007/0004
  AltCnstFieldDualInp
  :
  I@030/0004
  PrntCnstFieldDualInp
  {
    !IF I@051/0004
    alt
    :
    I@056/0004
    Number
  }
}
{
  !Ou I@008/0001
  CnstFieldDualOutp
  :
  I@029/0001
  RefCnstFieldDualOutp
  <
    I@050/0001
    AltCnstFieldDualOutp
  >
}
{
  !Ou I@008/0002
  RefCnstFieldDualOutp
  <
    I@030/0002
    $ref
    :PrntCnstFieldDualOutp
  >
  {
    !OF I@059/0002
    field
    :
    I@067/0002
    $ref
  }
}
{
  !Du I@006/0003
  PrntCnstFieldDualOutp
  |
  I@032/0003
  String
}
{
  !Ou I@008/0004
  AltCnstFieldDualOutp
  :
  I@032/0004
  PrntCnstFieldDualOutp
  {
    !OF I@054/0004
    alt
    :
    I@059/0004
    Number
  }
}
{
  !Du I@006/0001
  CnstFieldObjDual
  :
  I@026/0001
  RefCnstFieldObjDual
  <
    I@046/0001
    AltCnstFieldObjDual
  >
}
{
  !Du I@006/0002
  RefCnstFieldObjDual
  <
    I@027/0002
    $ref
    :PrntCnstFieldObjDual
  >
  {
    !DF I@055/0002
    field
    :
    I@063/0002
    $ref
  }
}
{
  !Du I@006/0003
  PrntCnstFieldObjDual
  |
  I@031/0003
  String
}
{
  !Du I@006/0004
  AltCnstFieldObjDual
  :
  I@029/0004
  PrntCnstFieldObjDual
  {
    !DF I@050/0004
    alt
    :
    I@055/0004
    Number
  }
}
{
  !In I@007/0001
  CnstFieldObjInp
  :
  I@026/0001
  RefCnstFieldObjInp
  <
    I@045/0001
    AltCnstFieldObjInp
  >
}
{
  !In I@007/0002
  RefCnstFieldObjInp
  <
    I@027/0002
    $ref
    :PrntCnstFieldObjInp
  >
  {
    !IF I@054/0002
    field
    :
    I@062/0002
    $ref
  }
}
{
  !In I@007/0003
  PrntCnstFieldObjInp
  |
  I@031/0003
  String
}
{
  !In I@007/0004
  AltCnstFieldObjInp
  :
  I@029/0004
  PrntCnstFieldObjInp
  {
    !IF I@049/0004
    alt
    :
    I@054/0004
    Number
  }
}
{
  !Ou I@008/0001
  CnstFieldObjOutp
  :
  I@028/0001
  RefCnstFieldObjOutp
  <
    I@048/0001
    AltCnstFieldObjOutp
  >
}
{
  !Ou I@008/0002
  RefCnstFieldObjOutp
  <
    I@029/0002
    $ref
    :PrntCnstFieldObjOutp
  >
  {
    !OF I@057/0002
    field
    :
    I@065/0002
    $ref
  }
}
{
  !Ou I@008/0003
  PrntCnstFieldObjOutp
  |
  I@033/0003
  String
}
{
  !Ou I@008/0004
  AltCnstFieldObjOutp
  :
  I@031/0004
  PrntCnstFieldObjOutp
  {
    !OF I@052/0004
    alt
    :
    I@057/0004
    Number
  }
}
{
  !Du I@006/0001
  CnstPrntDualPrntDual
  :
  I@030/0001
  RefCnstPrntDualPrntDual
  <
    I@054/0001
    AltCnstPrntDualPrntDual
  >
}
{
  !Du I@006/0002
  RefCnstPrntDualPrntDual
  <
    I@031/0002
    $ref
    :PrntCnstPrntDualPrntDual
  >
  :
  I@065/0002
  $ref
}
{
  !Du I@006/0003
  PrntCnstPrntDualPrntDual
  |
  I@035/0003
  String
}
{
  !Du I@006/0004
  AltCnstPrntDualPrntDual
  :
  I@033/0004
  PrntCnstPrntDualPrntDual
  {
    !DF I@058/0004
    alt
    :
    I@063/0004
    Number
  }
}
{
  !In I@007/0001
  CnstPrntDualPrntInp
  :
  I@030/0001
  RefCnstPrntDualPrntInp
  <
    I@053/0001
    AltCnstPrntDualPrntInp
  >
}
{
  !In I@007/0002
  RefCnstPrntDualPrntInp
  <
    I@031/0002
    $ref
    :PrntCnstPrntDualPrntInp
  >
  :
  I@064/0002
  $ref
}
{
  !Du I@006/0003
  PrntCnstPrntDualPrntInp
  |
  I@034/0003
  String
}
{
  !In I@007/0004
  AltCnstPrntDualPrntInp
  :
  I@033/0004
  PrntCnstPrntDualPrntInp
  {
    !IF I@057/0004
    alt
    :
    I@062/0004
    Number
  }
}
{
  !Ou I@008/0001
  CnstPrntDualPrntOutp
  :
  I@032/0001
  RefCnstPrntDualPrntOutp
  <
    I@056/0001
    AltCnstPrntDualPrntOutp
  >
}
{
  !Ou I@008/0002
  RefCnstPrntDualPrntOutp
  <
    I@033/0002
    $ref
    :PrntCnstPrntDualPrntOutp
  >
  :
  I@067/0002
  $ref
}
{
  !Du I@006/0003
  PrntCnstPrntDualPrntOutp
  |
  I@035/0003
  String
}
{
  !Ou I@008/0004
  AltCnstPrntDualPrntOutp
  :
  I@035/0004
  PrntCnstPrntDualPrntOutp
  {
    !OF I@060/0004
    alt
    :
    I@065/0004
    Number
  }
}
{
  !Du I@006/0001
  CnstPrntObjPrntDual
  :
  I@029/0001
  RefCnstPrntObjPrntDual
  <
    I@052/0001
    AltCnstPrntObjPrntDual
  >
}
{
  !Du I@006/0002
  RefCnstPrntObjPrntDual
  <
    I@030/0002
    $ref
    :PrntCnstPrntObjPrntDual
  >
  :
  I@063/0002
  $ref
}
{
  !Du I@006/0003
  PrntCnstPrntObjPrntDual
  |
  I@034/0003
  String
}
{
  !Du I@006/0004
  AltCnstPrntObjPrntDual
  :
  I@032/0004
  PrntCnstPrntObjPrntDual
  {
    !DF I@056/0004
    alt
    :
    I@061/0004
    Number
  }
}
{
  !In I@007/0001
  CnstPrntObjPrntInp
  :
  I@029/0001
  RefCnstPrntObjPrntInp
  <
    I@051/0001
    AltCnstPrntObjPrntInp
  >
}
{
  !In I@007/0002
  RefCnstPrntObjPrntInp
  <
    I@030/0002
    $ref
    :PrntCnstPrntObjPrntInp
  >
  :
  I@062/0002
  $ref
}
{
  !In I@007/0003
  PrntCnstPrntObjPrntInp
  |
  I@034/0003
  String
}
{
  !In I@007/0004
  AltCnstPrntObjPrntInp
  :
  I@032/0004
  PrntCnstPrntObjPrntInp
  {
    !IF I@055/0004
    alt
    :
    I@060/0004
    Number
  }
}
{
  !Ou I@008/0001
  CnstPrntObjPrntOutp
  :
  I@031/0001
  RefCnstPrntObjPrntOutp
  <
    I@054/0001
    AltCnstPrntObjPrntOutp
  >
}
{
  !Ou I@008/0002
  RefCnstPrntObjPrntOutp
  <
    I@032/0002
    $ref
    :PrntCnstPrntObjPrntOutp
  >
  :
  I@065/0002
  $ref
}
{
  !Ou I@008/0003
  PrntCnstPrntObjPrntOutp
  |
  I@036/0003
  String
}
{
  !Ou I@008/0004
  AltCnstPrntObjPrntOutp
  :
  I@034/0004
  PrntCnstPrntObjPrntOutp
  {
    !OF I@058/0004
    alt
    :
    I@063/0004
    Number
  }
}
{
  !Du I@006/0001
  FieldDual
  {
    !DF I@018/0001
    field
    :
    I@025/0001
    String
  }
}
{
  !In I@007/0001
  FieldInp
  {
    !IF I@018/0001
    field
    :
    I@025/0001
    String
  }
}
{
  !Ou I@008/0001
  FieldOutp
  {
    !OF I@020/0001
    field
    :
    I@027/0001
    String
  }
}
{
  !Du I@006/0001
  FieldDescrDual
  {
    'Test Descr'
    !DF I@038/0001
    field
    :
    I@045/0001
    String
  }
}
{
  !In I@007/0001
  FieldDescrInp
  {
    'Test Descr'
    !IF I@038/0001
    field
    :
    I@045/0001
    String
  }
}
{
  !Ou I@008/0001
  FieldDescrOutp
  {
    'Test Descr'
    !OF I@040/0001
    field
    :
    I@047/0001
    String
  }
}
{
  !Du I@006/0001
  FieldDualDual
  {
    !DF I@022/0001
    field
    :
    I@029/0001
    FldFieldDualDual
  }
}
{
  !Du I@006/0002
  FldFieldDualDual
  {
    !DF I@025/0002
    field
    :
    I@032/0002
    Number
  }
  |
  I@041/0002
  String
}
{
  !In I@007/0001
  FieldDualInp
  {
    !IF I@022/0001
    field
    :
    I@029/0001
    FldFieldDualInp
  }
}
{
  !Du I@006/0002
  FldFieldDualInp
  {
    !DF I@024/0002
    field
    :
    I@031/0002
    Number
  }
  |
  I@040/0002
  String
}
{
  !Ou I@008/0001
  FieldDualOutp
  {
    !OF I@024/0001
    field
    :
    I@031/0001
    FldFieldDualOutp
  }
}
{
  !Du I@006/0002
  FldFieldDualOutp
  {
    !DF I@025/0002
    field
    :
    I@032/0002
    Number
  }
  |
  I@041/0002
  String
}
{
  !Du I@006/0001
  FieldModEnumDual
  {
    !DF I@025/0001
    field
    :
    I@032/0001
    String
    [EnumFieldModEnumDual]
  }
}
{
  !En I@006/0002
  EnumFieldModEnumDual
  !EL I@029/0002
  value
}
{
  !In I@007/0001
  FieldModEnumInp
  {
    !IF I@025/0001
    field
    :
    I@032/0001
    String
    [EnumFieldModEnumInp]
  }
}
{
  !En I@006/0002
  EnumFieldModEnumInp
  !EL I@028/0002
  value
}
{
  !Ou I@008/0001
  FieldModEnumOutp
  {
    !OF I@027/0001
    field
    :
    I@034/0001
    String
    [EnumFieldModEnumOutp]
  }
}
{
  !En I@006/0002
  EnumFieldModEnumOutp
  !EL I@029/0002
  value
}
{
  !Du I@006/0001
  FieldModParamDual
  <
    I@025/0001
    $mod
    :String
  >
  {
    !DF I@039/0001
    field
    :
    I@046/0001
    FldFieldModParamDual
    [$mod]
  }
}
{
  !Du I@006/0002
  FldFieldModParamDual
  {
    !DF I@029/0002
    field
    :
    I@036/0002
    Number
  }
  |
  I@045/0002
  String
}
{
  !In I@007/0001
  FieldModParamInp
  <
    I@025/0001
    $mod
    :String
  >
  {
    !IF I@039/0001
    field
    :
    I@046/0001
    FldFieldModParamInp
    [$mod]
  }
}
{
  !In I@007/0002
  FldFieldModParamInp
  {
    !IF I@029/0002
    field
    :
    I@036/0002
    Number
  }
  |
  I@045/0002
  String
}
{
  !Ou I@008/0001
  FieldModParamOutp
  <
    I@027/0001
    $mod
    :String
  >
  {
    !OF I@041/0001
    field
    :
    I@048/0001
    FldFieldModParamOutp
    [$mod]
  }
}
{
  !Ou I@008/0002
  FldFieldModParamOutp
  {
    !OF I@031/0002
    field
    :
    I@038/0002
    Number
  }
  |
  I@047/0002
  String
}
{
  !Du I@006/0001
  FieldObjDual
  {
    !DF I@021/0001
    field
    :
    I@028/0001
    FldFieldObjDual
  }
}
{
  !Du I@006/0002
  FldFieldObjDual
  {
    !DF I@024/0002
    field
    :
    I@031/0002
    Number
  }
  |
  I@040/0002
  String
}
{
  !In I@007/0001
  FieldObjInp
  {
    !IF I@021/0001
    field
    :
    I@028/0001
    FldFieldObjInp
  }
}
{
  !In I@007/0002
  FldFieldObjInp
  {
    !IF I@024/0002
    field
    :
    I@031/0002
    Number
  }
  |
  I@040/0002
  String
}
{
  !Ou I@008/0001
  FieldObjOutp
  {
    !OF I@023/0001
    field
    :
    I@030/0001
    FldFieldObjOutp
  }
}
{
  !Ou I@008/0002
  FldFieldObjOutp
  {
    !OF I@026/0002
    field
    :
    I@033/0002
    Number
  }
  |
  I@042/0002
  String
}
{
  !Du I@006/0001
  FieldSmplDual
  {
    !DF I@022/0001
    field
    :
    I@029/0001
    Number
  }
}
{
  !In I@007/0001
  FieldSmplInp
  {
    !IF I@022/0001
    field
    :
    I@029/0001
    Number
  }
}
{
  !Ou I@008/0001
  FieldSmplOutp
  {
    !OF I@024/0001
    field
    :
    I@031/0001
    Number
  }
}
{
  !Du I@006/0001
  FieldTypeDescrDual
  {
    !DF I@027/0001
    field
    :
    'Test Descr'
    I@049/0001
    Number
  }
}
{
  !In I@007/0001
  FieldTypeDescrInp
  {
    !IF I@027/0001
    field
    :
    'Test Descr'
    I@049/0001
    Number
  }
}
{
  !Ou I@008/0001
  FieldTypeDescrOutp
  {
    !OF I@029/0001
    field
    :
    'Test Descr'
    I@051/0001
    Number
  }
}
{
  !Du I@006/0001
  GnrcAltDual
  <
    I@019/0001
    $type
    :String
  >
  |
  P@036/0001
  $type
}
{
  !In I@007/0001
  GnrcAltInp
  <
    I@019/0001
    $type
    :String
  >
  |
  P@036/0001
  $type
}
{
  !Ou I@008/0001
  GnrcAltOutp
  <
    I@021/0001
    $type
    :String
  >
  |
  P@038/0001
  $type
}
{
  !Du I@006/0001
  GnrcAltArgDual
  <
    I@022/0001
    $type
    :String
  >
  |
  I@039/0001
  RefGnrcAltArgDual
  <
    I@058/0001
    $type
  >
}
{
  !Du I@006/0002
  RefGnrcAltArgDual
  <
    I@025/0002
    $ref
    :String
  >
  |
  P@041/0002
  $ref
}
{
  !In I@007/0001
  GnrcAltArgInp
  <
    I@022/0001
    $type
    :String
  >
  |
  I@039/0001
  RefGnrcAltArgInp
  <
    I@057/0001
    $type
  >
}
{
  !In I@007/0002
  RefGnrcAltArgInp
  <
    I@025/0002
    $ref
    :String
  >
  |
  P@041/0002
  $ref
}
{
  !Ou I@008/0001
  GnrcAltArgOutp
  <
    I@024/0001
    $type
    :String
  >
  |
  I@041/0001
  RefGnrcAltArgOutp
  <
    I@060/0001
    $type
  >
}
{
  !Ou I@008/0002
  RefGnrcAltArgOutp
  <
    I@027/0002
    $ref
    :String
  >
  |
  P@043/0002
  $ref
}
{
  !Du I@006/0001
  GnrcAltArgDescrDual
  <
    I@027/0001
    $type
    :String
  >
  |
  I@044/0001
  RefGnrcAltArgDescrDual
  <
    'Test Descr'
    I@082/0001
    $type
  >
}
{
  !Du I@006/0002
  RefGnrcAltArgDescrDual
  <
    I@030/0002
    $ref
    :String
  >
  |
  P@046/0002
  $ref
}
{
  !In I@007/0001
  GnrcAltArgDescrInp
  <
    I@027/0001
    $type
    :String
  >
  |
  I@044/0001
  RefGnrcAltArgDescrInp
  <
    'Test Descr'
    I@081/0001
    $type
  >
}
{
  !In I@007/0002
  RefGnrcAltArgDescrInp
  <
    I@030/0002
    $ref
    :String
  >
  |
  P@046/0002
  $ref
}
{
  !Ou I@008/0001
  GnrcAltArgDescrOutp
  <
    I@029/0001
    $type
    :String
  >
  |
  I@046/0001
  RefGnrcAltArgDescrOutp
  <
    'Test Descr'
    I@084/0001
    $type
  >
}
{
  !Ou I@008/0002
  RefGnrcAltArgDescrOutp
  <
    I@032/0002
    $ref
    :String
  >
  |
  P@048/0002
  $ref
}
{
  !Du I@006/0001
  GnrcAltDualDual
  |
  I@026/0001
  RefGnrcAltDualDual
  <
    I@045/0001
    AltGnrcAltDualDual
  >
}
{
  !Du I@006/0002
  RefGnrcAltDualDual
  <
    I@026/0002
    $ref
    :_Dual
  >
  |
  P@041/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcAltDualDual
  {
    !DF I@027/0003
    alt
    :
    I@032/0003
    Number
  }
  |
  I@041/0003
  String
}
{
  !In I@007/0001
  GnrcAltDualInp
  |
  I@026/0001
  RefGnrcAltDualInp
  <
    I@044/0001
    AltGnrcAltDualInp
  >
}
{
  !In I@007/0002
  RefGnrcAltDualInp
  <
    I@026/0002
    $ref
    :_Dual
  >
  |
  P@041/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcAltDualInp
  {
    !DF I@026/0003
    alt
    :
    I@031/0003
    Number
  }
  |
  I@040/0003
  String
}
{
  !Ou I@008/0001
  GnrcAltDualOutp
  |
  I@028/0001
  RefGnrcAltDualOutp
  <
    I@047/0001
    AltGnrcAltDualOutp
  >
}
{
  !Ou I@008/0002
  RefGnrcAltDualOutp
  <
    I@028/0002
    $ref
    :_Dual
  >
  |
  P@043/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcAltDualOutp
  {
    !DF I@027/0003
    alt
    :
    I@032/0003
    Number
  }
  |
  I@041/0003
  String
}
{
  !Du I@006/0001
  RefGnrcAltModParamDual
  <
    I@030/0001
    $ref
    :String
    I@042/0001
    $mod
    :String
  >
  |
  P@058/0001
  $ref
  [$mod]
}
{
  !In I@007/0001
  RefGnrcAltModParamInp
  <
    I@030/0001
    $ref
    :String
    I@042/0001
    $mod
    :String
  >
  |
  P@058/0001
  $ref
  [$mod]
}
{
  !Ou I@008/0001
  RefGnrcAltModParamOutp
  <
    I@032/0001
    $ref
    :String
    I@044/0001
    $mod
    :String
  >
  |
  P@060/0001
  $ref
  [$mod]
}
{
  !Du I@006/0001
  RefGnrcAltModStrDual
  <
    I@028/0001
    $ref
    :String
  >
  |
  P@044/0001
  $ref
  [*]
}
{
  !In I@007/0001
  RefGnrcAltModStrInp
  <
    I@028/0001
    $ref
    :String
  >
  |
  P@044/0001
  $ref
  [*]
}
{
  !Ou I@008/0001
  RefGnrcAltModStrOutp
  <
    I@030/0001
    $ref
    :String
  >
  |
  P@046/0001
  $ref
  [*]
}
{
  !Du I@006/0001
  GnrcAltParamDual
  |
  I@027/0001
  RefGnrcAltParamDual
  <
    I@047/0001
    AltGnrcAltParamDual
  >
}
{
  !Du I@006/0002
  RefGnrcAltParamDual
  <
    I@027/0002
    $ref
    :_Dual
  >
  |
  P@042/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcAltParamDual
  {
    !DF I@028/0003
    alt
    :
    I@033/0003
    Number
  }
  |
  I@042/0003
  String
}
{
  !In I@007/0001
  GnrcAltParamInp
  |
  I@027/0001
  RefGnrcAltParamInp
  <
    I@046/0001
    AltGnrcAltParamInp
  >
}
{
  !In I@007/0002
  RefGnrcAltParamInp
  <
    I@027/0002
    $ref
    :_Input
  >
  |
  P@043/0002
  $ref
}
{
  !In I@007/0003
  AltGnrcAltParamInp
  {
    !IF I@028/0003
    alt
    :
    I@033/0003
    Number
  }
  |
  I@042/0003
  String
}
{
  !Ou I@008/0001
  GnrcAltParamOutp
  |
  I@029/0001
  RefGnrcAltParamOutp
  <
    I@049/0001
    AltGnrcAltParamOutp
  >
}
{
  !Ou I@008/0002
  RefGnrcAltParamOutp
  <
    I@029/0002
    $ref
    :_Output
  >
  |
  P@046/0002
  $ref
}
{
  !Ou I@008/0003
  AltGnrcAltParamOutp
  {
    !OF I@030/0003
    alt
    :
    I@035/0003
    Number
  }
  |
  I@044/0003
  String
}
{
  !Du I@006/0001
  GnrcAltSmplDual
  |
  I@026/0001
  RefGnrcAltSmplDual
  <
    I@045/0001
    String
  >
}
{
  !Du I@006/0002
  RefGnrcAltSmplDual
  <
    I@026/0002
    $ref
    :String
  >
  |
  P@042/0002
  $ref
}
{
  !In I@007/0001
  GnrcAltSmplInp
  |
  I@026/0001
  RefGnrcAltSmplInp
  <
    I@044/0001
    String
  >
}
{
  !In I@007/0002
  RefGnrcAltSmplInp
  <
    I@026/0002
    $ref
    :String
  >
  |
  P@042/0002
  $ref
}
{
  !Ou I@008/0001
  GnrcAltSmplOutp
  |
  I@028/0001
  RefGnrcAltSmplOutp
  <
    I@047/0001
    String
  >
}
{
  !Ou I@008/0002
  RefGnrcAltSmplOutp
  <
    I@028/0002
    $ref
    :String
  >
  |
  P@044/0002
  $ref
}
{
  !Du I@006/0001
  GnrcDescrDual
  <
    I@036/0001
    "Test Descr"
    $type
    :String
  >
  {
    !DF I@051/0001
    field
    :
    I@059/0001
    $type
  }
}
{
  !In I@007/0001
  GnrcDescrInp
  <
    I@036/0001
    "Test Descr"
    $type
    :String
  >
  {
    !IF I@051/0001
    field
    :
    I@059/0001
    $type
  }
}
{
  !Ou I@008/0001
  GnrcDescrOutp
  <
    I@038/0001
    "Test Descr"
    $type
    :String
  >
  {
    !OF I@053/0001
    field
    :
    I@061/0001
    $type
  }
}
{
  !Du I@006/0001
  GnrcFieldDual
  <
    I@021/0001
    $type
    :String
  >
  {
    !DF I@036/0001
    field
    :
    I@044/0001
    $type
  }
}
{
  !In I@007/0001
  GnrcFieldInp
  <
    I@021/0001
    $type
    :String
  >
  {
    !IF I@036/0001
    field
    :
    I@044/0001
    $type
  }
}
{
  !Ou I@008/0001
  GnrcFieldOutp
  <
    I@023/0001
    $type
    :String
  >
  {
    !OF I@038/0001
    field
    :
    I@046/0001
    $type
  }
}
{
  !Du I@006/0001
  GnrcFieldArgDual
  <
    I@024/0001
    $type
    :String
  >
  {
    !DF I@039/0001
    field
    :
    I@046/0001
    RefGnrcFieldArgDual
    <
      I@067/0001
      $type
    >
  }
}
{
  !Du I@006/0002
  RefGnrcFieldArgDual
  <
    I@027/0002
    $ref
    :String
  >
  |
  P@043/0002
  $ref
}
{
  !In I@007/0001
  GnrcFieldArgInp
  <
    I@024/0001
    $type
    :String
  >
  {
    !IF I@039/0001
    field
    :
    I@046/0001
    RefGnrcFieldArgInp
    <
      I@066/0001
      $type
    >
  }
}
{
  !In I@007/0002
  RefGnrcFieldArgInp
  <
    I@027/0002
    $ref
    :String
  >
  |
  P@043/0002
  $ref
}
{
  !Ou I@008/0001
  GnrcFieldArgOutp
  <
    I@026/0001
    $type
    :String
  >
  {
    !OF I@041/0001
    field
    :
    I@048/0001
    RefGnrcFieldArgOutp
    <
      I@069/0001
      $type
    >
  }
}
{
  !Ou I@008/0002
  RefGnrcFieldArgOutp
  <
    I@029/0002
    $ref
    :String
  >
  |
  P@045/0002
  $ref
}
{
  !Du I@006/0001
  GnrcFieldDualDual
  {
    !DF I@026/0001
    field
    :
    I@033/0001
    RefGnrcFieldDualDual
    <
      I@054/0001
      AltGnrcFieldDualDual
    >
  }
}
{
  !Du I@006/0002
  RefGnrcFieldDualDual
  <
    I@028/0002
    $ref
    :_Dual
  >
  |
  P@043/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcFieldDualDual
  {
    !DF I@029/0003
    alt
    :
    I@034/0003
    Number
  }
  |
  I@043/0003
  String
}
{
  !In I@007/0001
  GnrcFieldDualInp
  {
    !IF I@026/0001
    field
    :
    I@033/0001
    RefGnrcFieldDualInp
    <
      I@053/0001
      AltGnrcFieldDualInp
    >
  }
}
{
  !In I@007/0002
  RefGnrcFieldDualInp
  <
    I@028/0002
    $ref
    :_Dual
  >
  |
  P@043/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcFieldDualInp
  {
    !DF I@028/0003
    alt
    :
    I@033/0003
    Number
  }
  |
  I@042/0003
  String
}
{
  !Ou I@008/0001
  GnrcFieldDualOutp
  {
    !OF I@028/0001
    field
    :
    I@035/0001
    RefGnrcFieldDualOutp
    <
      I@056/0001
      AltGnrcFieldDualOutp
    >
  }
}
{
  !Ou I@008/0002
  RefGnrcFieldDualOutp
  <
    I@030/0002
    $ref
    :_Dual
  >
  |
  P@045/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcFieldDualOutp
  {
    !DF I@029/0003
    alt
    :
    I@034/0003
    Number
  }
  |
  I@043/0003
  String
}
{
  !Du I@006/0001
  GnrcFieldParamDual
  {
    !DF I@027/0001
    field
    :
    I@034/0001
    RefGnrcFieldParamDual
    <
      I@056/0001
      AltGnrcFieldParamDual
    >
  }
}
{
  !Du I@006/0002
  RefGnrcFieldParamDual
  <
    I@029/0002
    $ref
    :_Dual
  >
  |
  P@044/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcFieldParamDual
  {
    !DF I@030/0003
    alt
    :
    I@035/0003
    Number
  }
  |
  I@044/0003
  String
}
{
  !In I@007/0001
  GnrcFieldParamInp
  {
    !IF I@027/0001
    field
    :
    I@034/0001
    RefGnrcFieldParamInp
    <
      I@055/0001
      AltGnrcFieldParamInp
    >
  }
}
{
  !In I@007/0002
  RefGnrcFieldParamInp
  <
    I@029/0002
    $ref
    :_Input
  >
  |
  P@045/0002
  $ref
}
{
  !In I@007/0003
  AltGnrcFieldParamInp
  {
    !IF I@030/0003
    alt
    :
    I@035/0003
    Number
  }
  |
  I@044/0003
  String
}
{
  !Ou I@008/0001
  GnrcFieldParamOutp
  {
    !OF I@029/0001
    field
    :
    I@036/0001
    RefGnrcFieldParamOutp
    <
      I@058/0001
      AltGnrcFieldParamOutp
    >
  }
}
{
  !Ou I@008/0002
  RefGnrcFieldParamOutp
  <
    I@031/0002
    $ref
    :_Output
  >
  |
  P@048/0002
  $ref
}
{
  !Ou I@008/0003
  AltGnrcFieldParamOutp
  {
    !OF I@032/0003
    alt
    :
    I@037/0003
    Number
  }
  |
  I@046/0003
  String
}
{
  !Du I@006/0001
  GnrcPrntDual
  <
    I@020/0001
    $type
    :String
  >
  :
  I@037/0001
  $type
}
{
  !In I@007/0001
  GnrcPrntInp
  <
    I@020/0001
    $type
    :String
  >
  :
  I@037/0001
  $type
}
{
  !Ou I@008/0001
  GnrcPrntOutp
  <
    I@022/0001
    $type
    :String
  >
  :
  I@039/0001
  $type
}
{
  !Du I@006/0001
  GnrcPrntArgDual
  <
    I@023/0001
    $type
    :String
  >
  :
  I@039/0001
  RefGnrcPrntArgDual
  <
    I@059/0001
    $type
  >
}
{
  !Du I@006/0002
  RefGnrcPrntArgDual
  <
    I@026/0002
    $ref
    :String
  >
  |
  P@042/0002
  $ref
}
{
  !In I@007/0001
  GnrcPrntArgInp
  <
    I@023/0001
    $type
    :String
  >
  :
  I@039/0001
  RefGnrcPrntArgInp
  <
    I@058/0001
    $type
  >
}
{
  !In I@007/0002
  RefGnrcPrntArgInp
  <
    I@026/0002
    $ref
    :String
  >
  |
  P@042/0002
  $ref
}
{
  !Ou I@008/0001
  GnrcPrntArgOutp
  <
    I@025/0001
    $type
    :String
  >
  :
  I@041/0001
  RefGnrcPrntArgOutp
  <
    I@061/0001
    $type
  >
}
{
  !Ou I@008/0002
  RefGnrcPrntArgOutp
  <
    I@028/0002
    $ref
    :String
  >
  |
  P@044/0002
  $ref
}
{
  !Du I@006/0001
  GnrcPrntDescrDual
  <
    I@025/0001
    $type
    :String
  >
  :
  'Parent comment'
  I@058/0001
  $type
}
{
  !In I@007/0001
  GnrcPrntDescrInp
  <
    I@025/0001
    $type
    :String
  >
  :
  'Parent comment'
  I@058/0001
  $type
}
{
  !Ou I@008/0001
  GnrcPrntDescrOutp
  <
    I@027/0001
    $type
    :String
  >
  :
  'Parent comment'
  I@060/0001
  $type
}
{
  !Du I@006/0001
  GnrcPrntDualDual
  :
  I@026/0001
  RefGnrcPrntDualDual
  <
    I@046/0001
    AltGnrcPrntDualDual
  >
}
{
  !Du I@006/0002
  RefGnrcPrntDualDual
  <
    I@027/0002
    $ref
    :_Dual
  >
  |
  P@042/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcPrntDualDual
  {
    !DF I@028/0003
    alt
    :
    I@033/0003
    Number
  }
  |
  I@042/0003
  String
}
{
  !In I@007/0001
  GnrcPrntDualInp
  :
  I@026/0001
  RefGnrcPrntDualInp
  <
    I@045/0001
    AltGnrcPrntDualInp
  >
}
{
  !In I@007/0002
  RefGnrcPrntDualInp
  <
    I@027/0002
    $ref
    :_Dual
  >
  |
  P@042/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcPrntDualInp
  {
    !DF I@027/0003
    alt
    :
    I@032/0003
    Number
  }
  |
  I@041/0003
  String
}
{
  !Ou I@008/0001
  GnrcPrntDualOutp
  :
  I@028/0001
  RefGnrcPrntDualOutp
  <
    I@048/0001
    AltGnrcPrntDualOutp
  >
}
{
  !Ou I@008/0002
  RefGnrcPrntDualOutp
  <
    I@029/0002
    $ref
    :_Dual
  >
  |
  P@044/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcPrntDualOutp
  {
    !DF I@028/0003
    alt
    :
    I@033/0003
    Number
  }
  |
  I@042/0003
  String
}
{
  !Du I@006/0001
  GnrcPrntDualPrntDual
  :
  I@030/0001
  RefGnrcPrntDualPrntDual
  <
    I@054/0001
    AltGnrcPrntDualPrntDual
  >
}
{
  !Du I@006/0002
  RefGnrcPrntDualPrntDual
  <
    I@031/0002
    $ref
    :_Dual
  >
  :
  I@046/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcPrntDualPrntDual
  {
    !DF I@032/0003
    alt
    :
    I@037/0003
    Number
  }
  |
  I@046/0003
  String
}
{
  !In I@007/0001
  GnrcPrntDualPrntInp
  :
  I@030/0001
  RefGnrcPrntDualPrntInp
  <
    I@053/0001
    AltGnrcPrntDualPrntInp
  >
}
{
  !In I@007/0002
  RefGnrcPrntDualPrntInp
  <
    I@031/0002
    $ref
    :_Dual
  >
  :
  I@046/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcPrntDualPrntInp
  {
    !DF I@031/0003
    alt
    :
    I@036/0003
    Number
  }
  |
  I@045/0003
  String
}
{
  !Ou I@008/0001
  GnrcPrntDualPrntOutp
  :
  I@032/0001
  RefGnrcPrntDualPrntOutp
  <
    I@056/0001
    AltGnrcPrntDualPrntOutp
  >
}
{
  !Ou I@008/0002
  RefGnrcPrntDualPrntOutp
  <
    I@033/0002
    $ref
    :_Dual
  >
  :
  I@048/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcPrntDualPrntOutp
  {
    !DF I@032/0003
    alt
    :
    I@037/0003
    Number
  }
  |
  I@046/0003
  String
}
{
  !Du I@006/0001
  GnrcPrntEnumChildDual
  :
  I@031/0001
  FieldGnrcPrntEnumChildDual
  <
    I@058/0001
    ParentGnrcPrntEnumChildDual
  >
}
{
  !Du I@006/0002
  FieldGnrcPrntEnumChildDual
  <
    I@034/0002
    $ref
    :EnumGnrcPrntEnumChildDual
  >
  {
    !DF I@067/0002
    field
    :
    I@075/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntEnumChildDual
  :( !Tr I@035/0003 ParentGnrcPrntEnumChildDual )
  !EL I@063/0003
  gnrcPrntEnumChildDualLabel
}
{
  !En I@006/0004
  ParentGnrcPrntEnumChildDual
  !EL I@036/0004
  gnrcPrntEnumChildDualParent
}
{
  !In I@007/0001
  GnrcPrntEnumChildInp
  :
  I@031/0001
  FieldGnrcPrntEnumChildInp
  <
    I@057/0001
    ParentGnrcPrntEnumChildInp
  >
}
{
  !In I@007/0002
  FieldGnrcPrntEnumChildInp
  <
    I@034/0002
    $ref
    :EnumGnrcPrntEnumChildInp
  >
  {
    !IF I@066/0002
    field
    :
    I@074/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntEnumChildInp
  :( !Tr I@034/0003 ParentGnrcPrntEnumChildInp )
  !EL I@061/0003
  gnrcPrntEnumChildInpLabel
}
{
  !En I@006/0004
  ParentGnrcPrntEnumChildInp
  !EL I@035/0004
  gnrcPrntEnumChildInpParent
}
{
  !Ou I@008/0001
  GnrcPrntEnumChildOutp
  :
  I@033/0001
  FieldGnrcPrntEnumChildOutp
  <
    I@060/0001
    ParentGnrcPrntEnumChildOutp
  >
}
{
  !Ou I@008/0002
  FieldGnrcPrntEnumChildOutp
  <
    I@036/0002
    $ref
    :EnumGnrcPrntEnumChildOutp
  >
  {
    !OF I@069/0002
    field
    :
    I@077/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntEnumChildOutp
  :( !Tr I@035/0003 ParentGnrcPrntEnumChildOutp )
  !EL I@063/0003
  gnrcPrntEnumChildOutpLabel
}
{
  !En I@006/0004
  ParentGnrcPrntEnumChildOutp
  !EL I@036/0004
  gnrcPrntEnumChildOutpParent
}
{
  !Du I@006/0001
  GnrcPrntEnumDomDual
  :
  I@029/0001
  FieldGnrcPrntEnumDomDual
  <
    I@054/0001
    DomGnrcPrntEnumDomDual
  >
}
{
  !Du I@006/0002
  FieldGnrcPrntEnumDomDual
  <
    I@032/0002
    $ref
    :EnumGnrcPrntEnumDomDual
  >
  {
    !DF I@063/0002
    field
    :
    I@071/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntEnumDomDual
  !EL I@032/0003
  gnrcPrntEnumDomDualLabel
  !EL I@057/0003
  gnrcPrntEnumDomDualOther
}
{
  !Do I@008/0004
  DomGnrcPrntEnumDomDual
  Enum
  !DE I@038/0004
  EnumGnrcPrntEnumDomDual
  gnrcPrntEnumDomDualLabel
}
{
  !In I@007/0001
  GnrcPrntEnumDomInp
  :
  I@029/0001
  FieldGnrcPrntEnumDomInp
  <
    I@053/0001
    DomGnrcPrntEnumDomInp
  >
}
{
  !In I@007/0002
  FieldGnrcPrntEnumDomInp
  <
    I@032/0002
    $ref
    :EnumGnrcPrntEnumDomInp
  >
  {
    !IF I@062/0002
    field
    :
    I@070/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntEnumDomInp
  !EL I@031/0003
  gnrcPrntEnumDomInpLabel
  !EL I@055/0003
  gnrcPrntEnumDomInpOther
}
{
  !Do I@008/0004
  DomGnrcPrntEnumDomInp
  Enum
  !DE I@037/0004
  EnumGnrcPrntEnumDomInp
  gnrcPrntEnumDomInpLabel
}
{
  !Ou I@008/0001
  GnrcPrntEnumDomOutp
  :
  I@031/0001
  FieldGnrcPrntEnumDomOutp
  <
    I@056/0001
    DomGnrcPrntEnumDomOutp
  >
}
{
  !Ou I@008/0002
  FieldGnrcPrntEnumDomOutp
  <
    I@034/0002
    $ref
    :EnumGnrcPrntEnumDomOutp
  >
  {
    !OF I@065/0002
    field
    :
    I@073/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntEnumDomOutp
  !EL I@032/0003
  gnrcPrntEnumDomOutpLabel
  !EL I@057/0003
  gnrcPrntEnumDomOutpOther
}
{
  !Do I@008/0004
  DomGnrcPrntEnumDomOutp
  Enum
  !DE I@038/0004
  EnumGnrcPrntEnumDomOutp
  gnrcPrntEnumDomOutpLabel
}
{
  !Du I@006/0001
  GnrcPrntEnumPrntDual
  :
  I@030/0001
  FieldGnrcPrntEnumPrntDual
  <
    I@056/0001
    EnumGnrcPrntEnumPrntDual
  >
}
{
  !Du I@006/0002
  FieldGnrcPrntEnumPrntDual
  <
    I@033/0002
    $ref
    :ParentGnrcPrntEnumPrntDual
  >
  {
    !DF I@067/0002
    field
    :
    I@075/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntEnumPrntDual
  :( !Tr I@034/0003 ParentGnrcPrntEnumPrntDual )
  !EL I@061/0003
  gnrcPrntEnumPrntDualLabel
}
{
  !En I@006/0004
  ParentGnrcPrntEnumPrntDual
  !EL I@035/0004
  gnrcPrntEnumPrntDualParent
}
{
  !In I@007/0001
  GnrcPrntEnumPrntInp
  :
  I@030/0001
  FieldGnrcPrntEnumPrntInp
  <
    I@055/0001
    EnumGnrcPrntEnumPrntInp
  >
}
{
  !In I@007/0002
  FieldGnrcPrntEnumPrntInp
  <
    I@033/0002
    $ref
    :ParentGnrcPrntEnumPrntInp
  >
  {
    !IF I@066/0002
    field
    :
    I@074/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntEnumPrntInp
  :( !Tr I@033/0003 ParentGnrcPrntEnumPrntInp )
  !EL I@059/0003
  gnrcPrntEnumPrntInpLabel
}
{
  !En I@006/0004
  ParentGnrcPrntEnumPrntInp
  !EL I@034/0004
  gnrcPrntEnumPrntInpParent
}
{
  !Ou I@008/0001
  GnrcPrntEnumPrntOutp
  :
  I@032/0001
  FieldGnrcPrntEnumPrntOutp
  <
    I@058/0001
    EnumGnrcPrntEnumPrntOutp
  >
}
{
  !Ou I@008/0002
  FieldGnrcPrntEnumPrntOutp
  <
    I@035/0002
    $ref
    :ParentGnrcPrntEnumPrntOutp
  >
  {
    !OF I@069/0002
    field
    :
    I@077/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntEnumPrntOutp
  :( !Tr I@034/0003 ParentGnrcPrntEnumPrntOutp )
  !EL I@061/0003
  gnrcPrntEnumPrntOutpLabel
}
{
  !En I@006/0004
  ParentGnrcPrntEnumPrntOutp
  !EL I@035/0004
  gnrcPrntEnumPrntOutpParent
}
{
  !Du I@006/0001
  GnrcPrntParamDual
  :
  I@027/0001
  RefGnrcPrntParamDual
  <
    I@048/0001
    AltGnrcPrntParamDual
  >
}
{
  !Du I@006/0002
  RefGnrcPrntParamDual
  <
    I@028/0002
    $ref
    :_Dual
  >
  |
  P@043/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcPrntParamDual
  {
    !DF I@029/0003
    alt
    :
    I@034/0003
    Number
  }
  |
  I@043/0003
  String
}
{
  !In I@007/0001
  GnrcPrntParamInp
  :
  I@027/0001
  RefGnrcPrntParamInp
  <
    I@047/0001
    AltGnrcPrntParamInp
  >
}
{
  !In I@007/0002
  RefGnrcPrntParamInp
  <
    I@028/0002
    $ref
    :_Input
  >
  |
  P@044/0002
  $ref
}
{
  !In I@007/0003
  AltGnrcPrntParamInp
  {
    !IF I@029/0003
    alt
    :
    I@034/0003
    Number
  }
  |
  I@043/0003
  String
}
{
  !Ou I@008/0001
  GnrcPrntParamOutp
  :
  I@029/0001
  RefGnrcPrntParamOutp
  <
    I@050/0001
    AltGnrcPrntParamOutp
  >
}
{
  !Ou I@008/0002
  RefGnrcPrntParamOutp
  <
    I@030/0002
    $ref
    :_Output
  >
  |
  P@047/0002
  $ref
}
{
  !Ou I@008/0003
  AltGnrcPrntParamOutp
  {
    !OF I@031/0003
    alt
    :
    I@036/0003
    Number
  }
  |
  I@045/0003
  String
}
{
  !Du I@006/0001
  GnrcPrntParamPrntDual
  :
  I@031/0001
  RefGnrcPrntParamPrntDual
  <
    I@056/0001
    AltGnrcPrntParamPrntDual
  >
}
{
  !Du I@006/0002
  RefGnrcPrntParamPrntDual
  <
    I@032/0002
    $ref
    :_Dual
  >
  :
  I@047/0002
  $ref
}
{
  !Du I@006/0003
  AltGnrcPrntParamPrntDual
  {
    !DF I@033/0003
    alt
    :
    I@038/0003
    Number
  }
  |
  I@047/0003
  String
}
{
  !In I@007/0001
  GnrcPrntParamPrntInp
  :
  I@031/0001
  RefGnrcPrntParamPrntInp
  <
    I@055/0001
    AltGnrcPrntParamPrntInp
  >
}
{
  !In I@007/0002
  RefGnrcPrntParamPrntInp
  <
    I@032/0002
    $ref
    :_Input
  >
  :
  I@048/0002
  $ref
}
{
  !In I@007/0003
  AltGnrcPrntParamPrntInp
  {
    !IF I@033/0003
    alt
    :
    I@038/0003
    Number
  }
  |
  I@047/0003
  String
}
{
  !Ou I@008/0001
  GnrcPrntParamPrntOutp
  :
  I@033/0001
  RefGnrcPrntParamPrntOutp
  <
    I@058/0001
    AltGnrcPrntParamPrntOutp
  >
}
{
  !Ou I@008/0002
  RefGnrcPrntParamPrntOutp
  <
    I@034/0002
    $ref
    :_Output
  >
  :
  I@051/0002
  $ref
}
{
  !Ou I@008/0003
  AltGnrcPrntParamPrntOutp
  {
    !OF I@035/0003
    alt
    :
    I@040/0003
    Number
  }
  |
  I@049/0003
  String
}
{
  !Du I@006/0001
  GnrcPrntSmplEnumDual
  :
  I@030/0001
  FieldGnrcPrntSmplEnumDual
  <
    I@056/0001
    EnumGnrcPrntSmplEnumDual
  >
}
{
  !Du I@006/0002
  FieldGnrcPrntSmplEnumDual
  <
    I@033/0002
    $ref
    :_Simple
  >
  {
    !DF I@048/0002
    field
    :
    I@056/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntSmplEnumDual
  !EL I@033/0003
  gnrcPrntSmplEnumDual
}
{
  !In I@007/0001
  GnrcPrntSmplEnumInp
  :
  I@030/0001
  FieldGnrcPrntSmplEnumInp
  <
    I@055/0001
    EnumGnrcPrntSmplEnumInp
  >
}
{
  !In I@007/0002
  FieldGnrcPrntSmplEnumInp
  <
    I@033/0002
    $ref
    :_Simple
  >
  {
    !IF I@048/0002
    field
    :
    I@056/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntSmplEnumInp
  !EL I@032/0003
  gnrcPrntSmplEnumInp
}
{
  !Ou I@008/0001
  GnrcPrntSmplEnumOutp
  :
  I@032/0001
  FieldGnrcPrntSmplEnumOutp
  <
    I@058/0001
    EnumGnrcPrntSmplEnumOutp
  >
}
{
  !Ou I@008/0002
  FieldGnrcPrntSmplEnumOutp
  <
    I@035/0002
    $ref
    :_Simple
  >
  {
    !OF I@050/0002
    field
    :
    I@058/0002
    $ref
  }
}
{
  !En I@006/0003
  EnumGnrcPrntSmplEnumOutp
  !EL I@033/0003
  gnrcPrntSmplEnumOutp
}
{
  !Du I@006/0001
  GnrcPrntStrDomDual
  :
  I@028/0001
  FieldGnrcPrntStrDomDual
  <
    I@052/0001
    DomGnrcPrntStrDomDual
  >
}
{
  !Du I@006/0002
  FieldGnrcPrntStrDomDual
  <
    I@031/0002
    $ref
    :String
  >
  {
    !DF I@045/0002
    field
    :
    I@053/0002
    $ref
  }
}
{
  !Do I@008/0003
  DomGnrcPrntStrDomDual
  String
  !DX R@039/0003
  /\\w+/
}
{
  !In I@007/0001
  GnrcPrntStrDomInp
  :
  I@028/0001
  FieldGnrcPrntStrDomInp
  <
    I@051/0001
    DomGnrcPrntStrDomInp
  >
}
{
  !In I@007/0002
  FieldGnrcPrntStrDomInp
  <
    I@031/0002
    $ref
    :String
  >
  {
    !IF I@045/0002
    field
    :
    I@053/0002
    $ref
  }
}
{
  !Do I@008/0003
  DomGnrcPrntStrDomInp
  String
  !DX R@038/0003
  /\\w+/
}
{
  !Ou I@008/0001
  GnrcPrntStrDomOutp
  :
  I@030/0001
  FieldGnrcPrntStrDomOutp
  <
    I@054/0001
    DomGnrcPrntStrDomOutp
  >
}
{
  !Ou I@008/0002
  FieldGnrcPrntStrDomOutp
  <
    I@033/0002
    $ref
    :String
  >
  {
    !OF I@047/0002
    field
    :
    I@055/0002
    $ref
  }
}
{
  !Do I@008/0003
  DomGnrcPrntStrDomOutp
  String
  !DX R@039/0003
  /\\w+/
}
{
  !In I@007/0001
  InpFieldDescrNmbr
  {
    'Test Descr'
    !IF I@042/0001
    field
    :
    I@049/0001
    Number
    =( !k N@058/0001 42 )
  }
}
{
  !In I@007/0001
  InpFieldEnum
  {
    !IF I@022/0001
    field
    :
    I@029/0001
    EnumInpFieldEnum
    =( !k I@048/0001 inpFieldEnum )
  }
}
{
  !En I@006/0002
  EnumInpFieldEnum
  !EL I@025/0002
  inpFieldEnum
}
{
  !In I@007/0001
  InpFieldNull
  {
    !IF I@022/0001
    field
    :
    I@029/0001
    FldInpFieldNull
    ?
    =( !k I@048/0001 Null.null )
  }
}
{
  !Du I@006/0002
  FldInpFieldNull
}
{
  !In I@007/0001
  InpFieldNmbr
  {
    !IF I@022/0001
    field
    :
    I@029/0001
    Number
    =( !k N@038/0001 42 )
  }
}
{
  !In I@007/0001
  InpFieldNmbrDescr
  {
    !IF I@027/0001
    field
    :
    'Test Descr'
    I@049/0001
    Number
    =( !k N@058/0001 42 )
  }
}
{
  !In I@007/0001
  InpFieldStr
  {
    !IF I@021/0001
    field
    :
    I@028/0001
    String
    =( !k S@037/0001 'default' )
  }
}
{
  !Ou I@008/0001
  OutpCnstDomEnum
  |
  I@028/0001
  RefOutpCnstDomEnum
  <
    I@047/0001
    outpCnstDomEnum
  >
}
{
  !Ou I@008/0002
  RefOutpCnstDomEnum
  <
    I@028/0002
    $type
    :JustOutpCnstDomEnum
  >
  {
    !OF I@056/0002
    field
    :
    I@064/0002
    $type
  }
}
{
  !En I@006/0003
  EnumOutpCnstDomEnum
  !EL I@028/0003
  outpCnstDomEnum
  !EL I@044/0003
  other
}
{
  !Do I@008/0004
  JustOutpCnstDomEnum
  Enum
  !DE I@035/0004
  EnumOutpCnstDomEnum
  outpCnstDomEnum
}
{
  !Ou I@008/0001
  OutpCnstEnum
  |
  I@025/0001
  RefOutpCnstEnum
  <
    I@041/0001
    outpCnstEnum
  >
}
{
  !Ou I@008/0002
  RefOutpCnstEnum
  <
    I@025/0002
    $type
    :EnumOutpCnstEnum
  >
  {
    !OF I@050/0002
    field
    :
    I@058/0002
    $type
  }
}
{
  !En I@006/0003
  EnumOutpCnstEnum
  !EL I@025/0003
  outpCnstEnum
}
{
  !Ou I@008/0001
  OutpCnstEnumPrnt
  |
  I@029/0001
  RefOutpCnstEnumPrnt
  <
    I@049/0001
    outpCnstEnumPrnt
  >
}
{
  !Ou I@008/0002
  RefOutpCnstEnumPrnt
  <
    I@029/0002
    $type
    :ParentOutpCnstEnumPrnt
  >
  {
    !OF I@060/0002
    field
    :
    I@068/0002
    $type
  }
}
{
  !En I@006/0003
  EnumOutpCnstEnumPrnt
  :( !Tr I@030/0003 ParentOutpCnstEnumPrnt )
  !EL I@053/0003
  outpCnstEnumPrnt
}
{
  !En I@006/0004
  ParentOutpCnstEnumPrnt
  !EL I@031/0004
  parentOutpCnstEnumPrnt
}
{
  !Ou I@008/0001
  OutpCnstPrntEnum
  |
  I@029/0001
  RefOutpCnstPrntEnum
  <
    I@049/0001
    parentOutpCnstPrntEnum
  >
}
{
  !Ou I@008/0002
  RefOutpCnstPrntEnum
  <
    I@029/0002
    $type
    :EnumOutpCnstPrntEnum
  >
  {
    !OF I@058/0002
    field
    :
    I@066/0002
    $type
  }
}
{
  !En I@006/0003
  EnumOutpCnstPrntEnum
  :( !Tr I@030/0003 ParentOutpCnstPrntEnum )
  !EL I@053/0003
  outpCnstPrntEnum
}
{
  !En I@006/0004
  ParentOutpCnstPrntEnum
  !EL I@031/0004
  parentOutpCnstPrntEnum
}
{
  !Ou I@008/0001
  OutpDescrParam
  {
    'Test Descr'
    !OF I@040/0001
    field
    (
      !Pa
      I@046/0001
      InOutpDescrParam
    )
    :
    I@065/0001
    FldOutpDescrParam
  }
}
{
  !Du I@006/0002
  FldOutpDescrParam
}
{
  !In I@007/0003
  InOutpDescrParam
  {
    !IF I@026/0003
    param
    :
    I@033/0003
    Number
  }
  |
  I@042/0003
  String
}
{
  !Ou I@008/0001
  OutpFieldEnum
  {
    !OF I@024/0001
    field
    =
    I@032/0001
    EnumOutpFieldEnum
    .outpFieldEnum
  }
}
{
  !En I@006/0002
  EnumOutpFieldEnum
  !EL I@026/0002
  outpFieldEnum
}
{
  !Ou I@008/0001
  OutpFieldEnumPrnt
  {
    !OF I@028/0001
    field
    =
    I@036/0001
    EnumOutpFieldEnumPrnt
    .prnt_outpFieldEnumPrnt
  }
}
{
  !En I@006/0002
  EnumOutpFieldEnumPrnt
  :( !Tr I@031/0002 PrntOutpFieldEnumPrnt )
  !EL I@053/0002
  outpFieldEnumPrnt
}
{
  !En I@006/0003
  PrntOutpFieldEnumPrnt
  !EL I@030/0003
  prnt_outpFieldEnumPrnt
}
{
  !Ou I@008/0001
  OutpFieldValue
  {
    !OF I@025/0001
    field
    =
    I@033/0001
    EnumOutpFieldValue
    .outpFieldValue
  }
}
{
  !En I@006/0002
  EnumOutpFieldValue
  !EL I@027/0002
  outpFieldValue
}
{
  !Ou I@008/0001
  OutpFieldValueDescr
  {
    !OF I@030/0001
    field
    =
    'Test Descr'
    I@053/0001
    EnumOutpFieldValueDescr
    .outpFieldValueDescr
  }
}
{
  !En I@006/0002
  EnumOutpFieldValueDescr
  !EL I@032/0002
  outpFieldValueDescr
}
{
  !Ou I@008/0001
  OutpGnrcEnum
  |
  I@025/0001
  RefOutpGnrcEnum
  <
    I@041/0001
    EnumOutpGnrcEnum.outpGnrcEnum
  >
}
{
  !Ou I@008/0002
  RefOutpGnrcEnum
  <
    I@025/0002
    $type
    :_Enum
  >
  {
    !OF I@039/0002
    field
    :
    I@047/0002
    $type
  }
}
{
  !En I@006/0003
  EnumOutpGnrcEnum
  !EL I@025/0003
  outpGnrcEnum
}
{
  !Ou I@008/0001
  OutpGnrcValue
  |
  I@026/0001
  RefOutpGnrcValue
  <
    I@043/0001
    outpGnrcValue
  >
}
{
  !Ou I@008/0002
  RefOutpGnrcValue
  <
    I@026/0002
    $type
    :_Enum
  >
  {
    !OF I@040/0002
    field
    :
    I@048/0002
    $type
  }
}
{
  !En I@006/0003
  EnumOutpGnrcValue
  !EL I@026/0003
  outpGnrcValue
}
{
  !Ou I@008/0001
  OutpParam
  {
    !OF I@020/0001
    field
    (
      !Pa
      I@026/0001
      InOutpParam
    )
    :
    I@040/0001
    FldOutpParam
  }
}
{
  !Du I@006/0002
  FldOutpParam
}
{
  !In I@007/0003
  InOutpParam
  {
    !IF I@021/0003
    param
    :
    I@028/0003
    Number
  }
  |
  I@037/0003
  String
}
{
  !Ou I@008/0001
  OutpParamDescr
  {
    !OF I@025/0001
    field
    (
      !Pa
      'Test Descr'
      I@046/0001
      InOutpParamDescr
    )
    :
    I@065/0001
    FldOutpParamDescr
  }
}
{
  !Du I@006/0002
  FldOutpParamDescr
}
{
  !In I@007/0003
  InOutpParamDescr
  {
    !IF I@026/0003
    param
    :
    I@033/0003
    Number
  }
  |
  I@042/0003
  String
}
{
  !Ou I@008/0001
  OutpParamModDmn
  {
    !OF I@026/0001
    field
    (
      !Pa
      I@032/0001
      InOutpParamModDmn
      [DomOutpParamModDmn]
    )
    :
    I@072/0001
    DomOutpParamModDmn
  }
}
{
  !In I@007/0002
  InOutpParamModDmn
  {
    !IF I@027/0002
    param
    :
    I@034/0002
    Number
  }
  |
  I@043/0002
  String
}
{
  !Do I@008/0003
  DomOutpParamModDmn
  Number
  !DN N@036/0003
  1
  ~
  10
}
{
  !Ou I@008/0001
  OutpParamModParam
  <
    I@027/0001
    $mod
    :String
  >
  {
    !OF I@041/0001
    field
    (
      !Pa
      I@047/0001
      InOutpParamModParam
      [$mod]
    )
    :
    I@075/0001
    DomOutpParamModParam
  }
}
{
  !In I@007/0002
  InOutpParamModParam
  {
    !IF I@029/0002
    param
    :
    I@036/0002
    Number
  }
  |
  I@045/0002
  String
}
{
  !Do I@008/0003
  DomOutpParamModParam
  Number
  !DN N@038/0003
  1
  ~
  10
}
{
  !Ou I@008/0001
  OutpParamTypeDescr
  {
    !OF I@029/0001
    field
    (
      !Pa
      I@035/0001
      InOutpParamTypeDescr
    )
    :
    'Test Descr'
    I@073/0001
    FldOutpParamTypeDescr
  }
}
{
  !Du I@006/0002
  FldOutpParamTypeDescr
}
{
  !In I@007/0003
  InOutpParamTypeDescr
  {
    !IF I@030/0003
    param
    :
    I@037/0003
    Number
  }
  |
  I@046/0003
  String
}
{
  !Ou I@008/0001
  OutpPrntGnrc
  |
  I@025/0001
  RefOutpPrntGnrc
  <
    I@041/0001
    EnumOutpPrntGnrc.prnt_outpPrntGnrc
  >
}
{
  !Ou I@008/0002
  RefOutpPrntGnrc
  <
    I@025/0002
    $type
    :PrntOutpPrntGnrc
  >
  {
    !OF I@050/0002
    field
    :
    I@058/0002
    $type
  }
}
{
  !En I@006/0003
  EnumOutpPrntGnrc
  :( !Tr I@026/0003 PrntOutpPrntGnrc )
  !EL I@043/0003
  outpPrntGnrc
}
{
  !En I@006/0004
  PrntOutpPrntGnrc
  !EL I@025/0004
  prnt_outpPrntGnrc
}
{
  !Ou I@008/0001
  OutpPrntParam
  :
  I@025/0001
  PrntOutpPrntParam
  {
    !OF I@043/0001
    field
    (
      !Pa
      I@049/0001
      InOutpPrntParam
    )
    :
    I@067/0001
    FldOutpPrntParam
  }
}
{
  !Ou I@008/0002
  PrntOutpPrntParam
  {
    !OF I@028/0002
    field
    (
      !Pa
      I@034/0002
      PrntOutpPrntParamIn
    )
    :
    I@056/0002
    FldOutpPrntParam
  }
}
{
  !Du I@006/0003
  FldOutpPrntParam
}
{
  !In I@007/0004
  InOutpPrntParam
  {
    !IF I@025/0004
    param
    :
    I@032/0004
    Number
  }
  |
  I@041/0004
  String
}
{
  !In I@007/0005
  PrntOutpPrntParamIn
  {
    !IF I@029/0005
    parent
    :
    I@037/0005
    Number
  }
  |
  I@046/0005
  String
}
{
  !Du I@006/0001
  PrntDual
  :
  I@018/0001
  RefPrntDual
}
{
  !Du I@006/0002
  RefPrntDual
  {
    !DF I@020/0002
    parent
    :
    I@028/0002
    Number
  }
  |
  I@037/0002
  String
}
{
  !In I@007/0001
  PrntInp
  :
  I@018/0001
  RefPrntInp
}
{
  !In I@007/0002
  RefPrntInp
  {
    !IF I@020/0002
    parent
    :
    I@028/0002
    Number
  }
  |
  I@037/0002
  String
}
{
  !Ou I@008/0001
  PrntOutp
  :
  I@020/0001
  RefPrntOutp
}
{
  !Ou I@008/0002
  RefPrntOutp
  {
    !OF I@022/0002
    parent
    :
    I@030/0002
    Number
  }
  |
  I@039/0002
  String
}
{
  !Du I@006/0001
  PrntAltDual
  :
  I@021/0001
  RefPrntAltDual
  |
  I@038/0001
  Number
}
{
  !Du I@006/0002
  RefPrntAltDual
  {
    !DF I@024/0002
    parent
    :
    I@032/0002
    Number
  }
  |
  I@041/0002
  String
}
{
  !In I@007/0001
  PrntAltInp
  :
  I@021/0001
  RefPrntAltInp
  |
  I@037/0001
  Number
}
{
  !In I@007/0002
  RefPrntAltInp
  {
    !IF I@024/0002
    parent
    :
    I@032/0002
    Number
  }
  |
  I@041/0002
  String
}
{
  !Ou I@008/0001
  PrntAltOutp
  :
  I@023/0001
  RefPrntAltOutp
  |
  I@040/0001
  Number
}
{
  !Ou I@008/0002
  RefPrntAltOutp
  {
    !OF I@026/0002
    parent
    :
    I@034/0002
    Number
  }
  |
  I@043/0002
  String
}
{
  !Du I@006/0001
  PrntDescrDual
  :
  'Test Descr'
  I@039/0001
  RefPrntDescrDual
}
{
  !Du I@006/0002
  RefPrntDescrDual
  {
    !DF I@025/0002
    parent
    :
    I@033/0002
    Number
  }
  |
  I@042/0002
  String
}
{
  !In I@007/0001
  PrntDescrInp
  :
  'Test Descr'
  I@039/0001
  RefPrntDescrInp
}
{
  !In I@007/0002
  RefPrntDescrInp
  {
    !IF I@025/0002
    parent
    :
    I@033/0002
    Number
  }
  |
  I@042/0002
  String
}
{
  !Ou I@008/0001
  PrntDescrOutp
  :
  'Test Descr'
  I@041/0001
  RefPrntDescrOutp
}
{
  !Ou I@008/0002
  RefPrntDescrOutp
  {
    !OF I@027/0002
    parent
    :
    I@035/0002
    Number
  }
  |
  I@044/0002
  String
}
{
  !Du I@006/0001
  PrntDualDual
  :
  I@022/0001
  RefPrntDualDual
}
{
  !Du I@006/0002
  RefPrntDualDual
  {
    !DF I@024/0002
    parent
    :
    I@032/0002
    Number
  }
  |
  I@041/0002
  String
}
{
  !In I@007/0001
  PrntDualInp
  :
  I@022/0001
  RefPrntDualInp
}
{
  !Du I@006/0002
  RefPrntDualInp
  {
    !DF I@023/0002
    parent
    :
    I@031/0002
    Number
  }
  |
  I@040/0002
  String
}
{
  !Ou I@008/0001
  PrntDualOutp
  :
  I@024/0001
  RefPrntDualOutp
}
{
  !Du I@006/0002
  RefPrntDualOutp
  {
    !DF I@024/0002
    parent
    :
    I@032/0002
    Number
  }
  |
  I@041/0002
  String
}
{
  !Du I@006/0001
  PrntFieldDual
  :
  I@023/0001
  RefPrntFieldDual
  {
    !DF I@040/0001
    field
    :
    I@047/0001
    Number
  }
}
{
  !Du I@006/0002
  RefPrntFieldDual
  {
    !DF I@025/0002
    parent
    :
    I@033/0002
    Number
  }
  |
  I@042/0002
  String
}
{
  !In I@007/0001
  PrntFieldInp
  :
  I@023/0001
  RefPrntFieldInp
  {
    !IF I@039/0001
    field
    :
    I@046/0001
    Number
  }
}
{
  !In I@007/0002
  RefPrntFieldInp
  {
    !IF I@025/0002
    parent
    :
    I@033/0002
    Number
  }
  |
  I@042/0002
  String
}
{
  !Ou I@008/0001
  PrntFieldOutp
  :
  I@025/0001
  RefPrntFieldOutp
  {
    !OF I@042/0001
    field
    :
    I@049/0001
    Number
  }
}
{
  !Ou I@008/0002
  RefPrntFieldOutp
  {
    !OF I@027/0002
    parent
    :
    I@035/0002
    Number
  }
  |
  I@044/0002
  String
}
{
  !Du I@006/0001
  PrntParamDiffDual
  <
    I@025/0001
    $a
    :String
  >
  :
  I@038/0001
  RefPrntParamDiffDual
  <
    I@060/0001
    $a
  >
  {
    !DF I@063/0001
    field
    :
    I@071/0001
    $a
  }
}
{
  !Du I@006/0002
  RefPrntParamDiffDual
  <
    I@028/0002
    $b
    :String
  >
  |
  P@042/0002
  $b
}
{
  !In I@007/0001
  PrntParamDiffInp
  <
    I@025/0001
    $a
    :String
  >
  :
  I@038/0001
  RefPrntParamDiffInp
  <
    I@059/0001
    $a
  >
  {
    !IF I@062/0001
    field
    :
    I@070/0001
    $a
  }
}
{
  !In I@007/0002
  RefPrntParamDiffInp
  <
    I@028/0002
    $b
    :String
  >
  |
  P@042/0002
  $b
}
{
  !Ou I@008/0001
  PrntParamDiffOutp
  <
    I@027/0001
    $a
    :String
  >
  :
  I@040/0001
  RefPrntParamDiffOutp
  <
    I@062/0001
    $a
  >
  {
    !OF I@065/0001
    field
    :
    I@073/0001
    $a
  }
}
{
  !Ou I@008/0002
  RefPrntParamDiffOutp
  <
    I@030/0002
    $b
    :String
  >
  |
  P@044/0002
  $b
}
{
  !Du I@006/0001
  PrntParamSameDual
  <
    I@025/0001
    $a
    :String
  >
  :
  I@038/0001
  RefPrntParamSameDual
  <
    I@060/0001
    $a
  >
  {
    !DF I@063/0001
    field
    :
    I@071/0001
    $a
  }
}
{
  !Du I@006/0002
  RefPrntParamSameDual
  <
    I@028/0002
    $a
    :String
  >
  |
  P@042/0002
  $a
}
{
  !In I@007/0001
  PrntParamSameInp
  <
    I@025/0001
    $a
    :String
  >
  :
  I@038/0001
  RefPrntParamSameInp
  <
    I@059/0001
    $a
  >
  {
    !IF I@062/0001
    field
    :
    I@070/0001
    $a
  }
}
{
  !In I@007/0002
  RefPrntParamSameInp
  <
    I@028/0002
    $a
    :String
  >
  |
  P@042/0002
  $a
}
{
  !Ou I@008/0001
  PrntParamSameOutp
  <
    I@027/0001
    $a
    :String
  >
  :
  I@040/0001
  RefPrntParamSameOutp
  <
    I@062/0001
    $a
  >
  {
    !OF I@065/0001
    field
    :
    I@073/0001
    $a
  }
}
{
  !Ou I@008/0002
  RefPrntParamSameOutp
  <
    I@030/0002
    $a
    :String
  >
  |
  P@044/0002
  $a
}
{
  !Ou I@008/0003
  Ctgr
}
{
  !Ou I@008/0003
  CtgrAlias
}
{
  !Ou I@008/0005
  CtgrDescr
}
{
  !Ou I@008/0003
  CtgrMod
}
{
  !In I@007/0003
  InDrctParam
}
{
  !Do I@008/0001
  DmnAlias
  [
    Num1
    Num2
  ]
  Number
}
{
  !Do I@008/0001
  DmnBool
  Boolean
  !DT P@026/0001
  False
  !DT P@026/0001
  True
}
{
  !Do I@008/0001
  DmnBoolDiff
  Boolean
  !DT I@030/0001
  True
  !DT I@030/0002
  False
}
{
  !Do I@008/0001
  DmnBoolSame
  Boolean
  !DT I@030/0001
  True
}
{
  !Do I@008/0001
  DmnEnumDiff
  Enum
  !DE I@027/0001
  Boolean
  true
  !DE I@027/0002
  Boolean
  false
}
{
  !Do I@008/0001
  DmnEnumSame
  Enum
  !DE I@027/0001
  Boolean
  true
}
{
  !Do I@008/0001
  DmnNmbr
  Number
}
{
  !Do I@008/0001
  DmnNmbrDiff
  Number
  !DN N@029/0001
  1
  ~
  9
}
{
  !Do I@008/0001
  DmnNmbrSame
  Number
  !DN N@029/0001
  1
  ~
  9
}
{
  !Do I@008/0001
  DmnStr
  String
}
{
  !Do I@008/0001
  DmnStrDiff
  String
  !DX R@028/0001
  /a+/
}
{
  !Do I@008/0001
  DmnStrSame
  String
  !DX R@028/0001
  /a+/
}
{
  !En I@006/0001
  EnumAlias
  [
    En1
    En2
  ]
  !EL I@024/0001
  enumAlias
}
{
  !En I@006/0001
  EnumDiff
  !EL I@017/0001
  one
  !EL I@017/0002
  two
}
{
  !En I@006/0001
  EnumSame
  !EL I@017/0001
  enumSame
}
{
  !En I@006/0001
  EnumSamePrnt
  :( !Tr I@022/0001 PrntEnumSamePrnt )
  !EL I@039/0001
  enumSamePrnt
}
{
  !En I@006/0003
  PrntEnumSamePrnt
  !EL I@025/0003
  prnt_enumSamePrnt
}
{
  !En I@006/0001
  EnumValueAlias
  !EL I@023/0001
  enumValueAlias
  [
    val1
    val2
  ]
}
{
  !Du I@006/0001
  ObjDual
}
{
  !In I@007/0001
  ObjInp
}
{
  !Ou I@008/0001
  ObjOutp
}
{
  !Du I@006/0001
  ObjAliasDual
  [
    Dual1
    Dual2
  ]
}
{
  !In I@007/0001
  ObjAliasInp
  [
    Input1
    Input2
  ]
}
{
  !Ou I@008/0001
  ObjAliasOutp
  [
    Output1
    Output2
  ]
}
{
  !Du I@006/0001
  ObjAltDual
  |
  I@021/0001
  ObjAltDualType
}
{
  !Du I@006/0003
  ObjAltDualType
}
{
  !In I@007/0001
  ObjAltInp
  |
  I@021/0001
  ObjAltInpType
}
{
  !In I@007/0003
  ObjAltInpType
}
{
  !Ou I@008/0001
  ObjAltOutp
  |
  I@023/0001
  ObjAltOutpType
}
{
  !Ou I@008/0003
  ObjAltOutpType
}
{
  !Du I@006/0001
  ObjCnstDual
  <
    I@019/0001
    $type
    :String
  >
  {
    !DF I@034/0001
    field
    :
    I@042/0001
    $type
    !DF I@034/0002
    str
    :
    I@040/0002
    $type
  }
}
{
  !In I@007/0001
  ObjCnstInp
  <
    I@019/0001
    $type
    :String
  >
  {
    !IF I@034/0001
    field
    :
    I@042/0001
    $type
    !IF I@034/0002
    str
    :
    I@040/0002
    $type
  }
}
{
  !Ou I@008/0001
  ObjCnstOutp
  <
    I@021/0001
    $type
    :String
  >
  {
    !OF I@036/0001
    field
    :
    I@044/0001
    $type
    !OF I@036/0002
    str
    :
    I@042/0002
    $type
  }
}
{
  !Du I@006/0001
  ObjFieldDual
  {
    !DF I@021/0001
    field
    :
    I@028/0001
    FldObjFieldDual
  }
}
{
  !Du I@006/0003
  FldObjFieldDual
}
{
  !In I@007/0001
  ObjFieldInp
  {
    !IF I@021/0001
    field
    :
    I@028/0001
    FldObjFieldInp
  }
}
{
  !In I@007/0003
  FldObjFieldInp
}
{
  !Ou I@008/0001
  ObjFieldOutp
  {
    !OF I@023/0001
    field
    :
    I@030/0001
    FldObjFieldOutp
  }
}
{
  !Ou I@008/0003
  FldObjFieldOutp
}
{
  !Du I@006/0001
  ObjFieldAliasDual
  {
    !DF I@026/0001
    field
    [
      field1
      field2
    ]
    :
    I@042/0001
    FldObjFieldAliasDual
  }
}
{
  !Du I@006/0003
  FldObjFieldAliasDual
}
{
  !In I@007/0001
  ObjFieldAliasInp
  {
    !IF I@026/0001
    field
    [
      field1
      field2
    ]
    :
    I@042/0001
    FldObjFieldAliasInp
  }
}
{
  !In I@007/0003
  FldObjFieldAliasInp
}
{
  !Ou I@008/0001
  ObjFieldAliasOutp
  {
    !OF I@028/0001
    field
    [
      field1
      field2
    ]
    :
    I@044/0001
    FldObjFieldAliasOutp
  }
}
{
  !Ou I@008/0003
  FldObjFieldAliasOutp
}
{
  !Du I@006/0001
  ObjFieldTypeAliasDual
  {
    !DF I@030/0001
    field
    :
    I@037/0001
    String
  }
}
{
  !In I@007/0001
  ObjFieldTypeAliasInp
  {
    !IF I@030/0001
    field
    :
    I@037/0001
    String
  }
}
{
  !Ou I@008/0001
  ObjFieldTypeAliasOutp
  {
    !OF I@032/0001
    field
    :
    I@039/0001
    String
  }
}
{
  !Du I@006/0001
  ObjParamDual
  <
    I@020/0001
    $test
    :String
    I@020/0002
    $type
    :String
  >
  {
    !DF I@035/0001
    test
    :
    I@042/0001
    $test
    !DF I@035/0002
    type
    :
    I@042/0002
    $type
  }
}
{
  !In I@007/0001
  ObjParamInp
  <
    I@020/0001
    $test
    :String
    I@020/0002
    $type
    :String
  >
  {
    !IF I@035/0001
    test
    :
    I@042/0001
    $test
    !IF I@035/0002
    type
    :
    I@042/0002
    $type
  }
}
{
  !Ou I@008/0001
  ObjParamOutp
  <
    I@022/0001
    $test
    :String
    I@022/0002
    $type
    :String
  >
  {
    !OF I@037/0001
    test
    :
    I@044/0001
    $test
    !OF I@037/0002
    type
    :
    I@044/0002
    $type
  }
}
{
  !Du I@006/0001
  ObjParamCnstDual
  <
    I@024/0001
    $test
    :String
  >
  {
    !DF I@039/0001
    test
    :
    I@046/0001
    $test
    !DF I@039/0002
    type
    :
    I@046/0002
    $test
  }
}
{
  !In I@007/0001
  ObjParamCnstInp
  <
    I@024/0001
    $test
    :String
  >
  {
    !IF I@039/0001
    test
    :
    I@046/0001
    $test
    !IF I@039/0002
    type
    :
    I@046/0002
    $test
  }
}
{
  !Ou I@008/0001
  ObjParamCnstOutp
  <
    I@026/0001
    $test
    :String
  >
  {
    !OF I@041/0001
    test
    :
    I@048/0001
    $test
    !OF I@041/0002
    type
    :
    I@048/0002
    $test
  }
}
{
  !Du I@006/0001
  ObjParamDupDual
  <
    I@023/0001
    $test
    :String
  >
  {
    !DF I@038/0001
    test
    :
    I@045/0001
    $test
    !DF I@038/0002
    type
    :
    I@045/0002
    $test
  }
}
{
  !In I@007/0001
  ObjParamDupInp
  <
    I@023/0001
    $test
    :String
  >
  {
    !IF I@038/0001
    test
    :
    I@045/0001
    $test
    !IF I@038/0002
    type
    :
    I@045/0002
    $test
  }
}
{
  !Ou I@008/0001
  ObjParamDupOutp
  <
    I@025/0001
    $test
    :String
  >
  {
    !OF I@040/0001
    test
    :
    I@047/0001
    $test
    !OF I@040/0002
    type
    :
    I@047/0002
    $test
  }
}
{
  !Du I@006/0001
  ObjPrntDual
  :
  I@021/0001
  RefObjPrntDual
}
{
  !Du I@006/0003
  RefObjPrntDual
}
{
  !In I@007/0001
  ObjPrntInp
  :
  I@021/0001
  RefObjPrntInp
}
{
  !In I@007/0003
  RefObjPrntInp
}
{
  !Ou I@008/0001
  ObjPrntOutp
  :
  I@023/0001
  RefObjPrntOutp
}
{
  !Ou I@008/0003
  RefObjPrntOutp
}
{
  !Ou I@008/0001
  OutpFieldEnumAlias
  {
    !OF I@029/0001
    field
    [
      field1
      field2
    ]
    =
    I@046/0001
    Boolean
    .true
  }
}
{
  !Ou I@008/0001
  OutpFieldEnumValue
  {
    !OF I@029/0001
    field
    =
    I@037/0001
    Boolean
    .true
  }
}
{
  !Ou I@008/0001
  OutpFieldParam
  {
    !OF I@025/0001
    field
    (
      !Pa
      I@031/0001
      OutpFieldParam1
      !Pa
      I@031/0002
      OutpFieldParam2
    )
    :
    I@049/0001
    FldOutpFieldParam
  }
}
{
  !In I@007/0003
  OutpFieldParam1
}
{
  !In I@007/0004
  OutpFieldParam2
}
{
  !Du I@006/0005
  FldOutpFieldParam
}
{
  !Un I@007/0001
  UnionAlias
  [
    UnA1
    UnA2
  ]
  !UM I@027/0001
  Boolean
  !UM I@027/0002
  Number
}
{
  !Un I@007/0001
  UnionDiff
  !UM I@019/0001
  Boolean
  !UM I@019/0002
  Number
}
{
  !Un I@007/0001
  UnionSame
  !UM I@019/0001
  Boolean
}
{
  !Un I@007/0001
  UnionSamePrnt
  :( !Tr I@024/0001 PrntUnionSamePrnt )
  !UM I@042/0001
  Boolean
}
{
  !Un I@007/0003
  PrntUnionSamePrnt
  !UM I@027/0003
  String
}
{
  !Do I@008/0001
  DmnBoolDescr
  Boolean
  !DT I@049/0001
  True
}
{
  !Do I@008/0001
  DmnBoolPrnt
  Boolean
  :( !Tr I@023/0001 PrntDmnBoolPrnt )
  !DT I@047/0001
  False
}
{
  !Do I@008/0002
  PrntDmnBoolPrnt
  Boolean
  !DT I@034/0002
  True
}
{
  !Do I@008/0001
  DmnBoolPrntDescr
  Boolean
  :( 'Parent comment' !Tr I@044/0001 PrntDmnBoolPrntDescr )
  !DT I@073/0001
  False
}
{
  !Do I@008/0002
  PrntDmnBoolPrntDescr
  Boolean
  !DT I@039/0002
  True
}
{
  !Do I@008/0001
  DmnEnumAll
  Enum
  !DE I@026/0001
  EnumDmnEnumAll
  *
}
{
  !En I@006/0002
  EnumDmnEnumAll
  !EL I@023/0002
  dmnEnumAll
  !EL I@034/0002
  enum_dmnEnumAll
}
{
  !Do I@008/0001
  DmnEnumAllDescr
  Enum
  !DE I@048/0001
  EnumDmnEnumAllDescr
  *
}
{
  !En I@006/0002
  EnumDmnEnumAllDescr
  !EL I@028/0002
  dmnEnumAllDescr
  !EL I@044/0002
  enum_dmnEnumAllDescr
}
{
  !Do I@008/0001
  DmnEnumAllPrnt
  Enum
  !DE I@030/0001
  EnumDmnEnumAllPrnt
  *
}
{
  !En I@006/0002
  EnumDmnEnumAllPrnt
  :( !Tr I@028/0002 PrntDmnEnumAllPrnt )
  !EL I@047/0002
  dmnEnumAllPrnt
}
{
  !En I@006/0003
  PrntDmnEnumAllPrnt
  !EL I@027/0003
  prnt_dmnEnumAllPrnt
}
{
  !Do I@008/0001
  DmnEnumDescr
  Enum
  !DE I@046/0001
  EnumDmnEnumDescr
  dmnEnumDescr
}
{
  !En I@006/0002
  EnumDmnEnumDescr
  !EL I@025/0002
  dmnEnumDescr
}
{
  !Do I@008/0001
  DmnEnumLabel
  Enum
  !DE I@028/0001
  EnumDmnEnumLabel
  dmnEnumLabel
}
{
  !En I@006/0002
  EnumDmnEnumLabel
  !EL I@025/0002
  dmnEnumLabel
}
{
  !Do I@008/0001
  DmnEnumPrnt
  Enum
  :( !Tr I@023/0001 PrntDmnEnumPrnt )
  !DE I@044/0001
  EnumDmnEnumPrnt
  enum_dmnEnumPrnt
}
{
  !Do I@008/0002
  PrntDmnEnumPrnt
  Enum
  !DE I@031/0002
  EnumDmnEnumPrnt
  prnt_dmnEnumPrnt
}
{
  !En I@006/0003
  EnumDmnEnumPrnt
  !EL I@024/0003
  enum_dmnEnumPrnt
  !EL I@041/0003
  prnt_dmnEnumPrnt
}
{
  !Do I@008/0001
  DmnEnumPrntDescr
  Enum
  :( 'Parent comment' !Tr I@044/0001 PrntDmnEnumPrntDescr )
  !DE I@070/0001
  EnumDmnEnumPrntDescr
  enum_dmnEnumPrntDescr
}
{
  !Do I@008/0002
  PrntDmnEnumPrntDescr
  Enum
  !DE I@036/0002
  EnumDmnEnumPrntDescr
  prnt_dmnEnumPrntDescr
}
{
  !En I@006/0003
  EnumDmnEnumPrntDescr
  !EL I@029/0003
  enum_dmnEnumPrntDescr
  !EL I@051/0003
  prnt_dmnEnumPrntDescr
}
{
  !En I@006/0002
  EnumDmnEnumUnq
  !EL I@023/0002
  enum_dmnEnumUnq
  !EL I@039/0002
  dmnEnumUnq
}
{
  !En I@006/0003
  EnumDomDup
  !EL I@019/0003
  dmnEnumUnq
  !EL I@030/0003
  dup_dmnEnumUnq
}
{
  !En I@006/0002
  EnumDmnEnumUnqPrnt
  :( !Tr I@028/0002 PrntDmnEnumUnqPrnt )
  !EL I@047/0002
  enum_dmnEnumUnqPrnt
}
{
  !En I@006/0003
  PrntDmnEnumUnqPrnt
  !EL I@027/0003
  dmnEnumUnqPrnt
  !EL I@042/0003
  prnt_dmnEnumUnqPrnt
}
{
  !En I@006/0004
  DupDmnEnumUnqPrnt
  !EL I@026/0004
  dmnEnumUnqPrnt
  !EL I@041/0004
  dup_dmnEnumUnqPrnt
}
{
  !Do I@008/0001
  DmnEnumValue
  Enum
  !DE I@028/0001
  EnumDmnEnumValue
  dmnEnumValue
}
{
  !En I@006/0002
  EnumDmnEnumValue
  !EL I@025/0002
  dmnEnumValue
}
{
  !Do I@008/0001
  DmnEnumValuePrnt
  Enum
  !DE I@032/0001
  EnumDmnEnumValuePrnt
  prnt_dmnEnumValuePrnt
}
{
  !En I@006/0002
  EnumDmnEnumValuePrnt
  :( !Tr I@030/0002 PrntDmnEnumValuePrnt )
  !EL I@051/0002
  dmnEnumValuePrnt
}
{
  !En I@006/0003
  PrntDmnEnumValuePrnt
  !EL I@029/0003
  prnt_dmnEnumValuePrnt
}
{
  !Do I@008/0001
  DmnNmbrDescr
  Number
  !DN P@050/0001
  <
    2
  }
  {
    !Do I@008/0001
    DmnNmbrPrnt
    Number
    :( !Tr I@023/0001 PrntDmnNmbrPrnt )
    !DN N@046/0001
    2
  >
}
{
  !Do I@008/0002
  PrntDmnNmbrPrnt
  Number
  !DN P@033/0002
  <
    2
  }
  {
    !Do I@008/0001
    DmnNmbrPrntDescr
    Number
    :( 'Parent comment' !Tr I@044/0001 PrntDmnNmbrPrntDescr )
    !DN N@072/0001
    2
  >
}
{
  !Do I@008/0002
  PrntDmnNmbrPrntDescr
  Number
  !DN P@038/0002
  <
    2
  }
  {
    !Do I@008/0001
    DmnStrDescr
    String
    !DX R@049/0001
    /a+/
  }
  {
    !Do I@008/0001
    DmnStrPrnt
    String
    :( !Tr I@022/0001 PrntDmnStrPrnt )
    !DX R@044/0001
    /a+/
  }
  {
    !Do I@008/0002
    PrntDmnStrPrnt
    String
    !DX R@032/0002
    /b+/
  }
  {
    !Do I@008/0001
    DmnStrPrntDescr
    String
    :( 'Parent comment' !Tr I@043/0001 PrntDmnStrPrntDescr )
    !DX R@070/0001
    /a+/
  }
  {
    !Do I@008/0002
    PrntDmnStrPrntDescr
    String
    !DX R@037/0002
    /b+/
  }
  {
    !En I@006/0001
    EnumDescr
    'Enum Descr'
    !EL I@033/0001
    enumDescr
  }
  {
    !En I@006/0001
    EnumPrnt
    :( !Tr I@018/0001 PrntEnumPrnt )
    !EL I@031/0001
    enumPrnt
  }
  {
    !En I@006/0002
    PrntEnumPrnt
    !EL I@021/0002
    prnt_enumPrnt
  }
  {
    !En I@006/0001
    EnumPrntAlias
    :( !Tr I@023/0001 PrntEnumPrntAlias )
    !EL I@041/0001
    val_enumPrntAlias
    !EL I@059/0001
    prnt_enumPrntAlias
    [
      enumPrntAlias
    ]
  }
  {
    !En I@006/0002
    PrntEnumPrntAlias
    !EL I@026/0002
    prnt_enumPrntAlias
  }
  {
    !En I@006/0001
    EnumPrntDescr
    :( 'Parent comment' !Tr I@041/0001 PrntEnumPrntDescr )
    !EL I@059/0001
    enumPrntDescr
  }
  {
    !En I@006/0002
    PrntEnumPrntDescr
    !EL I@026/0002
    prnt_enumPrntDescr
  }
  {
    !En I@006/0001
    EnumPrntDup
    :( !Tr I@021/0001 PrntEnumPrntDup )
    !EL I@037/0001
    enumPrntDup
  }
  {
    !En I@006/0002
    PrntEnumPrntDup
    !EL I@024/0002
    prnt_enumPrntDup
    [
      enumPrntDup
    ]
  }
  {
    !Un I@007/0001
    UnionDescr
    'Union Descr'
    !UM I@036/0001
    Number
  }
  {
    !Un I@007/0001
    UnionPrnt
    :( !Tr I@020/0001 PrntUnionPrnt )
    !UM I@034/0001
    String
  }
  {
    !Un I@007/0002
    PrntUnionPrnt
    !UM I@023/0002
    Number
  }
  {
    !Un I@007/0001
    UnionPrntDescr
    :( 'Parent comment' !Tr I@041/0001 PrntUnionPrntDescr )
    !UM I@060/0001
    Number
  }
  {
    !Un I@007/0002
    PrntUnionPrntDescr
    !UM I@028/0002
    Number
  }
  {
    !Un I@007/0001
    UnionPrntDup
    :( !Tr I@023/0001 PrntUnionPrntDup )
    !UM I@040/0001
    Number
  }
  {
    !Un I@007/0002
    PrntUnionPrntDup
    !UM I@026/0002
    Number
  }
]