﻿!_Schema
types: !_Map_Type
  !_Identifier RefGnrcAltModParamOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        object: RefGnrcAltModParamOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcAltModParamOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: mod