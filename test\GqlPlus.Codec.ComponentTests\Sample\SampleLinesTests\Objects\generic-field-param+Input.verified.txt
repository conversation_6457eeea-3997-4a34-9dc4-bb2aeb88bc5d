﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcFieldParamInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: AltGnrcFieldParamInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltGnrcFieldParamInp
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: alt
        type: !_InputBase
          input: Number
    name: AltGnrcFieldParamInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcFieldParamInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: GnrcFieldParamInp
        type: !_InputBase
          input: RefGnrcFieldParamInp
          typeArgs:
            - !_InputArg
              input: AltGnrcFieldParamInp
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          input: RefGnrcFieldParamInp
          typeArgs:
            - !_InputArg
              input: AltGnrcFieldParamInp
    name: GnrcFieldParamInp
    typeKind: !_TypeKind Input
  !_Identifier RefGnrcFieldParamInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefGnrcFieldParamInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefGnrcFieldParamInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Input
        name: ref