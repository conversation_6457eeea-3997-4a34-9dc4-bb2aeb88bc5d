﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcAltSmplDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: GnrcAltSmplDual
        type: !_DualBase
          dual: RefGnrcAltSmplDual
          typeArgs:
            - !_DualArg
              dual: String
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: RefGnrcAltSmplDual
          typeArgs:
            - !_DualArg
              dual: String
    name: GnrcAltSmplDual
    typeKind: !_TypeKind Dual
  !_Identifier RefGnrcAltSmplDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefGnrcAltSmplDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefGnrcAltSmplDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref