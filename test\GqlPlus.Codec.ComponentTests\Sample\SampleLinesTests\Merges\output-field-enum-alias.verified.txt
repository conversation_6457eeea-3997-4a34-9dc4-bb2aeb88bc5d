﻿!_Schema
types: !_Map_Type
  !_Identifier OutpFieldEnumAlias: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        field: field
        label: true
        object: OutpFieldEnumAlias
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    fields:
      - !_OutputEnum
        field: field
        label: true
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    name: OutpFieldEnumAlias
    typeKind: !_TypeKind Output