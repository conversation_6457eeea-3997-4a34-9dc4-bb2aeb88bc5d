﻿!_Schema
types: !_Map_Type
  !_Identifier FieldModParamOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        modifiers: [!_ModifierTypeParam{by:mod,modifierKind:!_ModifierKind Param}]
        name: field
        object: FieldModParamOutp
        type: !_OutputBase
          output: FldFieldModParamOutp
    fields:
      - !_OutputField
        modifiers: [!_ModifierTypeParam{by:mod,modifierKind:!_ModifierKind Param}]
        name: field
        type: !_OutputBase
          output: FldFieldModParamOutp
    name: FieldModParamOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: mod
  !_Identifier FldFieldModParamOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: FldFieldModParamOutp
        type: !_OutputBase
          output: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: FldFieldModParamOutp
        type: !_OutputBase
          output: Number
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: String
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          output: Number
    name: FldFieldModParamOutp
    typeKind: !_TypeKind Output