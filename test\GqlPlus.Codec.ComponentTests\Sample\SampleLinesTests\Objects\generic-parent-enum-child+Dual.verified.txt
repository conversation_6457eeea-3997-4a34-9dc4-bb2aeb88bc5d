﻿!_Schema
types: !_Map_Type
  !_Identifier EnumGnrcPrntEnumChildDual: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentGnrcPrntEnumChildDual
        name: gnrcPrntEnumChildDualParent
      - !_EnumLabel
        enum: EnumGnrcPrntEnumChildDual
        name: gnrcPrntEnumChildDualLabel
    items:
      - !_Aliased
        name: gnrcPrntEnumChildDualLabel
    name: EnumGnrcPrntEnumChildDual
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumChildDual
    typeKind: !_TypeKind Enum
  !_Identifier FieldGnrcPrntEnumChildDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FieldGnrcPrntEnumChildDual
        type: !_DualBase
          typeParam: ref
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          typeParam: ref
    name: FieldGnrcPrntEnumChildDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: EnumGnrcPrntEnumChildDual
        name: ref
  !_Identifier GnrcPrntEnumChildDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FieldGnrcPrntEnumChildDual
        type: !_DualBase
          dual: ParentGnrcPrntEnumChildDual
    name: GnrcPrntEnumChildDual
    parent: !_DualBase
      dual: FieldGnrcPrntEnumChildDual
      typeArgs:
        - !_DualArg
          dual: ParentGnrcPrntEnumChildDual
    typeKind: !_TypeKind Dual
  !_Identifier ParentGnrcPrntEnumChildDual: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: ParentGnrcPrntEnumChildDual
        name: gnrcPrntEnumChildDualParent
    items:
      - !_Aliased
        name: gnrcPrntEnumChildDualParent
    name: ParentGnrcPrntEnumChildDual
    typeKind: !_TypeKind Enum