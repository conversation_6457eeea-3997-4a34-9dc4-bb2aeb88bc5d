﻿!_Schema
errors:
  - !_Error
    _kind: !_TokenKind End
    _message: In _TypeInput can't get model for type '_TypeObject'
  - !_Error
    _kind: !_TokenKind End
    _message: In _InputBase can't get model for type '_ObjBase'
  - !_Error
    _kind: !_TokenKind End
    _message: In _InputTypeParam can't get model for type '_ObjTypeParam'
  - !_Error
    _kind: !_TokenKind End
    _message: In _InputField can't get model for type '_Field'
  - !_Error
    _kind: !_TokenKind End
    _message: In _InputAlternate can't get model for type '_Alternate'
  - !_Error
    _kind: !_TokenKind End
    _message: In _InputType<PERSON><PERSON> can't get model for type '_ObjTypeArg'
types: !_Map_Type
  !_Identifier _InputAlternate: !_TypeOutput
    name: _InputAlternate
    parent: !_OutputBase
      output: _Alternate
      typeArgs:
        - !_OutputArg
          output: _InputBase
    typeKind: !_TypeKind Output
  !_Identifier _InputBase: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _InputBase
        type: !_OutputBase
          output: _DualBase
    allFields:
      - !_ObjectFor(_OutputField)
        name: input
        object: _InputBase
        type: !_OutputBase
          output: _Identifier
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _DualBase
    fields:
      - !_OutputField
        name: input
        type: !_OutputBase
          output: _Identifier
    name: _InputBase
    parent: !_OutputBase
      output: _ObjBase
      typeArgs:
        - !_OutputArg
          output: _InputTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _InputField: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: default
        object: _InputField
        type: !_OutputBase
          output: _Constant
    fields:
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: default
        type: !_OutputBase
          output: _Constant
    name: _InputField
    parent: !_OutputBase
      output: _Field
      typeArgs:
        - !_OutputArg
          output: _InputBase
    typeKind: !_TypeKind Output
  !_Identifier _InputParam: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _InputBase
        type: !_OutputBase
          output: _DualBase
    allFields:
      - !_ObjectFor(_OutputField)
        name: input
        object: _InputBase
        type: !_OutputBase
          output: _Identifier
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: modifiers
        object: _InputParam
        type: !_OutputBase
          output: _Modifiers
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: default
        object: _InputParam
        type: !_OutputBase
          output: _Constant
    fields:
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: modifiers
        type: !_OutputBase
          output: _Modifiers
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: default
        type: !_OutputBase
          output: _Constant
    name: _InputParam
    parent: !_OutputBase
      output: _InputBase
    typeKind: !_TypeKind Output
  !_Identifier _InputTypeArg: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: input
        object: _InputTypeArg
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: input
        type: !_OutputBase
          output: _Identifier
    name: _InputTypeArg
    parent: !_OutputBase
      output: _ObjTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _InputTypeParam: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _InputTypeParam
        type: !_OutputBase
          output: _TypeRef
          typeArgs:
            - !_OutputArg
              label: Dual
              typeKind: !_SimpleKind Enum
              typeName: _TypeKind
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _TypeRef
          typeArgs:
            - !_OutputArg
              label: Dual
              typeKind: !_SimpleKind Enum
              typeName: _TypeKind
    name: _InputTypeParam
    parent: !_OutputBase
      output: _ObjTypeParam
      typeArgs:
        - !_OutputArg
          label: Input
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _TypeInput: !_TypeOutput
    name: _TypeInput
    parent: !_OutputBase
      output: _TypeObject
      typeArgs:
        - !_OutputArg
          label: Input
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
        - !_OutputArg
          output: _InputBase
        - !_OutputArg
          output: _InputTypeParam
        - !_OutputArg
          output: _InputField
        - !_OutputArg
          output: _InputAlternate
    typeKind: !_TypeKind Output