﻿!_Schema
types: !_Map_Type
  !_Identifier EnumFieldModEnumOutp: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumFieldModEnumOutp
        name: value
    items:
      - !_Aliased
        name: value
    name: EnumFieldModEnumOutp
    typeKind: !_TypeKind Enum
  !_Identifier FieldModEnumOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        modifiers: [!_ModifierDictionary{by:EnumFieldModEnumOutp,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Enum}]
        name: field
        object: FieldModEnumOutp
        type: !_OutputBase
          output: String
    fields:
      - !_OutputField
        modifiers: [!_ModifierDictionary{by:EnumFieldModEnumOutp,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Enum}]
        name: field
        type: !_OutputBase
          output: String
    name: FieldModEnumOutp
    typeKind: !_TypeKind Output