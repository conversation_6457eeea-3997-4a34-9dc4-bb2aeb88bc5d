﻿!_Schema
types: !_Map_Type
  !_Identifier FldOutpFieldParam: !_TypeDual
    name: FldOutpFieldParam
    typeKind: !_TypeKind Dual
  !_Identifier OutpFieldParam: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: OutpFieldParam
        parameters:
          - !_InputParam
            input: OutpFieldParam1
          - !_InputParam
            input: OutpFieldParam2
        type: !_DualBase
          dual: FldOutpFieldParam
    fields:
      - !_OutputField
        name: field
        parameters:
          - !_InputParam
            input: OutpFieldParam1
          - !_InputParam
            input: OutpFieldParam2
        type: !_DualBase
          dual: FldOutpFieldParam
    name: OutpFieldParam
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldParam1: !_TypeInput
    name: OutpFieldParam1
    typeKind: !_TypeKind Input
  !_Identifier OutpFieldParam2: !_TypeInput
    name: OutpFieldParam2
    typeKind: !_TypeKind Input