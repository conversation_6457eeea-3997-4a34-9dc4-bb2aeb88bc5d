﻿{
  "$tag": "_Schema",
  "errors": [
    {
      "$tag": "_Error",
      "_kind": {
        "$tag": "_TokenKind",
        "value": "End"
      },
      "_message": "In _Category can\u0027t get model for type \u0027_Aliased\u0027"
    },
    {
      "$tag": "_Error",
      "_kind": {
        "$tag": "_TokenKind",
        "value": "End"
      },
      "_message": "In _Directive can\u0027t get model for type \u0027_Aliased\u0027"
    },
    {
      "$tag": "_Error",
      "_kind": {
        "$tag": "_TokenKind",
        "value": "End"
      },
      "_message": "In _Setting can\u0027t get model for type \u0027_Named\u0027"
    }
  ],
  "types": {
    "$tag": "_Map_Type",
    "_Categories": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Categories",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Category"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Categories",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "category",
          "object": "_Categories",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Category"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Categories",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Category"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "category",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Category"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "name": "_Categories",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Category": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "resolution",
          "object": "_Category",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Resolution"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "output",
          "object": "_Category",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Output",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "object": "_Category",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "resolution",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Resolution"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "output",
          "type": {
            "$tag": "_OutputBase",
            "output": "_TypeRef",
            "typeArgs": [
              {
                "$tag": "_OutputArg",
                "label": "Output",
                "typeKind": {
                  "$tag": "_SimpleKind",
                  "value": "Enum"
                },
                "typeName": "_TypeKind"
              }
            ]
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "modifiers",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Modifiers"
          }
        }
      ],
      "name": "_Category",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Aliased"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Directive": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "parameters",
          "object": "_Directive",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputParam"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "repeatable",
          "object": "_Directive",
          "type": {
            "$tag": "_OutputBase",
            "output": "Boolean"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Location",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Enum"
              }
            }
          ],
          "name": "locations",
          "object": "_Directive",
          "type": {
            "$tag": "_OutputBase",
            "output": "_"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_Modifier",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "List"
              }
            }
          ],
          "name": "parameters",
          "type": {
            "$tag": "_OutputBase",
            "output": "_InputParam"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "repeatable",
          "type": {
            "$tag": "_OutputBase",
            "output": "Boolean"
          }
        },
        {
          "$tag": "_OutputField",
          "modifiers": [
            {
              "$tag": "_ModifierDictionary",
              "by": "_Location",
              "modifierKind": {
                "$tag": "_ModifierKind",
                "value": "Dict"
              },
              "typeKind": {
                "$tag": "_SimpleKind",
                "value": "Enum"
              }
            }
          ],
          "name": "locations",
          "type": {
            "$tag": "_OutputBase",
            "output": "_"
          }
        }
      ],
      "name": "_Directive",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Aliased"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Directives": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Directives",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directive"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "_Directives",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "directive",
          "object": "_Directives",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directive"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "_Directives",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directive"
          }
        },
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "directive",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Directive"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "type",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Type"
          }
        }
      ],
      "name": "_Directives",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "_Location": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Operation"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Variable"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Field"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Inline"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Spread"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Location",
          "name": "Fragment"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Operation"
        },
        {
          "$tag": "_Aliased",
          "name": "Variable"
        },
        {
          "$tag": "_Aliased",
          "name": "Field"
        },
        {
          "$tag": "_Aliased",
          "name": "Inline"
        },
        {
          "$tag": "_Aliased",
          "name": "Spread"
        },
        {
          "$tag": "_Aliased",
          "name": "Fragment"
        }
      ],
      "name": "_Location",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "_Resolution": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "_Resolution",
          "name": "Parallel"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Resolution",
          "name": "Sequential"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "_Resolution",
          "name": "Single"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Parallel"
        },
        {
          "$tag": "_Aliased",
          "name": "Sequential"
        },
        {
          "$tag": "_Aliased",
          "name": "Single"
        }
      ],
      "name": "_Resolution",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "_Setting": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "value",
          "object": "_Setting",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "value",
          "type": {
            "$tag": "_OutputBase",
            "output": "_Constant"
          }
        }
      ],
      "name": "_Setting",
      "parent": {
        "$tag": "_OutputBase",
        "output": "_Named"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    }
  }
}