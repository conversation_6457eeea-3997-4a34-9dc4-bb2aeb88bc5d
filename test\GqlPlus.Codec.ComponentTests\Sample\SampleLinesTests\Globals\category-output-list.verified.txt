﻿!_Schema
categories: !_Map_Categories
  !_Identifier ctgrOutpList: !_Category
    modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
    name: ctgrOutpList
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrOutpList
    resolution: !_Resolution Parallel
types: !_Map_Type
  !_Identifier CtgrOutpList: !_TypeOutput
    name: CtgrOutpList
    typeKind: !_TypeKind Output