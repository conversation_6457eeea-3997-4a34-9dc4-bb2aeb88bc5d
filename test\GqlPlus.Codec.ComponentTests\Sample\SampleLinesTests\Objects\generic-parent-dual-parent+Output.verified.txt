﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcPrntDualPrntOutp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcPrntDualPrntOutp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcPrntDualPrntOutp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcPrntDualPrntOutp
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntDualPrntOutp: !_TypeOutput
    name: GnrcPrntDualPrntOutp
    parent: !_OutputBase
      output: RefGnrcPrntDualPrntOutp
      typeArgs:
        - !_DualArg
          dual: AltGnrcPrntDualPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier RefGnrcPrntDualPrntOutp: !_TypeOutput
    name: RefGnrcPrntDualPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref