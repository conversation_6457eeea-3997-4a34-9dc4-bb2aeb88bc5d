﻿!_Schema
types: !_Map_Type
  !_Identifier _Aliased: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    fields:
    - !_DualField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      type: !_DualBase
        dual: _Identifier
    name: _Aliased
    parent: !_DualBase
      dual: _Named
    typeKind: !_TypeKind Dual
  !_Identifier _Alternate: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: type
      object: _Alternate
      type: !_OutputBase
        typeParam: base
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: collections
      object: _Alternate
      type: !_OutputBase
        output: _Collections
    fields:
    - !_OutputField
      name: type
      type: !_OutputBase
        typeParam: base
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: collections
      type: !_OutputBase
        output: _Collections
    name: _Alternate
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _ObjBase
      name: base
  !_Identifier _BaseDomain: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _BaseType
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: parent
      object: _ChildType
      type: !_DualBase
        dual: _Named
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: items
      object: _ParentType
      type: !_OutputBase
        typeParam: item
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allItems
      object: _ParentType
      type: !_OutputBase
        typeParam: allItem
    - !_ObjectFor(_OutputField)
      name: domainKind
      object: _BaseDomain
      type: !_OutputBase
        typeParam: domain
    fields:
    - !_OutputField
      name: domainKind
      type: !_OutputBase
        typeParam: domain
    name: _BaseDomain
    parent: !_OutputBase
      output: _ParentType
      typeArgs:
      - !_OutputArg
        label: Domain
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
      - !_OutputArg
        typeParam: item
      - !_OutputArg
        typeParam: domainItem
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: _DomainKind
      name: domain
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: _BaseDomainItem
      name: item
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _DomainItem
      name: domainItem
  !_Identifier _BaseDomainItem: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: exclude
      object: _BaseDomainItem
      type: !_DualBase
        dual: Boolean
    fields:
    - !_DualField
      name: exclude
      type: !_DualBase
        dual: Boolean
    name: _BaseDomainItem
    parent: !_DualBase
      dual: _Described
    typeKind: !_TypeKind Dual
  !_Identifier _BaseType: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _BaseType
      type: !_OutputBase
        typeParam: kind
    fields:
    - !_OutputField
      name: typeKind
      type: !_OutputBase
        typeParam: kind
    name: _BaseType
    parent: !_DualBase
      dual: _Aliased
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: _TypeKind
      name: kind
  !_Identifier _BasicValue: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _BasicValue
      type: !_OutputBase
        output: Boolean
    - !_ObjectFor(_OutputAlternate)
      object: _BasicValue
      type: !_OutputBase
        output: _EnumValue
    - !_ObjectFor(_OutputAlternate)
      object: _BasicValue
      type: !_OutputBase
        output: Number
    - !_ObjectFor(_OutputAlternate)
      object: _BasicValue
      type: !_OutputBase
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: Boolean
    - !_OutputAlternate
      type: !_OutputBase
        output: _EnumValue
    - !_OutputAlternate
      type: !_OutputBase
        output: Number
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    name: _BasicValue
    typeKind: !_TypeKind Output
  !_Identifier _Categories: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _Categories
      type: !_OutputBase
        output: _Category
    - !_ObjectFor(_OutputAlternate)
      object: _Categories
      type: !_OutputBase
        output: _Type
    allFields:
    - !_ObjectFor(_OutputField)
      name: category
      object: _Categories
      type: !_OutputBase
        output: _Category
    - !_ObjectFor(_OutputField)
      name: type
      object: _Categories
      type: !_OutputBase
        output: _Type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _Category
    - !_OutputAlternate
      type: !_OutputBase
        output: _Type
    fields:
    - !_OutputField
      name: category
      type: !_OutputBase
        output: _Category
    - !_OutputField
      name: type
      type: !_OutputBase
        output: _Type
    name: _Categories
    typeKind: !_TypeKind Output
  !_Identifier _Category: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: resolution
      object: _Category
      type: !_OutputBase
        output: _Resolution
    - !_ObjectFor(_OutputField)
      name: output
      object: _Category
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Output
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      object: _Category
      type: !_OutputBase
        output: _Modifiers
    fields:
    - !_OutputField
      name: resolution
      type: !_OutputBase
        output: _Resolution
    - !_OutputField
      name: output
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Output
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      type: !_OutputBase
        output: _Modifiers
    name: _Category
    parent: !_DualBase
      dual: _Aliased
    typeKind: !_TypeKind Output
  !_Identifier _CategoryFilter: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_Modifier
        modifierKind: !_ModifierKind List
      object: _Filter
      type: !_InputBase
        input: _NameFilter
    allFields:
    - !_ObjectFor(_InputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: names
      object: _Filter
      type: !_InputBase
        input: _NameFilter
    - !_ObjectFor(_InputField)
      default: true
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: matchAliases
      object: _Filter
      type: !_InputBase
        input: Boolean
    - !_ObjectFor(_InputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Filter
      type: !_InputBase
        input: _NameFilter
    - !_ObjectFor(_InputField)
      default: false
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: returnByAlias
      object: _Filter
      type: !_InputBase
        input: Boolean
    - !_ObjectFor(_InputField)
      default: false
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: returnReferencedTypes
      object: _Filter
      type: !_InputBase
        input: Boolean
    - !_ObjectFor(_InputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: resolutions
      object: _CategoryFilter
      type: !_InputBase
        input: _Resolution
    fields:
    - !_InputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: resolutions
      type: !_InputBase
        input: _Resolution
    name: _CategoryFilter
    parent: !_InputBase
      input: _Filter
    typeKind: !_TypeKind Input
  !_Identifier _ChildType: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _BaseType
      type: !_OutputBase
        typeParam: kind
    - !_ObjectFor(_OutputField)
      name: parent
      object: _ChildType
      type: !_OutputBase
        typeParam: parent
    fields:
    - !_OutputField
      name: parent
      type: !_OutputBase
        typeParam: parent
    name: _ChildType
    parent: !_OutputBase
      output: _BaseType
      typeArgs:
      - !_OutputArg
        typeParam: kind
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: _TypeKind
      name: kind
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: _Described
      name: parent
  !_Identifier _Collections: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _Collections
      type: !_OutputBase
        output: _Modifier
        typeArgs:
        - !_OutputArg
          label: List
          typeKind: !_SimpleKind Enum
          typeName: _ModifierKind
    - !_ObjectFor(_OutputAlternate)
      object: _Collections
      type: !_OutputBase
        output: _ModifierKeyed
        typeArgs:
        - !_OutputArg
          label: Dictionary
          typeKind: !_SimpleKind Enum
          typeName: _ModifierKind
    - !_ObjectFor(_OutputAlternate)
      object: _Collections
      type: !_OutputBase
        output: _ModifierKeyed
        typeArgs:
        - !_OutputArg
          label: TypeParam
          typeKind: !_SimpleKind Enum
          typeName: _ModifierKind
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _Modifier
        typeArgs:
        - !_OutputArg
          label: List
          typeKind: !_SimpleKind Enum
          typeName: _ModifierKind
    - !_OutputAlternate
      type: !_OutputBase
        output: _ModifierKeyed
        typeArgs:
        - !_OutputArg
          label: Dictionary
          typeKind: !_SimpleKind Enum
          typeName: _ModifierKind
    - !_OutputAlternate
      type: !_OutputBase
        output: _ModifierKeyed
        typeArgs:
        - !_OutputArg
          label: TypeParam
          typeKind: !_SimpleKind Enum
          typeName: _ModifierKind
    name: _Collections
    typeKind: !_TypeKind Output
  !_Identifier _Constant: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _Constant
      type: !_OutputBase
        output: _SimpleValue
    - !_ObjectFor(_OutputAlternate)
      object: _Constant
      type: !_OutputBase
        output: _ConstantList
    - !_ObjectFor(_OutputAlternate)
      object: _Constant
      type: !_OutputBase
        output: _ConstantMap
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _SimpleValue
    - !_OutputAlternate
      type: !_OutputBase
        output: _ConstantList
    - !_OutputAlternate
      type: !_OutputBase
        output: _ConstantMap
    name: _Constant
    typeKind: !_TypeKind Output
  !_Identifier _ConstantList: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      collections:
      - !_Modifier
        modifierKind: !_ModifierKind List
      object: _ConstantList
      type: !_OutputBase
        output: _Constant
    alternates:
    - !_OutputAlternate
      collections:
      - !_Modifier
        modifierKind: !_ModifierKind List
      type: !_OutputBase
        output: _Constant
    name: _ConstantList
    typeKind: !_TypeKind Output
  !_Identifier _ConstantMap: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      collections:
      - !_ModifierDictionary
        by: Simple
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Internal
      object: _ConstantMap
      type: !_OutputBase
        output: _Constant
    alternates:
    - !_OutputAlternate
      collections:
      - !_ModifierDictionary
        by: Simple
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Internal
      type: !_OutputBase
        output: _Constant
    name: _ConstantMap
    typeKind: !_TypeKind Output
  !_Identifier _Described: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      type: !_DualBase
        dual: String
    name: _Described
    typeKind: !_TypeKind Dual
  !_Identifier _Directive: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: parameters
      object: _Directive
      type: !_OutputBase
        output: _InputParam
    - !_ObjectFor(_OutputField)
      name: repeatable
      object: _Directive
      type: !_OutputBase
        output: Boolean
    - !_ObjectFor(_OutputField)
      modifiers: [!_ModifierDictionary {by: _Location, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Enum}]
      name: locations
      object: _Directive
      type: !_OutputBase
        output: _
    fields:
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: parameters
      type: !_OutputBase
        output: _InputParam
    - !_OutputField
      name: repeatable
      type: !_OutputBase
        output: Boolean
    - !_OutputField
      modifiers: [!_ModifierDictionary {by: _Location, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Enum}]
      name: locations
      type: !_OutputBase
        output: _
    name: _Directive
    parent: !_DualBase
      dual: _Aliased
    typeKind: !_TypeKind Output
  !_Identifier _Directives: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _Directives
      type: !_OutputBase
        output: _Directive
    - !_ObjectFor(_OutputAlternate)
      object: _Directives
      type: !_OutputBase
        output: _Type
    allFields:
    - !_ObjectFor(_OutputField)
      name: directive
      object: _Directives
      type: !_OutputBase
        output: _Directive
    - !_ObjectFor(_OutputField)
      name: type
      object: _Directives
      type: !_OutputBase
        output: _Type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _Directive
    - !_OutputAlternate
      type: !_OutputBase
        output: _Type
    fields:
    - !_OutputField
      name: directive
      type: !_OutputBase
        output: _Directive
    - !_OutputField
      name: type
      type: !_OutputBase
        output: _Type
    name: _Directives
    typeKind: !_TypeKind Output
  !_Identifier _DomainItem: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: domain
      object: _DomainItem
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: domain
      type: !_OutputBase
        output: _Identifier
    name: _DomainItem
    parent: !_OutputBase
      typeParam: item
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: _BaseDomainItem
      name: item
  !_Identifier _DomainItemLabel: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: exclude
      object: _BaseDomainItem
      type: !_DualBase
        dual: Boolean
    - !_ObjectFor(_OutputField)
      name: label
      object: _DomainLabel
      type: !_OutputBase
        output: _EnumValue
    - !_ObjectFor(_OutputField)
      name: domain
      object: _DomainItem
      type: !_OutputBase
        output: _Identifier
    name: _DomainItemLabel
    parent: !_OutputBase
      output: _DomainItem
      typeArgs:
      - !_OutputArg
        output: _DomainLabel
    typeKind: !_TypeKind Output
  !_Identifier _DomainItemRange: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: domain
      object: _DomainItem
      type: !_OutputBase
        output: _Identifier
    name: _DomainItemRange
    parent: !_OutputBase
      output: _DomainItem
      typeArgs:
      - !_DualArg
        dual: _DomainRange
    typeKind: !_TypeKind Output
  !_Identifier _DomainItemRegex: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: domain
      object: _DomainItem
      type: !_OutputBase
        output: _Identifier
    name: _DomainItemRegex
    parent: !_OutputBase
      output: _DomainItem
      typeArgs:
      - !_DualArg
        dual: _DomainRegex
    typeKind: !_TypeKind Output
  !_Identifier _DomainItemTrueFalse: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: domain
      object: _DomainItem
      type: !_OutputBase
        output: _Identifier
    name: _DomainItemTrueFalse
    parent: !_OutputBase
      output: _DomainItem
      typeArgs:
      - !_DualArg
        dual: _DomainTrueFalse
    typeKind: !_TypeKind Output
  !_Identifier _DomainKind: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: _DomainKind
      name: Boolean
    - !_EnumLabel
      enum: _DomainKind
      name: Enum
    - !_EnumLabel
      enum: _DomainKind
      name: Number
    - !_EnumLabel
      enum: _DomainKind
      name: String
    items:
    - !_Aliased
      name: Boolean
    - !_Aliased
      name: Enum
    - !_Aliased
      name: Number
    - !_Aliased
      name: String
    name: _DomainKind
    typeKind: !_TypeKind Enum
  !_Identifier _DomainLabel: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: exclude
      object: _BaseDomainItem
      type: !_DualBase
        dual: Boolean
    - !_ObjectFor(_OutputField)
      name: label
      object: _DomainLabel
      type: !_OutputBase
        output: _EnumValue
    fields:
    - !_OutputField
      name: label
      type: !_OutputBase
        output: _EnumValue
    name: _DomainLabel
    parent: !_DualBase
      dual: _BaseDomainItem
    typeKind: !_TypeKind Output
  !_Identifier _DomainRange: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: exclude
      object: _BaseDomainItem
      type: !_DualBase
        dual: Boolean
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: lower
      object: _DomainRange
      type: !_DualBase
        dual: Number
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: upper
      object: _DomainRange
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: lower
      type: !_DualBase
        dual: Number
    - !_DualField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: upper
      type: !_DualBase
        dual: Number
    name: _DomainRange
    parent: !_DualBase
      dual: _BaseDomainItem
    typeKind: !_TypeKind Dual
  !_Identifier _DomainRef: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      name: domainKind
      object: _DomainRef
      type: !_OutputBase
        typeParam: kind
    fields:
    - !_OutputField
      name: domainKind
      type: !_OutputBase
        typeParam: kind
    name: _DomainRef
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
      - !_OutputArg
        label: Domain
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: _DomainKind
      name: kind
  !_Identifier _DomainRegex: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: exclude
      object: _BaseDomainItem
      type: !_DualBase
        dual: Boolean
    - !_ObjectFor(_DualField)
      name: pattern
      object: _DomainRegex
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: pattern
      type: !_DualBase
        dual: String
    name: _DomainRegex
    parent: !_DualBase
      dual: _BaseDomainItem
    typeKind: !_TypeKind Dual
  !_Identifier _DomainTrueFalse: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: exclude
      object: _BaseDomainItem
      type: !_DualBase
        dual: Boolean
    - !_ObjectFor(_DualField)
      name: value
      object: _DomainTrueFalse
      type: !_DualBase
        dual: Boolean
    fields:
    - !_DualField
      name: value
      type: !_DualBase
        dual: Boolean
    name: _DomainTrueFalse
    parent: !_DualBase
      dual: _BaseDomainItem
    typeKind: !_TypeKind Dual
  !_Identifier _DomainValue: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      name: domainKind
      object: _DomainRef
      type: !_OutputBase
        typeParam: kind
    - !_ObjectFor(_OutputField)
      name: value
      object: _DomainValue
      type: !_OutputBase
        typeParam: value
    fields:
    - !_OutputField
      name: value
      type: !_OutputBase
        typeParam: value
    name: _DomainValue
    parent: !_OutputBase
      output: _DomainRef
      typeArgs:
      - !_OutputArg
        typeParam: kind
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: _DomainKind
      name: kind
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _BasicValue
      name: value
  !_Identifier _DualAlternate: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: type
      object: _Alternate
      type: !_OutputBase
        output: _DualBase
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: collections
      object: _Alternate
      type: !_OutputBase
        output: _Collections
    name: _DualAlternate
    parent: !_OutputBase
      output: _Alternate
      typeArgs:
      - !_OutputArg
        output: _DualBase
    typeKind: !_TypeKind Output
  !_Identifier _DualBase: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _ObjBase
      type: !_OutputBase
        output: _TypeParam
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeArgs
      object: _ObjBase
      type: !_OutputBase
        output: _DualTypeArg
    - !_ObjectFor(_OutputField)
      name: dual
      object: _DualBase
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: dual
      type: !_OutputBase
        output: _Identifier
    name: _DualBase
    parent: !_OutputBase
      output: _ObjBase
      typeArgs:
      - !_OutputArg
        output: _DualTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _DualField: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: type
      object: _Field
      type: !_OutputBase
        output: _DualBase
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      object: _Field
      type: !_OutputBase
        output: _Modifiers
    name: _DualField
    parent: !_OutputBase
      output: _Field
      typeArgs:
      - !_OutputArg
        output: _DualBase
    typeKind: !_TypeKind Output
  !_Identifier _DualTypeArg: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _ObjTypeArg
      type: !_OutputBase
        output: _TypeParam
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      name: dual
      object: _DualTypeArg
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: dual
      type: !_OutputBase
        output: _Identifier
    name: _DualTypeArg
    parent: !_OutputBase
      output: _ObjTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _DualTypeParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: constraint
      object: _ObjTypeParam
      type: !_OutputBase
        output: _ObjConstraint
        typeArgs:
        - !_OutputArg
          typeParam: kind
    name: _DualTypeParam
    parent: !_OutputBase
      output: _ObjTypeParam
      typeArgs:
      - !_OutputArg
        label: Dual
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _EnumLabel: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      name: enum
      object: _EnumLabel
      type: !_DualBase
        dual: _Identifier
    fields:
    - !_DualField
      name: enum
      type: !_DualBase
        dual: _Identifier
    name: _EnumLabel
    parent: !_DualBase
      dual: _Aliased
    typeKind: !_TypeKind Dual
  !_Identifier _EnumValue: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      name: label
      object: _EnumValue
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: label
      type: !_OutputBase
        output: _Identifier
    name: _EnumValue
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
      - !_OutputArg
        label: Enum
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _Field: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: type
      object: _Field
      type: !_OutputBase
        typeParam: base
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      object: _Field
      type: !_OutputBase
        output: _Modifiers
    fields:
    - !_OutputField
      name: type
      type: !_OutputBase
        typeParam: base
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      type: !_OutputBase
        output: _Modifiers
    name: _Field
    parent: !_DualBase
      dual: _Aliased
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _ObjBase
      name: base
  !_Identifier _Filter: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_Modifier
        modifierKind: !_ModifierKind List
      object: _Filter
      type: !_InputBase
        input: _NameFilter
    allFields:
    - !_ObjectFor(_InputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: names
      object: _Filter
      type: !_InputBase
        input: _NameFilter
    - !_ObjectFor(_InputField)
      default: true
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: matchAliases
      object: _Filter
      type: !_InputBase
        input: Boolean
    - !_ObjectFor(_InputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Filter
      type: !_InputBase
        input: _NameFilter
    - !_ObjectFor(_InputField)
      default: false
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: returnByAlias
      object: _Filter
      type: !_InputBase
        input: Boolean
    - !_ObjectFor(_InputField)
      default: false
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: returnReferencedTypes
      object: _Filter
      type: !_InputBase
        input: Boolean
    alternates:
    - !_InputAlternate
      collections:
      - !_Modifier
        modifierKind: !_ModifierKind List
      type: !_InputBase
        input: _NameFilter
    fields:
    - !_InputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: names
      type: !_InputBase
        input: _NameFilter
    - !_InputField
      default: true
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: matchAliases
      type: !_InputBase
        input: Boolean
    - !_InputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      type: !_InputBase
        input: _NameFilter
    - !_InputField
      default: false
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: returnByAlias
      type: !_InputBase
        input: Boolean
    - !_InputField
      default: false
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: returnReferencedTypes
      type: !_InputBase
        input: Boolean
    name: _Filter
    typeKind: !_TypeKind Input
  !_Identifier _ForParam: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _ForParam
      type: !_OutputBase
        output: _Alternate
        typeArgs:
        - !_OutputArg
          typeParam: base
    - !_ObjectFor(_OutputAlternate)
      object: _ForParam
      type: !_OutputBase
        output: _Field
        typeArgs:
        - !_OutputArg
          typeParam: base
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _Alternate
        typeArgs:
        - !_OutputArg
          typeParam: base
    - !_OutputAlternate
      type: !_OutputBase
        output: _Field
        typeArgs:
        - !_OutputArg
          typeParam: base
    name: _ForParam
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _ObjBase
      name: base
  !_Identifier _Identifier: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: _Identifier
      exclude: false
      pattern: '[A-Za-z_]+'
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: '[A-Za-z_]+'
    name: _Identifier
    typeKind: !_TypeKind Domain
  !_Identifier _InputAlternate: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: type
      object: _Alternate
      type: !_OutputBase
        output: _InputBase
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: collections
      object: _Alternate
      type: !_OutputBase
        output: _Collections
    name: _InputAlternate
    parent: !_OutputBase
      output: _Alternate
      typeArgs:
      - !_OutputArg
        output: _InputBase
    typeKind: !_TypeKind Output
  !_Identifier _InputBase: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _ObjBase
      type: !_OutputBase
        output: _TypeParam
    - !_ObjectFor(_OutputAlternate)
      object: _InputBase
      type: !_OutputBase
        output: _DualBase
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeArgs
      object: _ObjBase
      type: !_OutputBase
        output: _InputTypeArg
    - !_ObjectFor(_OutputField)
      name: input
      object: _InputBase
      type: !_OutputBase
        output: _Identifier
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _DualBase
    fields:
    - !_OutputField
      name: input
      type: !_OutputBase
        output: _Identifier
    name: _InputBase
    parent: !_OutputBase
      output: _ObjBase
      typeArgs:
      - !_OutputArg
        output: _InputTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _InputField: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: type
      object: _Field
      type: !_OutputBase
        output: _InputBase
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      object: _Field
      type: !_OutputBase
        output: _Modifiers
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: default
      object: _InputField
      type: !_OutputBase
        output: _Constant
    fields:
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: default
      type: !_OutputBase
        output: _Constant
    name: _InputField
    parent: !_OutputBase
      output: _Field
      typeArgs:
      - !_OutputArg
        output: _InputBase
    typeKind: !_TypeKind Output
  !_Identifier _InputParam: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _ObjBase
      type: !_OutputBase
        output: _TypeParam
    - !_ObjectFor(_OutputAlternate)
      object: _InputBase
      type: !_OutputBase
        output: _DualBase
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeArgs
      object: _ObjBase
      type: !_OutputBase
        output: _InputTypeArg
    - !_ObjectFor(_OutputField)
      name: input
      object: _InputBase
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      object: _InputParam
      type: !_OutputBase
        output: _Modifiers
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: default
      object: _InputParam
      type: !_OutputBase
        output: _Constant
    fields:
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      type: !_OutputBase
        output: _Modifiers
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: default
      type: !_OutputBase
        output: _Constant
    name: _InputParam
    parent: !_OutputBase
      output: _InputBase
    typeKind: !_TypeKind Output
  !_Identifier _InputTypeArg: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _ObjTypeArg
      type: !_OutputBase
        output: _TypeParam
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      name: input
      object: _InputTypeArg
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: input
      type: !_OutputBase
        output: _Identifier
    name: _InputTypeArg
    parent: !_OutputBase
      output: _ObjTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _InputTypeParam: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _InputTypeParam
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Dual
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: constraint
      object: _ObjTypeParam
      type: !_OutputBase
        output: _ObjConstraint
        typeArgs:
        - !_OutputArg
          typeParam: kind
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Dual
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    name: _InputTypeParam
    parent: !_OutputBase
      output: _ObjTypeParam
      typeArgs:
      - !_OutputArg
        label: Input
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _Internal: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _Internal
      type: !_OutputBase
        output: Null
    - !_ObjectFor(_OutputAlternate)
      object: _Internal
      type: !_OutputBase
        output: Object
    - !_ObjectFor(_OutputAlternate)
      object: _Internal
      type: !_OutputBase
        output: Void
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: Null
    - !_OutputAlternate
      type: !_OutputBase
        output: Object
    - !_OutputAlternate
      type: !_OutputBase
        output: Void
    name: _Internal
    typeKind: !_TypeKind Output
  !_Identifier _Location: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: _Location
      name: Operation
    - !_EnumLabel
      enum: _Location
      name: Variable
    - !_EnumLabel
      enum: _Location
      name: Field
    - !_EnumLabel
      enum: _Location
      name: Inline
    - !_EnumLabel
      enum: _Location
      name: Spread
    - !_EnumLabel
      enum: _Location
      name: Fragment
    items:
    - !_Aliased
      name: Operation
    - !_Aliased
      name: Variable
    - !_Aliased
      name: Field
    - !_Aliased
      name: Inline
    - !_Aliased
      name: Spread
    - !_Aliased
      name: Fragment
    name: _Location
    typeKind: !_TypeKind Enum
  !_Identifier _Modifier: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: modifierKind
      object: _Modifier
      type: !_OutputBase
        typeParam: kind
    fields:
    - !_OutputField
      name: modifierKind
      type: !_OutputBase
        typeParam: kind
    name: _Modifier
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: _ModifierKind
      name: kind
  !_Identifier _ModifierKeyed: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: modifierKind
      object: _Modifier
      type: !_OutputBase
        typeParam: kind
    - !_ObjectFor(_OutputField)
      name: by
      object: _ModifierKeyed
      type: !_OutputBase
        output: _TypeSimple
    - !_ObjectFor(_OutputField)
      name: optional
      object: _ModifierKeyed
      type: !_OutputBase
        output: Boolean
    fields:
    - !_OutputField
      name: by
      type: !_OutputBase
        output: _TypeSimple
    - !_OutputField
      name: optional
      type: !_OutputBase
        output: Boolean
    name: _ModifierKeyed
    parent: !_OutputBase
      output: _Modifier
      typeArgs:
      - !_OutputArg
        typeParam: kind
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: _ModifierKind
      name: kind
  !_Identifier _ModifierKind: !_TypeEnum
    allItems:
    - !_EnumLabel
      aliases: [Optional]
      enum: _ModifierKind
      name: Opt
    - !_EnumLabel
      enum: _ModifierKind
      name: List
    - !_EnumLabel
      aliases: [Dictionary]
      enum: _ModifierKind
      name: Dict
    - !_EnumLabel
      aliases: [TypeParam]
      enum: _ModifierKind
      name: Param
    items:
    - !_Aliased
      aliases: [Optional]
      name: Opt
    - !_Aliased
      name: List
    - !_Aliased
      aliases: [Dictionary]
      name: Dict
    - !_Aliased
      aliases: [TypeParam]
      name: Param
    name: _ModifierKind
    typeKind: !_TypeKind Enum
  !_Identifier _Modifiers: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _Modifiers
      type: !_OutputBase
        output: _Modifier
        typeArgs:
        - !_OutputArg
          label: Optional
          typeKind: !_SimpleKind Enum
          typeName: _ModifierKind
    - !_ObjectFor(_OutputAlternate)
      object: _Modifiers
      type: !_OutputBase
        output: _Collections
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _Modifier
        typeArgs:
        - !_OutputArg
          label: Optional
          typeKind: !_SimpleKind Enum
          typeName: _ModifierKind
    - !_OutputAlternate
      type: !_OutputBase
        output: _Collections
    name: _Modifiers
    typeKind: !_TypeKind Output
  !_Identifier _NameFilter: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: _NameFilter
      exclude: false
      pattern: '[A-Za-z_.*]+'
    description: >-
      _NameFilter is a simple match expression against _Identifier
      where '.' matches any single character and '*' matches zero
      or more of any character.
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: '[A-Za-z_.*]+'
    name: _NameFilter
    typeKind: !_TypeKind Domain
  !_Identifier _Named: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    fields:
    - !_DualField
      name: name
      type: !_DualBase
        dual: _Identifier
    name: _Named
    parent: !_DualBase
      dual: _Described
    typeKind: !_TypeKind Dual
  !_Identifier _ObjBase: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _ObjBase
      type: !_OutputBase
        output: _TypeParam
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeArgs
      object: _ObjBase
      type: !_OutputBase
        typeParam: arg
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeParam
    fields:
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeArgs
      type: !_OutputBase
        typeParam: arg
    name: _ObjBase
    parent: !_DualBase
      dual: _Described
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _ObjTypeArg
      name: arg
  !_Identifier _ObjConstraint: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        typeParam: kind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    name: _ObjConstraint
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
      - !_OutputArg
        typeParam: kind
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Domain
        typeName: _ObjectKind
      name: kind
  !_Identifier _ObjTypeArg: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _ObjTypeArg
      type: !_OutputBase
        output: _TypeParam
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeParam
    name: _ObjTypeArg
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
      - !_OutputArg
        output: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _ObjTypeParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: constraint
      object: _ObjTypeParam
      type: !_OutputBase
        output: _ObjConstraint
        typeArgs:
        - !_OutputArg
          typeParam: kind
    fields:
    - !_OutputField
      name: constraint
      type: !_OutputBase
        output: _ObjConstraint
        typeArgs:
        - !_OutputArg
          typeParam: kind
    name: _ObjTypeParam
    parent: !_DualBase
      dual: _Named
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Domain
        typeName: _ObjectKind
      name: kind
  !_Identifier _ObjectFor: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: object
      object: _ObjectFor
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: object
      type: !_OutputBase
        output: _Identifier
    name: _ObjectFor
    parent: !_OutputBase
      typeParam: for
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _ForParam
      name: for
  !_Identifier _ObjectKind: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: _ObjectKind
      exclude: false
      value: !_EnumValue
        label: Dual
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    - !_DomainItem(_DomainLabel)
      domain: _ObjectKind
      exclude: false
      value: !_EnumValue
        label: Input
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    - !_DomainItem(_DomainLabel)
      domain: _ObjectKind
      exclude: false
      value: !_EnumValue
        label: Output
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: Dual
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: Input
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: Output
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    name: _ObjectKind
    typeKind: !_TypeKind Domain
  !_Identifier _OutputAlternate: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: type
      object: _Alternate
      type: !_OutputBase
        output: _OutputBase
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: collections
      object: _Alternate
      type: !_OutputBase
        output: _Collections
    name: _OutputAlternate
    parent: !_OutputBase
      output: _Alternate
      typeArgs:
      - !_OutputArg
        output: _OutputBase
    typeKind: !_TypeKind Output
  !_Identifier _OutputBase: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _ObjBase
      type: !_OutputBase
        output: _TypeParam
    - !_ObjectFor(_OutputAlternate)
      object: _OutputBase
      type: !_OutputBase
        output: _DualBase
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeArgs
      object: _ObjBase
      type: !_OutputBase
        output: _OutputTypeArg
    - !_ObjectFor(_OutputField)
      name: output
      object: _OutputBase
      type: !_OutputBase
        output: _Identifier
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _DualBase
    fields:
    - !_OutputField
      name: output
      type: !_OutputBase
        output: _Identifier
    name: _OutputBase
    parent: !_OutputBase
      output: _ObjBase
      typeArgs:
      - !_OutputArg
        output: _OutputTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _OutputEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      name: field
      object: _OutputEnum
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      name: label
      object: _OutputEnum
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: _Identifier
    - !_OutputField
      name: label
      type: !_OutputBase
        output: _Identifier
    name: _OutputEnum
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
      - !_OutputArg
        label: Enum
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _OutputField: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _OutputField
      type: !_OutputBase
        output: _OutputEnum
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: type
      object: _Field
      type: !_OutputBase
        output: _OutputBase
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      object: _Field
      type: !_OutputBase
        output: _Modifiers
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: parameters
      object: _OutputField
      type: !_OutputBase
        output: _InputParam
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _OutputEnum
    fields:
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: parameters
      type: !_OutputBase
        output: _InputParam
    name: _OutputField
    parent: !_OutputBase
      output: _Field
      typeArgs:
      - !_OutputArg
        output: _OutputBase
    typeKind: !_TypeKind Output
  !_Identifier _OutputTypeArg: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _ObjTypeArg
      type: !_OutputBase
        output: _TypeParam
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      name: output
      object: _OutputTypeArg
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: label
      object: _OutputTypeArg
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: output
      type: !_OutputBase
        output: _Identifier
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: label
      type: !_OutputBase
        output: _Identifier
    name: _OutputTypeArg
    parent: !_OutputBase
      output: _ObjTypeArg
    typeKind: !_TypeKind Output
  !_Identifier _OutputTypeParam: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _OutputTypeParam
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Dual
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_ObjectFor(_OutputAlternate)
      object: _OutputTypeParam
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: constraint
      object: _ObjTypeParam
      type: !_OutputBase
        output: _ObjConstraint
        typeArgs:
        - !_OutputArg
          typeParam: kind
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Dual
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    name: _OutputTypeParam
    parent: !_OutputBase
      output: _ObjTypeParam
      typeArgs:
      - !_OutputArg
        label: Output
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _ParentType: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _BaseType
      type: !_OutputBase
        typeParam: kind
    - !_ObjectFor(_OutputField)
      name: parent
      object: _ChildType
      type: !_DualBase
        dual: _Named
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: items
      object: _ParentType
      type: !_OutputBase
        typeParam: item
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allItems
      object: _ParentType
      type: !_OutputBase
        typeParam: allItem
    fields:
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: items
      type: !_OutputBase
        typeParam: item
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allItems
      type: !_OutputBase
        typeParam: allItem
    name: _ParentType
    parent: !_OutputBase
      output: _ChildType
      typeArgs:
      - !_OutputArg
        typeParam: kind
      - !_DualArg
        dual: _Named
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: _TypeKind
      name: kind
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: _Described
      name: item
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: _Described
      name: allItem
  !_Identifier _Resolution: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: _Resolution
      name: Parallel
    - !_EnumLabel
      enum: _Resolution
      name: Sequential
    - !_EnumLabel
      enum: _Resolution
      name: Single
    items:
    - !_Aliased
      name: Parallel
    - !_Aliased
      name: Sequential
    - !_Aliased
      name: Single
    name: _Resolution
    typeKind: !_TypeKind Enum
  !_Identifier _Schema: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      modifiers: [!_ModifierDictionary {by: _Identifier, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Domain}]
      name: categories
      object: _Schema
      parameters:
      - !_InputParam
        input: _CategoryFilter
        modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      type: !_OutputBase
        output: _Categories
    - !_ObjectFor(_OutputField)
      modifiers: [!_ModifierDictionary {by: _Identifier, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Domain}]
      name: directives
      object: _Schema
      parameters:
      - !_InputParam
        input: _Filter
        modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      type: !_OutputBase
        output: _Directives
    - !_ObjectFor(_OutputField)
      modifiers: [!_ModifierDictionary {by: _Identifier, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Domain}]
      name: types
      object: _Schema
      parameters:
      - !_InputParam
        input: _TypeFilter
        modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      type: !_OutputBase
        output: _Type
    - !_ObjectFor(_OutputField)
      modifiers: [!_ModifierDictionary {by: _Identifier, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Domain}]
      name: settings
      object: _Schema
      parameters:
      - !_InputParam
        input: _Filter
        modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      type: !_OutputBase
        output: _Setting
    fields:
    - !_OutputField
      modifiers: [!_ModifierDictionary {by: _Identifier, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Domain}]
      name: categories
      parameters:
      - !_InputParam
        input: _CategoryFilter
        modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      type: !_OutputBase
        output: _Categories
    - !_OutputField
      modifiers: [!_ModifierDictionary {by: _Identifier, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Domain}]
      name: directives
      parameters:
      - !_InputParam
        input: _Filter
        modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      type: !_OutputBase
        output: _Directives
    - !_OutputField
      modifiers: [!_ModifierDictionary {by: _Identifier, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Domain}]
      name: types
      parameters:
      - !_InputParam
        input: _TypeFilter
        modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      type: !_OutputBase
        output: _Type
    - !_OutputField
      modifiers: [!_ModifierDictionary {by: _Identifier, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Domain}]
      name: settings
      parameters:
      - !_InputParam
        input: _Filter
        modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      type: !_OutputBase
        output: _Setting
    name: _Schema
    parent: !_DualBase
      dual: _Named
    typeKind: !_TypeKind Output
  !_Identifier _Setting: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: value
      object: _Setting
      type: !_OutputBase
        output: _Constant
    fields:
    - !_OutputField
      name: value
      type: !_OutputBase
        output: _Constant
    name: _Setting
    parent: !_DualBase
      dual: _Named
    typeKind: !_TypeKind Output
  !_Identifier _SimpleKind: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: _SimpleKind
      name: Basic
    - !_EnumLabel
      enum: _SimpleKind
      name: Enum
    - !_EnumLabel
      enum: _SimpleKind
      name: Internal
    - !_EnumLabel
      enum: _SimpleKind
      name: Domain
    - !_EnumLabel
      enum: _SimpleKind
      name: Union
    items:
    - !_Aliased
      name: Basic
    - !_Aliased
      name: Enum
    - !_Aliased
      name: Internal
    - !_Aliased
      name: Domain
    - !_Aliased
      name: Union
    name: _SimpleKind
    typeKind: !_TypeKind Enum
  !_Identifier _SimpleValue: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _SimpleValue
      type: !_OutputBase
        output: _DomainValue
        typeArgs:
        - !_OutputArg
          label: Boolean
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_OutputArg
          output: Boolean
    - !_ObjectFor(_OutputAlternate)
      object: _SimpleValue
      type: !_OutputBase
        output: _DomainValue
        typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_OutputArg
          output: _EnumValue
    - !_ObjectFor(_OutputAlternate)
      object: _SimpleValue
      type: !_OutputBase
        output: _DomainValue
        typeArgs:
        - !_OutputArg
          label: Number
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_OutputArg
          output: Number
    - !_ObjectFor(_OutputAlternate)
      object: _SimpleValue
      type: !_OutputBase
        output: _DomainValue
        typeArgs:
        - !_OutputArg
          label: String
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_OutputArg
          output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _DomainValue
        typeArgs:
        - !_OutputArg
          label: Boolean
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_OutputArg
          output: Boolean
    - !_OutputAlternate
      type: !_OutputBase
        output: _DomainValue
        typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_OutputArg
          output: _EnumValue
    - !_OutputAlternate
      type: !_OutputBase
        output: _DomainValue
        typeArgs:
        - !_OutputArg
          label: Number
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_OutputArg
          output: Number
    - !_OutputAlternate
      type: !_OutputBase
        output: _DomainValue
        typeArgs:
        - !_OutputArg
          label: String
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_OutputArg
          output: String
    name: _SimpleValue
    typeKind: !_TypeKind Output
  !_Identifier _Type: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _Type
      type: !_OutputBase
        output: _BaseType
        typeArgs:
        - !_OutputArg
          label: Basic
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_ObjectFor(_OutputAlternate)
      object: _Type
      type: !_OutputBase
        output: _BaseType
        typeArgs:
        - !_OutputArg
          label: Internal
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_ObjectFor(_OutputAlternate)
      object: _Type
      type: !_OutputBase
        output: _TypeDual
    - !_ObjectFor(_OutputAlternate)
      object: _Type
      type: !_OutputBase
        output: _TypeEnum
    - !_ObjectFor(_OutputAlternate)
      object: _Type
      type: !_OutputBase
        output: _TypeInput
    - !_ObjectFor(_OutputAlternate)
      object: _Type
      type: !_OutputBase
        output: _TypeOutput
    - !_ObjectFor(_OutputAlternate)
      object: _Type
      type: !_OutputBase
        output: _TypeDomain
    - !_ObjectFor(_OutputAlternate)
      object: _Type
      type: !_OutputBase
        output: _TypeUnion
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _BaseType
        typeArgs:
        - !_OutputArg
          label: Basic
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_OutputAlternate
      type: !_OutputBase
        output: _BaseType
        typeArgs:
        - !_OutputArg
          label: Internal
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeDual
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeEnum
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeInput
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeOutput
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeDomain
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeUnion
    name: _Type
    typeKind: !_TypeKind Output
  !_Identifier _TypeDomain: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _TypeDomain
      type: !_OutputBase
        output: _BaseDomain
        typeArgs:
        - !_OutputArg
          label: Boolean
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_DualArg
          dual: _DomainTrueFalse
        - !_OutputArg
          output: _DomainItemTrueFalse
    - !_ObjectFor(_OutputAlternate)
      object: _TypeDomain
      type: !_OutputBase
        output: _BaseDomain
        typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_OutputArg
          output: _DomainLabel
        - !_OutputArg
          output: _DomainItemLabel
    - !_ObjectFor(_OutputAlternate)
      object: _TypeDomain
      type: !_OutputBase
        output: _BaseDomain
        typeArgs:
        - !_OutputArg
          label: Number
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_DualArg
          dual: _DomainRange
        - !_OutputArg
          output: _DomainItemRange
    - !_ObjectFor(_OutputAlternate)
      object: _TypeDomain
      type: !_OutputBase
        output: _BaseDomain
        typeArgs:
        - !_OutputArg
          label: String
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_DualArg
          dual: _DomainRegex
        - !_OutputArg
          output: _DomainItemRegex
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _BaseDomain
        typeArgs:
        - !_OutputArg
          label: Boolean
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_DualArg
          dual: _DomainTrueFalse
        - !_OutputArg
          output: _DomainItemTrueFalse
    - !_OutputAlternate
      type: !_OutputBase
        output: _BaseDomain
        typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_OutputArg
          output: _DomainLabel
        - !_OutputArg
          output: _DomainItemLabel
    - !_OutputAlternate
      type: !_OutputBase
        output: _BaseDomain
        typeArgs:
        - !_OutputArg
          label: Number
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_DualArg
          dual: _DomainRange
        - !_OutputArg
          output: _DomainItemRange
    - !_OutputAlternate
      type: !_OutputBase
        output: _BaseDomain
        typeArgs:
        - !_OutputArg
          label: String
          typeKind: !_SimpleKind Enum
          typeName: _DomainKind
        - !_DualArg
          dual: _DomainRegex
        - !_OutputArg
          output: _DomainItemRegex
    name: _TypeDomain
    typeKind: !_TypeKind Output
  !_Identifier _TypeDual: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _BaseType
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: parent
      object: _ChildType
      type: !_OutputBase
        output: _DualBase
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeParams
      object: _TypeObject
      type: !_OutputBase
        output: _DualTypeParam
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: fields
      object: _TypeObject
      type: !_OutputBase
        output: _DualField
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: alternates
      object: _TypeObject
      type: !_OutputBase
        output: _DualAlternate
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allFields
      object: _TypeObject
      type: !_OutputBase
        output: _ObjectFor
        typeArgs:
        - !_OutputArg
          typeParam: field
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allAlternates
      object: _TypeObject
      type: !_OutputBase
        output: _ObjectFor
        typeArgs:
        - !_OutputArg
          typeParam: alternate
    name: _TypeDual
    parent: !_OutputBase
      output: _TypeObject
      typeArgs:
      - !_OutputArg
        label: Dual
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
      - !_OutputArg
        output: _DualBase
      - !_OutputArg
        output: _DualTypeParam
      - !_OutputArg
        output: _DualField
      - !_OutputArg
        output: _DualAlternate
    typeKind: !_TypeKind Output
  !_Identifier _TypeEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _BaseType
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: parent
      object: _ChildType
      type: !_DualBase
        dual: _Named
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: items
      object: _ParentType
      type: !_DualBase
        dual: _Aliased
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allItems
      object: _ParentType
      type: !_DualBase
        dual: _EnumLabel
    name: _TypeEnum
    parent: !_OutputBase
      output: _ParentType
      typeArgs:
      - !_OutputArg
        label: Enum
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
      - !_DualArg
        dual: _Aliased
      - !_DualArg
        dual: _EnumLabel
    typeKind: !_TypeKind Output
  !_Identifier _TypeFilter: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_Modifier
        modifierKind: !_ModifierKind List
      object: _Filter
      type: !_InputBase
        input: _NameFilter
    allFields:
    - !_ObjectFor(_InputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: names
      object: _Filter
      type: !_InputBase
        input: _NameFilter
    - !_ObjectFor(_InputField)
      default: true
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: matchAliases
      object: _Filter
      type: !_InputBase
        input: Boolean
    - !_ObjectFor(_InputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Filter
      type: !_InputBase
        input: _NameFilter
    - !_ObjectFor(_InputField)
      default: false
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: returnByAlias
      object: _Filter
      type: !_InputBase
        input: Boolean
    - !_ObjectFor(_InputField)
      default: false
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: returnReferencedTypes
      object: _Filter
      type: !_InputBase
        input: Boolean
    - !_ObjectFor(_InputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: kinds
      object: _TypeFilter
      type: !_InputBase
        input: _TypeKind
    fields:
    - !_InputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: kinds
      type: !_InputBase
        input: _TypeKind
    name: _TypeFilter
    parent: !_InputBase
      input: _Filter
    typeKind: !_TypeKind Input
  !_Identifier _TypeInput: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _BaseType
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: parent
      object: _ChildType
      type: !_OutputBase
        output: _InputBase
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeParams
      object: _TypeObject
      type: !_OutputBase
        output: _InputTypeParam
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: fields
      object: _TypeObject
      type: !_OutputBase
        output: _InputField
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: alternates
      object: _TypeObject
      type: !_OutputBase
        output: _InputAlternate
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allFields
      object: _TypeObject
      type: !_OutputBase
        output: _ObjectFor
        typeArgs:
        - !_OutputArg
          typeParam: field
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allAlternates
      object: _TypeObject
      type: !_OutputBase
        output: _ObjectFor
        typeArgs:
        - !_OutputArg
          typeParam: alternate
    name: _TypeInput
    parent: !_OutputBase
      output: _TypeObject
      typeArgs:
      - !_OutputArg
        label: Input
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
      - !_OutputArg
        output: _InputBase
      - !_OutputArg
        output: _InputTypeParam
      - !_OutputArg
        output: _InputField
      - !_OutputArg
        output: _InputAlternate
    typeKind: !_TypeKind Output
  !_Identifier _TypeKind: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: _SimpleKind
      name: Basic
    - !_EnumLabel
      enum: _SimpleKind
      name: Enum
    - !_EnumLabel
      enum: _SimpleKind
      name: Internal
    - !_EnumLabel
      enum: _SimpleKind
      name: Domain
    - !_EnumLabel
      enum: _SimpleKind
      name: Union
    - !_EnumLabel
      enum: _TypeKind
      name: Dual
    - !_EnumLabel
      enum: _TypeKind
      name: Input
    - !_EnumLabel
      enum: _TypeKind
      name: Output
    items:
    - !_Aliased
      name: Dual
    - !_Aliased
      name: Input
    - !_Aliased
      name: Output
    name: _TypeKind
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: _SimpleKind
    typeKind: !_TypeKind Enum
  !_Identifier _TypeObject: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _BaseType
      type: !_OutputBase
        typeParam: kind
    - !_ObjectFor(_OutputField)
      name: parent
      object: _ChildType
      type: !_OutputBase
        typeParam: parent
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeParams
      object: _TypeObject
      type: !_OutputBase
        typeParam: typeParam
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: fields
      object: _TypeObject
      type: !_OutputBase
        typeParam: field
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: alternates
      object: _TypeObject
      type: !_OutputBase
        typeParam: alternate
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allFields
      object: _TypeObject
      type: !_OutputBase
        output: _ObjectFor
        typeArgs:
        - !_OutputArg
          typeParam: field
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allAlternates
      object: _TypeObject
      type: !_OutputBase
        output: _ObjectFor
        typeArgs:
        - !_OutputArg
          typeParam: alternate
    fields:
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeParams
      type: !_OutputBase
        typeParam: typeParam
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: fields
      type: !_OutputBase
        typeParam: field
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: alternates
      type: !_OutputBase
        typeParam: alternate
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allFields
      type: !_OutputBase
        output: _ObjectFor
        typeArgs:
        - !_OutputArg
          typeParam: field
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allAlternates
      type: !_OutputBase
        output: _ObjectFor
        typeArgs:
        - !_OutputArg
          typeParam: alternate
    name: _TypeObject
    parent: !_OutputBase
      output: _ChildType
      typeArgs:
      - !_OutputArg
        typeParam: kind
      - !_OutputArg
        typeParam: parent
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Domain
        typeName: _ObjectKind
      name: kind
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _ObjBase
      name: parent
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _ObjTypeParam
      name: typeParam
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _Field
      name: field
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: _Alternate
      name: alternate
  !_Identifier _TypeOutput: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _BaseType
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: parent
      object: _ChildType
      type: !_OutputBase
        output: _OutputBase
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: typeParams
      object: _TypeObject
      type: !_OutputBase
        output: _OutputTypeParam
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: fields
      object: _TypeObject
      type: !_OutputBase
        output: _OutputField
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: alternates
      object: _TypeObject
      type: !_OutputBase
        output: _OutputAlternate
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allFields
      object: _TypeObject
      type: !_OutputBase
        output: _ObjectFor
        typeArgs:
        - !_OutputArg
          typeParam: field
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allAlternates
      object: _TypeObject
      type: !_OutputBase
        output: _ObjectFor
        typeArgs:
        - !_OutputArg
          typeParam: alternate
    name: _TypeOutput
    parent: !_OutputBase
      output: _TypeObject
      typeArgs:
      - !_OutputArg
        label: Output
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
      - !_OutputArg
        output: _OutputBase
      - !_OutputArg
        output: _OutputTypeParam
      - !_OutputArg
        output: _OutputField
      - !_OutputArg
        output: _OutputAlternate
    typeKind: !_TypeKind Output
  !_Identifier _TypeParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeParam
      object: _TypeParam
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: typeParam
      type: !_OutputBase
        output: _Identifier
    name: _TypeParam
    parent: !_DualBase
      dual: _Described
    typeKind: !_TypeKind Output
  !_Identifier _TypeRef: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        typeParam: kind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: typeKind
      type: !_OutputBase
        typeParam: kind
    - !_OutputField
      name: typeName
      type: !_OutputBase
        output: _Identifier
    name: _TypeRef
    parent: !_DualBase
      dual: _Described
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: _TypeKind
      name: kind
  !_Identifier _TypeSimple: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _TypeSimple
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Basic
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_ObjectFor(_OutputAlternate)
      object: _TypeSimple
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_ObjectFor(_OutputAlternate)
      object: _TypeSimple
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Domain
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_ObjectFor(_OutputAlternate)
      object: _TypeSimple
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Union
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Basic
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Domain
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_OutputAlternate
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Union
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    name: _TypeSimple
    typeKind: !_TypeKind Output
  !_Identifier _TypeUnion: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualField)
      name: name
      object: _Named
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: aliases
      object: _Aliased
      type: !_DualBase
        dual: _Identifier
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _BaseType
      type: !_OutputBase
        output: _TypeKind
    - !_ObjectFor(_OutputField)
      name: parent
      object: _ChildType
      type: !_DualBase
        dual: _Named
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: items
      object: _ParentType
      type: !_OutputBase
        output: _UnionRef
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: allItems
      object: _ParentType
      type: !_OutputBase
        output: _UnionMember
    name: _TypeUnion
    parent: !_OutputBase
      output: _ParentType
      typeArgs:
      - !_OutputArg
        label: Union
        typeKind: !_SimpleKind Enum
        typeName: _TypeKind
      - !_OutputArg
        output: _UnionRef
      - !_OutputArg
        output: _UnionMember
    typeKind: !_TypeKind Output
  !_Identifier _UnionMember: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        output: _SimpleKind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    - !_ObjectFor(_OutputField)
      name: union
      object: _UnionMember
      type: !_OutputBase
        output: _Identifier
    fields:
    - !_OutputField
      name: union
      type: !_OutputBase
        output: _Identifier
    name: _UnionMember
    parent: !_OutputBase
      output: _UnionRef
    typeKind: !_TypeKind Output
  !_Identifier _UnionRef: !_TypeOutput
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: description
      object: _Described
      type: !_DualBase
        dual: String
    - !_ObjectFor(_OutputField)
      name: typeKind
      object: _TypeRef
      type: !_OutputBase
        output: _SimpleKind
    - !_ObjectFor(_OutputField)
      name: typeName
      object: _TypeRef
      type: !_OutputBase
        output: _Identifier
    name: _UnionRef
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
      - !_OutputArg
        output: _SimpleKind
    typeKind: !_TypeKind Output