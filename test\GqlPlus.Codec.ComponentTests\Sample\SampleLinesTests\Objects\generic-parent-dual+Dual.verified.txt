﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcPrntDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcPrntDualDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcPrntDualDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefGnrcPrntDualDual
        type: !_DualBase
          dual: AltGnrcPrntDualDual
    name: GnrcPrntDualDual
    parent: !_DualBase
      dual: RefGnrcPrntDualDual
      typeArgs:
        - !_DualArg
          dual: AltGnrcPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier RefGnrcPrntDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefGnrcPrntDualDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefGnrcPrntDualDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref