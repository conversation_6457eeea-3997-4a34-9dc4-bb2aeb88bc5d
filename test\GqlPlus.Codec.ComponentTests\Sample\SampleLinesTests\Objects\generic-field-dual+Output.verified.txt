﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcFieldDualOutp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcFieldDualOutp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcFieldDualOutp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcFieldDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier GnrcFieldDualOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: GnrcFieldDualOutp
        type: !_OutputBase
          output: RefGnrcFieldDualOutp
          typeArgs:
            - !_DualArg
              dual: AltGnrcFieldDualOutp
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          output: RefGnrcFieldDualOutp
          typeArgs:
            - !_DualArg
              dual: AltGnrcFieldDualOutp
    name: GnrcFieldDualOutp
    typeKind: !_TypeKind Output
  !_Identifier RefGnrcFieldDualOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcFieldDualOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcFieldDualOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref