﻿!_Schema
types: !_Map_Type
  !_Identifier RefGnrcAltModStrDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        collections:
          - !_ModifierDictionary
            by: '*'
            modifierKind: !_ModifierKind Dict
            typeKind: !_SimpleKind Basic
        object: RefGnrcAltModStrDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        collections:
          - !_ModifierDictionary
            by: '*'
            modifierKind: !_ModifierKind Dict
            typeKind: !_SimpleKind Basic
        type: !_DualBase
          typeParam: ref
    name: RefGnrcAltModStrDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref