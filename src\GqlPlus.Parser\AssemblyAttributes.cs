﻿using System.Runtime.CompilerServices;

[assembly: InternalsVisibleTo("GqlPlus.Codec.ComponentTests")]
[assembly: InternalsVisibleTo("GqlPlus.ComponentTestBase")]
[assembly: InternalsVisibleTo("GqlPlus.Parser.ClassTests")]
[assembly: InternalsVisibleTo("GqlPlus.Parser.ComponentTests")]
[assembly: InternalsVisibleTo("GqlPlus.Modeller.ComponentTests")]
[assembly: InternalsVisibleTo("GqlPlus.TestBase")]
[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2")]
