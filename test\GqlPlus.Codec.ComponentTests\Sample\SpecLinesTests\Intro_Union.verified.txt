﻿!_Schema
errors:
  - !_Error
    _kind: !_TokenKind End
    _message: In _TypeUnion can't get model for type '_ParentType'
  - !_Error
    _kind: !_TokenKind End
    _message: In _UnionRef can't get model for type '_TypeRef'
types: !_Map_Type
  !_Identifier _TypeUnion: !_TypeOutput
    name: _TypeUnion
    parent: !_OutputBase
      output: _ParentType
      typeArgs:
        - !_OutputArg
          label: Union
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
        - !_OutputArg
          output: _UnionRef
        - !_OutputArg
          output: _UnionMember
    typeKind: !_TypeKind Output
  !_Identifier _UnionMember: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: union
        object: _UnionMember
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: union
        type: !_OutputBase
          output: _Identifier
    name: _UnionMember
    parent: !_OutputBase
      output: _UnionRef
    typeKind: !_TypeKind Output
  !_Identifier _UnionRef: !_TypeOutput
    name: _UnionRef
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
        - !_OutputArg
          output: _SimpleKind
    typeKind: !_TypeKind Output