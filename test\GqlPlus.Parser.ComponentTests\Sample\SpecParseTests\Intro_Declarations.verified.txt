﻿!Sc I@001/0001
Success
{
  !Ou I@008/0001
  _Schema
  :
  I@007/0002
  _Named
  {
    !OF I@009/0003
    categories
    (
      !Pa
      I@020/0003
      _CategoryFilter
      ?
    )
    :
    I@039/0003
    _Categories
    [_Identifier]
    !OF I@009/0004
    directives
    (
      !Pa
      I@020/0004
      _Filter
      ?
    )
    :
    I@031/0004
    _Directives
    [_Identifier]
    !OF I@009/0005
    types
    (
      !Pa
      I@015/0005
      _TypeFilter
      ?
    )
    :
    I@030/0005
    _Type
    [_Identifier]
    !OF I@009/0006
    settings
    (
      !Pa
      I@018/0006
      _Filter
      ?
    )
    :
    I@029/0006
    _Setting
    [_Identifier]
  }
}
{
  !Do I@008/0009
  _Identifier
  String
  !DX R@029/0009
  /[A-Za-z_]+/
}
{
  !In I@007/0011
  _Filter
  {
    !IF I@009/0012
    names
    :
    I@016/0012
    _NameFilter
    []
    !IF I@009/0013
    matchAliases
    :
    I@023/0013
    Boolean
    ?
    =( !k I@034/0013 Boolean.true )
    !IF I@009/0014
    aliases
    :
    I@018/0014
    _NameFilter
    []
    !IF I@009/0015
    returnByAlias
    :
    I@024/0015
    Boolean
    ?
    =( !k I@035/0015 Boolean.false )
    !IF I@009/0016
    returnReferencedTypes
    :
    I@032/0016
    Boolean
    ?
    =( !k I@043/0016 Boolean.false )
  }
  |
  I@007/0017
  _NameFilter
  []
}
{
  '_NameFilter is a simple match expression against _Identifier where \'.\' matches any single character and \'*\' matches zero or more of any character.'
  !Do I@008/0022
  _NameFilter
  String
  !DX R@029/0022
  /[A-Za-z_.*]+/
}
{
  !In I@007/0024
  _CategoryFilter
  :
  I@007/0025
  _Filter
  {
    !IF I@009/0026
    resolutions
    :
    I@022/0026
    _Resolution
    []
  }
}
{
  !In I@007/0029
  _TypeFilter
  :
  I@007/0030
  _Filter
  {
    !IF I@009/0031
    kinds
    :
    I@016/0031
    _TypeKind
    []
  }
}