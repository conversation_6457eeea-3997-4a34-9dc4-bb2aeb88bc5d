﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcPrntDualPrntDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcPrntDualPrntDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcPrntDualPrntDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntDualPrntDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcPrntDualPrntDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcPrntDualPrntDual
        type: !_DualBase
          dual: Number
    name: GnrcPrntDualPrntDual
    parent: !_DualBase
      dual: RefGnrcPrntDualPrntDual
      typeArgs:
        - !_DualArg
          dual: AltGnrcPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier RefGnrcPrntDualPrntDual: !_TypeDual
    name: RefGnrcPrntDualPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref