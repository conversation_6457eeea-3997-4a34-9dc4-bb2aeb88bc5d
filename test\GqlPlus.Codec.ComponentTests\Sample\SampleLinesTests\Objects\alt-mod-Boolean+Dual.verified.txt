﻿!_Schema
types: !_Map_Type
  !_Identifier AltAltModBoolDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltAltModBoolDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltAltModBoolDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltAltModBoolDual
    typeKind: !_TypeKind Dual
  !_Identifier AltModBoolDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        collections:
          - !_ModifierDictionary
            by: ^
            modifierKind: !_ModifierKind Dict
            typeKind: !_SimpleKind Basic
        object: AltModBoolDual
        type: !_DualBase
          dual: AltAltModBoolDual
    alternates:
      - !_DualAlternate
        collections:
          - !_ModifierDictionary
            by: ^
            modifierKind: !_ModifierKind Dict
            typeKind: !_SimpleKind Basic
        type: !_DualBase
          dual: AltAltModBoolDual
    name: AltModBoolDual
    typeKind: !_TypeKind Dual