﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcAltDualInp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcAltDualInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcAltDualInp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcAltDualInp
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltDualInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: GnrcAltDualInp
        type: !_InputBase
          input: RefGnrcAltDualInp
          typeArgs:
            - !_DualArg
              dual: AltGnrcAltDualInp
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: RefGnrcAltDualInp
          typeArgs:
            - !_DualArg
              dual: AltGnrcAltDualInp
    name: GnrcAltDualInp
    typeKind: !_TypeKind Input
  !_Identifier RefGnrcAltDualInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefGnrcAltDualInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefGnrcAltDualInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref