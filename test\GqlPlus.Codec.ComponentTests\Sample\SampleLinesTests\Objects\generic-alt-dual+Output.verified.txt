﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcAltDualOutp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcAltDualOutp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcAltDualOutp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcAltDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltDualOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: GnrcAltDualOutp
        type: !_OutputBase
          output: RefGnrcAltDualOutp
          typeArgs:
            - !_DualArg
              dual: AltGnrcAltDualOutp
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: RefGnrcAltDualOutp
          typeArgs:
            - !_DualArg
              dual: AltGnrcAltDualOutp
    name: GnrcAltDualOutp
    typeKind: !_TypeKind Output
  !_Identifier RefGnrcAltDualOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcAltDualOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcAltDualOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref