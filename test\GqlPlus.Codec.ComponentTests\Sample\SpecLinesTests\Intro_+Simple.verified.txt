﻿!_Schema
errors:
  - !_Error
    _kind: !_TokenKind End
    _message: In _DomainRef can't get model for type '_TypeRef'
  - !_Error
    _kind: !_TokenKind End
    _message: In _BaseDomain can't get model for type '_ParentType'
  - !_Error
    _kind: !_TokenKind End
    _message: In _BaseDomainItem can't get model for type '_Described'
  - !_Error
    _kind: !_TokenKind End
    _message: In _TypeEnum can't get model for type '_ParentType'
  - !_Error
    _kind: !_TokenKind End
    _message: In _EnumLabel can't get model for type '_Aliased'
  - !_Error
    _kind: !_TokenKind End
    _message: In _EnumValue can't get model for type '_TypeRef'
  - !_Error
    _kind: !_TokenKind End
    _message: In _TypeUnion can't get model for type '_ParentType'
  - !_Error
    _kind: !_TokenKind End
    _message: In _UnionR<PERSON> can't get model for type '_TypeRef'
types: !_Map_Type
  !_Identifier _BaseDomain: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: domainKind
        object: _BaseDomain
        type: !_OutputBase
          typeParam: domain
    fields:
      - !_OutputField
        name: domainKind
        type: !_OutputBase
          typeParam: domain
    name: _BaseDomain
    parent: !_OutputBase
      output: _ParentType
      typeArgs:
        - !_OutputArg
          label: Domain
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
        - !_OutputArg
          typeParam: item
        - !_OutputArg
          typeParam: domainItem
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: _DomainKind
        name: domain
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Dual
          typeName: _BaseDomainItem
        name: item
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _DomainItem
        name: domainItem
  !_Identifier _BaseDomainItem: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: exclude
        object: _BaseDomainItem
        type: !_DualBase
          dual: Boolean
    fields:
      - !_DualField
        name: exclude
        type: !_DualBase
          dual: Boolean
    name: _BaseDomainItem
    parent: !_DualBase
      dual: _Described
    typeKind: !_TypeKind Dual
  !_Identifier _BasicValue: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _BasicValue
        type: !_OutputBase
          output: Boolean
      - !_ObjectFor(_OutputAlternate)
        object: _BasicValue
        type: !_OutputBase
          output: _EnumValue
      - !_ObjectFor(_OutputAlternate)
        object: _BasicValue
        type: !_OutputBase
          output: Number
      - !_ObjectFor(_OutputAlternate)
        object: _BasicValue
        type: !_OutputBase
          output: String
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: Boolean
      - !_OutputAlternate
        type: !_OutputBase
          output: _EnumValue
      - !_OutputAlternate
        type: !_OutputBase
          output: Number
      - !_OutputAlternate
        type: !_OutputBase
          output: String
    name: _BasicValue
    typeKind: !_TypeKind Output
  !_Identifier _DomainItem: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: domain
        object: _DomainItem
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: domain
        type: !_OutputBase
          output: _Identifier
    name: _DomainItem
    parent: !_OutputBase
      typeParam: item
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Dual
          typeName: _BaseDomainItem
        name: item
  !_Identifier _DomainItemLabel: !_TypeOutput
    allFields:
      - !_ObjectFor(_DualField)
        name: exclude
        object: _BaseDomainItem
        type: !_DualBase
          dual: Boolean
      - !_ObjectFor(_OutputField)
        name: label
        object: _DomainLabel
        type: !_OutputBase
          output: _EnumValue
      - !_ObjectFor(_OutputField)
        name: domain
        object: _DomainItem
        type: !_OutputBase
          output: _Identifier
    name: _DomainItemLabel
    parent: !_OutputBase
      output: _DomainItem
      typeArgs:
        - !_OutputArg
          output: _DomainLabel
    typeKind: !_TypeKind Output
  !_Identifier _DomainItemRange: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: domain
        object: _DomainItem
        type: !_OutputBase
          output: _Identifier
    name: _DomainItemRange
    parent: !_OutputBase
      output: _DomainItem
      typeArgs:
        - !_DualArg
          dual: _DomainRange
    typeKind: !_TypeKind Output
  !_Identifier _DomainItemRegex: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: domain
        object: _DomainItem
        type: !_OutputBase
          output: _Identifier
    name: _DomainItemRegex
    parent: !_OutputBase
      output: _DomainItem
      typeArgs:
        - !_DualArg
          dual: _DomainRegex
    typeKind: !_TypeKind Output
  !_Identifier _DomainItemTrueFalse: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: domain
        object: _DomainItem
        type: !_OutputBase
          output: _Identifier
    name: _DomainItemTrueFalse
    parent: !_OutputBase
      output: _DomainItem
      typeArgs:
        - !_DualArg
          dual: _DomainTrueFalse
    typeKind: !_TypeKind Output
  !_Identifier _DomainKind: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: _DomainKind
        name: Boolean
      - !_EnumLabel
        enum: _DomainKind
        name: Enum
      - !_EnumLabel
        enum: _DomainKind
        name: Number
      - !_EnumLabel
        enum: _DomainKind
        name: String
    items:
      - !_Aliased
        name: Boolean
      - !_Aliased
        name: Enum
      - !_Aliased
        name: Number
      - !_Aliased
        name: String
    name: _DomainKind
    typeKind: !_TypeKind Enum
  !_Identifier _DomainLabel: !_TypeOutput
    allFields:
      - !_ObjectFor(_DualField)
        name: exclude
        object: _BaseDomainItem
        type: !_DualBase
          dual: Boolean
      - !_ObjectFor(_OutputField)
        name: label
        object: _DomainLabel
        type: !_OutputBase
          output: _EnumValue
    fields:
      - !_OutputField
        name: label
        type: !_OutputBase
          output: _EnumValue
    name: _DomainLabel
    parent: !_DualBase
      dual: _BaseDomainItem
    typeKind: !_TypeKind Output
  !_Identifier _DomainRange: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: exclude
        object: _BaseDomainItem
        type: !_DualBase
          dual: Boolean
      - !_ObjectFor(_DualField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: lower
        object: _DomainRange
        type: !_DualBase
          dual: Number
      - !_ObjectFor(_DualField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: upper
        object: _DomainRange
        type: !_DualBase
          dual: Number
    fields:
      - !_DualField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: lower
        type: !_DualBase
          dual: Number
      - !_DualField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: upper
        type: !_DualBase
          dual: Number
    name: _DomainRange
    parent: !_DualBase
      dual: _BaseDomainItem
    typeKind: !_TypeKind Dual
  !_Identifier _DomainRef: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: domainKind
        object: _DomainRef
        type: !_OutputBase
          typeParam: kind
    fields:
      - !_OutputField
        name: domainKind
        type: !_OutputBase
          typeParam: kind
    name: _DomainRef
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
        - !_OutputArg
          label: Domain
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: _DomainKind
        name: kind
  !_Identifier _DomainRegex: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: exclude
        object: _BaseDomainItem
        type: !_DualBase
          dual: Boolean
      - !_ObjectFor(_DualField)
        name: pattern
        object: _DomainRegex
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: pattern
        type: !_DualBase
          dual: String
    name: _DomainRegex
    parent: !_DualBase
      dual: _BaseDomainItem
    typeKind: !_TypeKind Dual
  !_Identifier _DomainTrueFalse: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: exclude
        object: _BaseDomainItem
        type: !_DualBase
          dual: Boolean
      - !_ObjectFor(_DualField)
        name: value
        object: _DomainTrueFalse
        type: !_DualBase
          dual: Boolean
    fields:
      - !_DualField
        name: value
        type: !_DualBase
          dual: Boolean
    name: _DomainTrueFalse
    parent: !_DualBase
      dual: _BaseDomainItem
    typeKind: !_TypeKind Dual
  !_Identifier _DomainValue: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: domainKind
        object: _DomainRef
        type: !_OutputBase
          typeParam: kind
      - !_ObjectFor(_OutputField)
        name: value
        object: _DomainValue
        type: !_OutputBase
          typeParam: value
    fields:
      - !_OutputField
        name: value
        type: !_OutputBase
          typeParam: value
    name: _DomainValue
    parent: !_OutputBase
      output: _DomainRef
      typeArgs:
        - !_OutputArg
          typeParam: kind
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: _DomainKind
        name: kind
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Output
          typeName: _BasicValue
        name: value
  !_Identifier _EnumLabel: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: enum
        object: _EnumLabel
        type: !_DualBase
          dual: _Identifier
    fields:
      - !_DualField
        name: enum
        type: !_DualBase
          dual: _Identifier
    name: _EnumLabel
    parent: !_DualBase
      dual: _Aliased
    typeKind: !_TypeKind Dual
  !_Identifier _EnumValue: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: label
        object: _EnumValue
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: label
        type: !_OutputBase
          output: _Identifier
    name: _EnumValue
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _TypeDomain: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _TypeDomain
        type: !_OutputBase
          output: _BaseDomain
          typeArgs:
            - !_OutputArg
              label: Boolean
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_DualArg
              dual: _DomainTrueFalse
            - !_OutputArg
              output: _DomainItemTrueFalse
      - !_ObjectFor(_OutputAlternate)
        object: _TypeDomain
        type: !_OutputBase
          output: _BaseDomain
          typeArgs:
            - !_OutputArg
              label: Enum
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_OutputArg
              output: _DomainLabel
            - !_OutputArg
              output: _DomainItemLabel
      - !_ObjectFor(_OutputAlternate)
        object: _TypeDomain
        type: !_OutputBase
          output: _BaseDomain
          typeArgs:
            - !_OutputArg
              label: Number
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_DualArg
              dual: _DomainRange
            - !_OutputArg
              output: _DomainItemRange
      - !_ObjectFor(_OutputAlternate)
        object: _TypeDomain
        type: !_OutputBase
          output: _BaseDomain
          typeArgs:
            - !_OutputArg
              label: String
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_DualArg
              dual: _DomainRegex
            - !_OutputArg
              output: _DomainItemRegex
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _BaseDomain
          typeArgs:
            - !_OutputArg
              label: Boolean
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_DualArg
              dual: _DomainTrueFalse
            - !_OutputArg
              output: _DomainItemTrueFalse
      - !_OutputAlternate
        type: !_OutputBase
          output: _BaseDomain
          typeArgs:
            - !_OutputArg
              label: Enum
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_OutputArg
              output: _DomainLabel
            - !_OutputArg
              output: _DomainItemLabel
      - !_OutputAlternate
        type: !_OutputBase
          output: _BaseDomain
          typeArgs:
            - !_OutputArg
              label: Number
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_DualArg
              dual: _DomainRange
            - !_OutputArg
              output: _DomainItemRange
      - !_OutputAlternate
        type: !_OutputBase
          output: _BaseDomain
          typeArgs:
            - !_OutputArg
              label: String
              typeKind: !_SimpleKind Enum
              typeName: _DomainKind
            - !_DualArg
              dual: _DomainRegex
            - !_OutputArg
              output: _DomainItemRegex
    name: _TypeDomain
    typeKind: !_TypeKind Output
  !_Identifier _TypeEnum: !_TypeOutput
    name: _TypeEnum
    parent: !_OutputBase
      output: _ParentType
      typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
        - !_OutputArg
          output: _Aliased
        - !_DualArg
          dual: _EnumLabel
    typeKind: !_TypeKind Output
  !_Identifier _TypeUnion: !_TypeOutput
    name: _TypeUnion
    parent: !_OutputBase
      output: _ParentType
      typeArgs:
        - !_OutputArg
          label: Union
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
        - !_OutputArg
          output: _UnionRef
        - !_OutputArg
          output: _UnionMember
    typeKind: !_TypeKind Output
  !_Identifier _UnionMember: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: union
        object: _UnionMember
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: union
        type: !_OutputBase
          output: _Identifier
    name: _UnionMember
    parent: !_OutputBase
      output: _UnionRef
    typeKind: !_TypeKind Output
  !_Identifier _UnionRef: !_TypeOutput
    name: _UnionRef
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
        - !_OutputArg
          output: _SimpleKind
    typeKind: !_TypeKind Output