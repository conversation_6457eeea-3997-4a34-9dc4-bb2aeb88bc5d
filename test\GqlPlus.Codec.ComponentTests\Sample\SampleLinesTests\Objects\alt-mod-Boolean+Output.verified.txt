﻿!_Schema
types: !_Map_Type
  !_Identifier AltAltModBoolOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: AltAltModBoolOutp
        type: !_OutputBase
          output: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: alt
        object: AltAltModBoolOutp
        type: !_OutputBase
          output: Number
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: String
    fields:
      - !_OutputField
        name: alt
        type: !_OutputBase
          output: Number
    name: AltAltModBoolOutp
    typeKind: !_TypeKind Output
  !_Identifier AltModBoolOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        collections:
          - !_ModifierDictionary
            by: ^
            modifierKind: !_ModifierKind Dict
            typeKind: !_SimpleKind Basic
        object: AltModBoolOutp
        type: !_OutputBase
          output: AltAltModBoolOutp
    alternates:
      - !_OutputAlternate
        collections:
          - !_ModifierDictionary
            by: ^
            modifierKind: !_ModifierKind Dict
            typeKind: !_SimpleKind Basic
        type: !_OutputBase
          output: AltAltModBoolOutp
    name: AltModBoolOutp
    typeKind: !_TypeKind Output