﻿!_Schema
types: !_Map_Type
  !_Identifier PrntUnionPrnt: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrnt
    items:
      - !_Aliased
        name: Number
    name: PrntUnionPrnt
    typeKind: !_TypeKind Union
  !_Identifier UnionPrnt: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrnt
      - !_UnionMember
        name: String
        union: UnionPrnt
    items:
      - !_Aliased
        name: String
    name: UnionPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Union
      typeName: PrntUnionPrnt
    typeKind: !_TypeKind Union