﻿!_Schema
types: !_Map_Type
  !_Identifier DomGnrcPrntStrDomDual: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: DomGnrcPrntStrDomDual
        exclude: false
        pattern: \w+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: \w+
    name: DomGnrcPrntStrDomDual
    typeKind: !_TypeKind Domain
  !_Identifier FieldGnrcPrntStrDomDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FieldGnrcPrntStrDomDual
        type: !_DualBase
          typeParam: ref
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          typeParam: ref
    name: FieldGnrcPrntStrDomDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref
  !_Identifier GnrcPrntStrDomDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FieldGnrcPrntStrDomDual
        type: !_DualBase
          dual: DomGnrcPrntStrDomDual
    name: GnrcPrntStrDomDual
    parent: !_DualBase
      dual: FieldGnrcPrntStrDomDual
      typeArgs:
        - !_DualArg
          dual: DomGnrcPrntStrDomDual
    typeKind: !_TypeKind Dual