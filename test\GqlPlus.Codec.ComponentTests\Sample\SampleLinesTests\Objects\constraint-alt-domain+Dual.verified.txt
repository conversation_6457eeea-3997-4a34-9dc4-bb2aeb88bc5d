﻿!_Schema
types: !_Map_Type
  !_Identifier CnstAltDmnDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: CnstAltDmnDual
        type: !_DualBase
          dual: RefCnstAltDmnDual
          typeArgs:
            - !_DualArg
              dual: DomCnstAltDmnDual
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: RefCnstAltDmnDual
          typeArgs:
            - !_DualArg
              dual: DomCnstAltDmnDual
    name: CnstAltDmnDual
    typeKind: !_TypeKind Dual
  !_Identifier DomCnstAltDmnDual: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: DomCnstAltDmnDual
        exclude: false
        pattern: \w+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: \w+
    name: DomCnstAltDmnDual
    typeKind: !_TypeKind Domain
  !_Identifier RefCnstAltDmnDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefCnstAltDmnDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefCnstAltDmnDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref