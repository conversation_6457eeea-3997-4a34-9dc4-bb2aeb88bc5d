﻿!_Schema
types: !_Map_Type
  !_Identifier ObjParamCnstInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: test
        object: ObjParamCnstInp
        type: !_InputBase
          typeParam: test
      - !_ObjectFor(_InputField)
        name: type
        object: ObjParamCnstInp
        type: !_InputBase
          typeParam: test
    fields:
      - !_InputField
        name: test
        type: !_InputBase
          typeParam: test
      - !_InputField
        name: type
        type: !_InputBase
          typeParam: test
    name: ObjParamCnstInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: test