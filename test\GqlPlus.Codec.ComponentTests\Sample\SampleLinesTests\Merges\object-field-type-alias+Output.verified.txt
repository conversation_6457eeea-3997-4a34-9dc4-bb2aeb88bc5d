﻿!_Schema
types: !_Map_Type
  !_Identifier ObjFieldTypeAliasOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: ObjFieldTypeAliasOutp
        type: !_OutputBase
          output: String
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          output: String
    name: ObjFieldTypeAliasOutp
    typeKind: !_TypeKind Output