﻿!_Schema
types: !_Map_Type
  !_Identifier FieldDescrInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        description: 'Test Descr'
        name: field
        object: FieldDescrInp
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        description: 'Test Descr'
        name: field
        type: !_InputBase
          input: String
    name: FieldDescrInp
    typeKind: !_TypeKind Input