﻿!_Schema
types: !_Map_Type
  !_Identifier PrntParamDiffOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefPrntParamDiffOutp
        type: !_OutputBase
          typeParam: b
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: PrntParamDiffOutp
        type: !_OutputBase
          typeParam: a
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: a
    name: PrntParamDiffOutp
    parent: !_OutputBase
      output: RefPrntParamDiffOutp
      typeArgs:
        - !_OutputArg
          typeParam: a
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: a
  !_Identifier RefPrntParamDiffOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefPrntParamDiffOutp
        type: !_OutputBase
          typeParam: b
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: b
    name: RefPrntParamDiffOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: b