﻿!_Schema
types: !_Map_Type
  !_Identifier FieldTypeDescrDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FieldTypeDescrDual
        type: !_DualBase
          description: 'Test Descr'
          dual: Number
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          description: 'Test Descr'
          dual: Number
    name: FieldTypeDescrDual
    typeKind: !_TypeKind Dual