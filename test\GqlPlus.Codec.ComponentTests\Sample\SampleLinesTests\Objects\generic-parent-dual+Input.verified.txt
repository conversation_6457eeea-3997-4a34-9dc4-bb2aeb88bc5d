﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcPrntDualInp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcPrntDualInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcPrntDualInp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcPrntDualInp
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntDualInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefGnrcPrntDualInp
        type: !_DualBase
          dual: AltGnrcPrntDualInp
    name: GnrcPrntDualInp
    parent: !_InputBase
      input: RefGnrcPrntDualInp
      typeArgs:
        - !_DualArg
          dual: AltGnrcPrntDualInp
    typeKind: !_TypeKind Input
  !_Identifier RefGnrcPrntDualInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefGnrcPrntDualInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefGnrcPrntDualInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref