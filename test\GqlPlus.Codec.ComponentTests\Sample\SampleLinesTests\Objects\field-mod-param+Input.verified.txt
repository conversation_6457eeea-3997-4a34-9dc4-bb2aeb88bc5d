﻿!_Schema
types: !_Map_Type
  !_Identifier FieldModParamInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        modifiers: [!_ModifierTypeParam{by:mod,modifierKind:!_ModifierKind Param}]
        name: field
        object: FieldModParamInp
        type: !_InputBase
          input: FldFieldModParamInp
    fields:
      - !_InputField
        modifiers: [!_ModifierTypeParam{by:mod,modifierKind:!_ModifierKind Param}]
        name: field
        type: !_InputBase
          input: FldFieldModParamInp
    name: FieldModParamInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: mod
  !_Identifier FldFieldModParamInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: FldFieldModParamInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: FldFieldModParamInp
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          input: Number
    name: FldFieldModParamInp
    typeKind: !_TypeKind Input