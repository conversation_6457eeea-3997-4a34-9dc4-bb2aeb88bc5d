﻿!_Schema
types: !_Map_Type
  !_Identifier EnumOutpCnstDomEnum: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumOutpCnstDomEnum
        name: outpCnstDomEnum
      - !_EnumLabel
        enum: EnumOutpCnstDomEnum
        name: other
    items:
      - !_Aliased
        name: outpCnstDomEnum
      - !_Aliased
        name: other
    name: EnumOutpCnstDomEnum
    typeKind: !_TypeKind Enum
  !_Identifier JustOutpCnstDomEnum: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: JustOutpCnstDomEnum
        exclude: false
        value: !_EnumValue
          label: outpCnstDomEnum
          typeKind: !_SimpleKind Enum
          typeName: EnumOutpCnstDomEnum
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: outpCnstDomEnum
          typeKind: !_SimpleKind Enum
          typeName: EnumOutpCnstDomEnum
    name: JustOutpCnstDomEnum
    typeKind: !_TypeKind Domain
  !_Identifier OutpCnstDomEnum: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: OutpCnstDomEnum
        type: !_OutputBase
          output: RefOutpCnstDomEnum
          typeArgs:
            - !_OutputArg
              output: outpCnstDomEnum
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: RefOutpCnstDomEnum
          typeArgs:
            - !_OutputArg
              output: outpCnstDomEnum
    name: OutpCnstDomEnum
    typeKind: !_TypeKind Output
  !_Identifier RefOutpCnstDomEnum: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: RefOutpCnstDomEnum
        type: !_OutputBase
          typeParam: type
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: type
    name: RefOutpCnstDomEnum
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Domain
          typeName: JustOutpCnstDomEnum
        name: type