﻿!_Schema
types: !_Map_Type
  !_Identifier FldObjFieldAliasInp: !_TypeInput
    name: FldObjFieldAliasInp
    typeKind: !_TypeKind Input
  !_Identifier ObjFieldAliasInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        aliases: [field1,field2]
        name: field
        object: ObjFieldAliasInp
        type: !_InputBase
          input: FldObjFieldAliasInp
    fields:
      - !_InputField
        aliases: [field1,field2]
        name: field
        type: !_InputBase
          input: FldObjFieldAliasInp
    name: ObjFieldAliasInp
    typeKind: !_TypeKind Input