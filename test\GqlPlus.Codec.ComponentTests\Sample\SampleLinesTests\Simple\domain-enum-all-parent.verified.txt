﻿!_Schema
types: !_Map_Type
  !_Identifier DmnEnumAllPrnt: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumAllPrnt
        exclude: false
        value: !_EnumValue
          label: '*'
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumAllPrnt
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: '*'
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumAllPrnt
    name: DmnEnumAllPrnt
    typeKind: !_TypeKind Domain
  !_Identifier EnumDmnEnumAllPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntDmnEnumAllPrnt
        name: prnt_dmnEnumAllPrnt
      - !_EnumLabel
        enum: EnumDmnEnumAllPrnt
        name: dmnEnumAllPrnt
    items:
      - !_Aliased
        name: dmnEnumAllPrnt
    name: EnumDmnEnumAllPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntDmnEnumAllPrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntDmnEnumAllPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntDmnEnumAllPrnt
        name: prnt_dmnEnumAllPrnt
    items:
      - !_Aliased
        name: prnt_dmnEnumAllPrnt
    name: PrntDmnEnumAllPrnt
    typeKind: !_TypeKind Enum