﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcAltArgDescrOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: GnrcAltArgDescrOutp
        type: !_OutputBase
          output: RefGnrcAltArgDescrOutp
          typeArgs:
            - !_OutputArg
              description: 'Test Descr'
              typeParam: type
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: RefGnrcAltArgDescrOutp
          typeArgs:
            - !_OutputArg
              description: 'Test Descr'
              typeParam: type
    name: GnrcAltArgDescrOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type
  !_Identifier RefGnrcAltArgDescrOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcAltArgDescrOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcAltArgDescrOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref