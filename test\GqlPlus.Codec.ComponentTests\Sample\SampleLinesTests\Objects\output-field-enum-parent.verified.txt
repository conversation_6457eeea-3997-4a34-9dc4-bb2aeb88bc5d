﻿!_Schema
types: !_Map_Type
  !_Identifier EnumOutpFieldEnumPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntOutpFieldEnumPrnt
        name: prnt_outpFieldEnumPrnt
      - !_EnumLabel
        enum: EnumOutpFieldEnumPrnt
        name: outpFieldEnumPrnt
    items:
      - !_Aliased
        name: outpFieldEnumPrnt
    name: EnumOutpFieldEnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntOutpFieldEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier OutpFieldEnumPrnt: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        field: field
        label: prnt_outpFieldEnumPrnt
        object: OutpFieldEnumPrnt
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpFieldEnumPrnt
    fields:
      - !_OutputEnum
        field: field
        label: prnt_outpFieldEnumPrnt
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpFieldEnumPrnt
    name: OutpFieldEnumPrnt
    typeKind: !_TypeKind Output
  !_Identifier PrntOutpFieldEnumPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntOutpFieldEnumPrnt
        name: prnt_outpFieldEnumPrnt
    items:
      - !_Aliased
        name: prnt_outpFieldEnumPrnt
    name: PrntOutpFieldEnumPrnt
    typeKind: !_TypeKind Enum