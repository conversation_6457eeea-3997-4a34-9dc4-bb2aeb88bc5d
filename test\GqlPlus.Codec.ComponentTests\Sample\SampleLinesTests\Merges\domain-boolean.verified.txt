﻿!_Schema
types: !_Map_Type
  !_Identifier DmnBool: !_DomainBoolean
    allItems:
      - !_DomainItem(_DomainTrueFalse)
        domain: DmnBool
        exclude: false
        value: false
      - !_DomainItem(_DomainTrueFalse)
        domain: DmnBool
        exclude: false
        value: true
    domainKind: !_DomainKind Boolean
    items:
      - !_DomainTrueFalse
        exclude: false
        value: false
      - !_DomainTrueFalse
        exclude: false
        value: true
    name: DmnBool
    typeKind: !_TypeKind Domain