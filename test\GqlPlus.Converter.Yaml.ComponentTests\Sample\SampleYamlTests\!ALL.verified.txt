﻿!_Schema
aliases: [Alias, Opt1, Opt2]
categories: !_Map_Categories
  !_Identifier ctgr: !_Category
    name: ctgr
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: Ctgr
    resolution: !_Resolution Parallel
  !_Identifier ctgrAlias: !_Category
    aliases: [CatA1, CatA2]
    name: ctgrAlias
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrAlias
    resolution: !_Resolution Parallel
  !_Identifier ctgrDescr: !_Category
    description: 'First category Second category'
    name: ctgrDescr
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrDescr
    resolution: !_Resolution Parallel
  !_Identifier ctgrDscrs: !_Category
    description: 'A Category described'
    name: ctgrDscrs
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrDscrs
    resolution: !_Resolution Parallel
  !_Identifier ctgrMod: !_Category
    aliases: [CatM1, CatM2]
    modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
    name: ctgrMod
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrMod
    resolution: !_Resolution Parallel
  !_Identifier ctgrOutp: !_Category
    name: ctgrOutp
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrOutp
    resolution: !_Resolution Parallel
  !_Identifier ctgrOutpDescr: !_Category
    name: ctgrOutpDescr
    output: !_TypeRef(_TypeKind)
      description: 'Output comment'
      typeKind: !_TypeKind Output
      typeName: CtgrOutpDescr
    resolution: !_Resolution Parallel
  !_Identifier ctgrOutpDict: !_Category
    modifiers: [!_ModifierDictionary {by: '*', modifierKind: !_ModifierKind Dict,
        typeKind: !_SimpleKind Basic}]
    name: ctgrOutpDict
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrOutpDict
    resolution: !_Resolution Parallel
  !_Identifier ctgrOutpList: !_Category
    modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
    name: ctgrOutpList
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrOutpList
    resolution: !_Resolution Parallel
  !_Identifier ctgrOutpOptl: !_Category
    modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
    name: ctgrOutpOptl
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: CtgrOutpOptl
    resolution: !_Resolution Parallel
  !_Identifier descrBtwn: !_Category
    name: descrBtwn
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: DescrBtwn
    resolution: !_Resolution Parallel
directives: !_Map_Directives
  !_Identifier Drct: !_Directive
    locations: !_Set(_Location) {Inline: _, Spread: _}
    name: Drct
    repeatable: false
  !_Identifier DrctAlias: !_Directive
    aliases: [DirA1, DirA2]
    locations: !_Set(_Location) {Field: _, Variable: _}
    name: DrctAlias
    repeatable: false
  !_Identifier DrctDescr: !_Directive
    description: 'A directive described'
    locations: !_Set(_Location) {Field: _, Fragment: _, Inline: _,
      Operation: _, Spread: _, Variable: _}
    name: DrctDescr
    repeatable: false
  !_Identifier DrctNoParam: !_Directive
    locations: !_Set(_Location) {Field: _, Fragment: _, Inline: _,
      Operation: _, Spread: _, Variable: _}
    name: DrctNoParam
    repeatable: false
  !_Identifier DrctParam: !_Directive
    locations: !_Set(_Location) {Fragment: _, Operation: _}
    name: DrctParam
    parameters:
    - !_InputParam
      input: InDrctParam
    repeatable: false
  !_Identifier DrctParamDict: !_Directive
    locations: !_Set(_Location) {Field: _, Fragment: _, Inline: _,
      Operation: _, Spread: _, Variable: _}
    name: DrctParamDict
    parameters:
    - !_InputParam
      input: InDrctParamDict
      modifiers: [!_ModifierDictionary {by: String, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Basic}]
    repeatable: false
  !_Identifier DrctParamIn: !_Directive
    locations: !_Set(_Location) {Field: _, Fragment: _, Inline: _,
      Operation: _, Spread: _, Variable: _}
    name: DrctParamIn
    parameters:
    - !_InputParam
      input: InDrctParamIn
    repeatable: false
  !_Identifier DrctParamList: !_Directive
    locations: !_Set(_Location) {Field: _, Fragment: _, Inline: _,
      Operation: _, Spread: _, Variable: _}
    name: DrctParamList
    parameters:
    - !_InputParam
      input: InDrctParamList
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
    repeatable: false
  !_Identifier DrctParamOpt: !_Directive
    locations: !_Set(_Location) {Field: _, Fragment: _, Inline: _,
      Operation: _, Spread: _, Variable: _}
    name: DrctParamOpt
    parameters:
    - !_InputParam
      input: InDrctParamOpt
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
    repeatable: false
name: Schema
settings: !_Map_Setting
  !_Identifier descr: !_Setting
    description: 'Option Descr'
    name: descr
    value: true
  !_Identifier global: !_Setting
    name: global
    value: true
  !_Identifier merged: !_Setting
    name: merged
    value:
    - true
    - 0
types: !_Map_Type
  !_Identifier AltAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltAltDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltAltDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltAltDual
    typeKind: !_TypeKind Dual
  !_Identifier AltAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltAltInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltAltInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltAltInp
    typeKind: !_TypeKind Input
  !_Identifier AltAltModBoolDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltAltModBoolDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltAltModBoolDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltAltModBoolDual
    typeKind: !_TypeKind Dual
  !_Identifier AltAltModBoolInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltAltModBoolInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltAltModBoolInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltAltModBoolInp
    typeKind: !_TypeKind Input
  !_Identifier AltAltModBoolOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltAltModBoolOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltAltModBoolOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltAltModBoolOutp
    typeKind: !_TypeKind Output
  !_Identifier AltAltModParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltAltModParamDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltAltModParamDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltAltModParamDual
    typeKind: !_TypeKind Dual
  !_Identifier AltAltModParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltAltModParamInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltAltModParamInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltAltModParamInp
    typeKind: !_TypeKind Input
  !_Identifier AltAltModParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltAltModParamOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltAltModParamOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltAltModParamOutp
    typeKind: !_TypeKind Output
  !_Identifier AltAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltAltOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltAltOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltAltOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstAltDualDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstAltDualDual
    parent: !_DualBase
      dual: PrntCnstAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstAltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstAltDualInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstAltDualInp
    parent: !_DualBase
      dual: PrntCnstAltDualInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstAltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstAltDualOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstAltDualOutp
    parent: !_DualBase
      dual: PrntCnstAltDualOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstAltObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltObjDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstAltObjDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstAltObjDual
    parent: !_DualBase
      dual: PrntCnstAltObjDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstAltObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstAltObjInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstAltObjInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstAltObjInp
    parent: !_InputBase
      input: PrntCnstAltObjInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstAltObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstAltObjOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstAltObjOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstAltObjOutp
    parent: !_OutputBase
      output: PrntCnstAltObjOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstFieldDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstFieldDualDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstFieldDualDual
    parent: !_DualBase
      dual: PrntCnstFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstFieldDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstFieldDualInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstFieldDualInp
    parent: !_DualBase
      dual: PrntCnstFieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstFieldDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstFieldDualOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstFieldDualOutp
    parent: !_DualBase
      dual: PrntCnstFieldDualOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstFieldObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldObjDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstFieldObjDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstFieldObjDual
    parent: !_DualBase
      dual: PrntCnstFieldObjDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstFieldObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstFieldObjInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstFieldObjInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstFieldObjInp
    parent: !_InputBase
      input: PrntCnstFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstFieldObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstFieldObjOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstFieldObjOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstFieldObjOutp
    parent: !_OutputBase
      output: PrntCnstFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstPrntDualPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstPrntDualPrntDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstPrntDualPrntDual
    parent: !_DualBase
      dual: PrntCnstPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstPrntDualPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstPrntDualPrntInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstPrntDualPrntInp
    parent: !_DualBase
      dual: PrntCnstPrntDualPrntInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstPrntDualPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstPrntDualPrntOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstPrntDualPrntOutp
    parent: !_DualBase
      dual: PrntCnstPrntDualPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier AltCnstPrntObjPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntObjPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstPrntObjPrntDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltCnstPrntObjPrntDual
    parent: !_DualBase
      dual: PrntCnstPrntObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier AltCnstPrntObjPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstPrntObjPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstPrntObjPrntInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltCnstPrntObjPrntInp
    parent: !_InputBase
      input: PrntCnstPrntObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier AltCnstPrntObjPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstPrntObjPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstPrntObjPrntOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltCnstPrntObjPrntOutp
    parent: !_OutputBase
      output: PrntCnstPrntObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier AltDescrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltDescrDual
      type: !_DualBase
        description: 'Test Descr'
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        description: 'Test Descr'
        dual: String
    name: AltDescrDual
    typeKind: !_TypeKind Dual
  !_Identifier AltDescrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltDescrInp
      type: !_InputBase
        description: 'Test Descr'
        input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        description: 'Test Descr'
        input: String
    name: AltDescrInp
    typeKind: !_TypeKind Input
  !_Identifier AltDescrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltDescrOutp
      type: !_OutputBase
        description: 'Test Descr'
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        description: 'Test Descr'
        output: String
    name: AltDescrOutp
    typeKind: !_TypeKind Output
  !_Identifier AltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltDual
      type: !_DualBase
        dual: AltAltDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: AltAltDual
    name: AltDual
    typeKind: !_TypeKind Dual
  !_Identifier AltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltDualDual
      type: !_DualBase
        dual: ObjDualAltDualDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: ObjDualAltDualDual
    name: AltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltDualInp
      type: !_DualBase
        dual: ObjDualAltDualInp
    alternates:
    - !_InputAlternate
      type: !_DualBase
        dual: ObjDualAltDualInp
    name: AltDualInp
    typeKind: !_TypeKind Input
  !_Identifier AltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltDualOutp
      type: !_DualBase
        dual: ObjDualAltDualOutp
    alternates:
    - !_OutputAlternate
      type: !_DualBase
        dual: ObjDualAltDualOutp
    name: AltDualOutp
    typeKind: !_TypeKind Output
  !_Identifier AltGnrcAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcAltDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcAltDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcAltDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcAltDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcAltDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcAltDualInp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcAltDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcAltDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcAltDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcAltDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcAltParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcAltParamDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcAltParamDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcAltParamDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcAltParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltGnrcAltParamInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltGnrcAltParamInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltGnrcAltParamInp
    typeKind: !_TypeKind Input
  !_Identifier AltGnrcAltParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltGnrcAltParamOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltGnrcAltParamOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltGnrcAltParamOutp
    typeKind: !_TypeKind Output
  !_Identifier AltGnrcFieldDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcFieldDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcFieldDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcFieldDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcFieldDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcFieldDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcFieldDualInp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcFieldDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcFieldDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcFieldDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcFieldDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcFieldParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcFieldParamDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcFieldParamDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcFieldParamDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcFieldParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltGnrcFieldParamInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltGnrcFieldParamInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltGnrcFieldParamInp
    typeKind: !_TypeKind Input
  !_Identifier AltGnrcFieldParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltGnrcFieldParamOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltGnrcFieldParamOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltGnrcFieldParamOutp
    typeKind: !_TypeKind Output
  !_Identifier AltGnrcPrntDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualInp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntDualPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualPrntDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntDualPrntInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualPrntInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualPrntInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualPrntInp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntDualPrntOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualPrntOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualPrntOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntDualPrntOutp
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntParamDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntParamDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntParamDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltGnrcPrntParamInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltGnrcPrntParamInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltGnrcPrntParamInp
    typeKind: !_TypeKind Input
  !_Identifier AltGnrcPrntParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltGnrcPrntParamOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltGnrcPrntParamOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltGnrcPrntParamOutp
    typeKind: !_TypeKind Output
  !_Identifier AltGnrcPrntParamPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntParamPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntParamPrntDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: AltGnrcPrntParamPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier AltGnrcPrntParamPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltGnrcPrntParamPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltGnrcPrntParamPrntInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: alt
      type: !_InputBase
        input: Number
    name: AltGnrcPrntParamPrntInp
    typeKind: !_TypeKind Input
  !_Identifier AltGnrcPrntParamPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltGnrcPrntParamPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltGnrcPrntParamPrntOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: alt
      type: !_OutputBase
        output: Number
    name: AltGnrcPrntParamPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier AltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltInp
      type: !_InputBase
        input: AltAltInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: AltAltInp
    name: AltInp
    typeKind: !_TypeKind Input
  !_Identifier AltModBoolDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: AltModBoolDual
      type: !_DualBase
        dual: AltAltModBoolDual
    alternates:
    - !_DualAlternate
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_DualBase
        dual: AltAltModBoolDual
    name: AltModBoolDual
    typeKind: !_TypeKind Dual
  !_Identifier AltModBoolInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: AltModBoolInp
      type: !_InputBase
        input: AltAltModBoolInp
    alternates:
    - !_InputAlternate
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_InputBase
        input: AltAltModBoolInp
    name: AltModBoolInp
    typeKind: !_TypeKind Input
  !_Identifier AltModBoolOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: AltModBoolOutp
      type: !_OutputBase
        output: AltAltModBoolOutp
    alternates:
    - !_OutputAlternate
      collections:
      - !_ModifierDictionary
        by: ^
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_OutputBase
        output: AltAltModBoolOutp
    name: AltModBoolOutp
    typeKind: !_TypeKind Output
  !_Identifier AltModParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: AltModParamDual
      type: !_DualBase
        dual: AltAltModParamDual
    alternates:
    - !_DualAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_DualBase
        dual: AltAltModParamDual
    name: AltModParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier AltModParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: AltModParamInp
      type: !_InputBase
        input: AltAltModParamInp
    alternates:
    - !_InputAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_InputBase
        input: AltAltModParamInp
    name: AltModParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier AltModParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: AltModParamOutp
      type: !_OutputBase
        output: AltAltModParamOutp
    alternates:
    - !_OutputAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_OutputBase
        output: AltAltModParamOutp
    name: AltModParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier AltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltOutp
      type: !_OutputBase
        output: AltAltOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: AltAltOutp
    name: AltOutp
    typeKind: !_TypeKind Output
  !_Identifier AltSmplDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltSmplDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: AltSmplDual
    typeKind: !_TypeKind Dual
  !_Identifier AltSmplInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltSmplInp
      type: !_InputBase
        input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    name: AltSmplInp
    typeKind: !_TypeKind Input
  !_Identifier AltSmplOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltSmplOutp
      type: !_OutputBase
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    name: AltSmplOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstAltDmnDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: CnstAltDmnDual
      type: !_DualBase
        dual: RefCnstAltDmnDual
        typeArgs:
        - !_DualArg
          dual: DomCnstAltDmnDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefCnstAltDmnDual
        typeArgs:
        - !_DualArg
          dual: DomCnstAltDmnDual
    name: CnstAltDmnDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstAltDmnInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: CnstAltDmnInp
      type: !_InputBase
        input: RefCnstAltDmnInp
        typeArgs:
        - !_InputArg
          input: DomCnstAltDmnInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefCnstAltDmnInp
        typeArgs:
        - !_InputArg
          input: DomCnstAltDmnInp
    name: CnstAltDmnInp
    typeKind: !_TypeKind Input
  !_Identifier CnstAltDmnOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: CnstAltDmnOutp
      type: !_OutputBase
        output: RefCnstAltDmnOutp
        typeArgs:
        - !_OutputArg
          output: DomCnstAltDmnOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefCnstAltDmnOutp
        typeArgs:
        - !_OutputArg
          output: DomCnstAltDmnOutp
    name: CnstAltDmnOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: CnstAltDual
      type: !_DualBase
        typeParam: type
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: type
    name: CnstAltDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: Number
      name: type
  !_Identifier CnstAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: CnstAltDualDual
      type: !_DualBase
        dual: RefCnstAltDualDual
        typeArgs:
        - !_DualArg
          dual: AltCnstAltDualDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefCnstAltDualDual
        typeArgs:
        - !_DualArg
          dual: AltCnstAltDualDual
    name: CnstAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstAltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: CnstAltDualInp
      type: !_InputBase
        input: RefCnstAltDualInp
        typeArgs:
        - !_InputArg
          input: AltCnstAltDualInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefCnstAltDualInp
        typeArgs:
        - !_InputArg
          input: AltCnstAltDualInp
    name: CnstAltDualInp
    typeKind: !_TypeKind Input
  !_Identifier CnstAltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: CnstAltDualOutp
      type: !_OutputBase
        output: RefCnstAltDualOutp
        typeArgs:
        - !_OutputArg
          output: AltCnstAltDualOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefCnstAltDualOutp
        typeArgs:
        - !_OutputArg
          output: AltCnstAltDualOutp
    name: CnstAltDualOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: CnstAltInp
      type: !_InputBase
        typeParam: type
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: type
    name: CnstAltInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: Number
      name: type
  !_Identifier CnstAltObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: CnstAltObjDual
      type: !_DualBase
        dual: RefCnstAltObjDual
        typeArgs:
        - !_DualArg
          dual: AltCnstAltObjDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefCnstAltObjDual
        typeArgs:
        - !_DualArg
          dual: AltCnstAltObjDual
    name: CnstAltObjDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstAltObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: CnstAltObjInp
      type: !_InputBase
        input: RefCnstAltObjInp
        typeArgs:
        - !_InputArg
          input: AltCnstAltObjInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefCnstAltObjInp
        typeArgs:
        - !_InputArg
          input: AltCnstAltObjInp
    name: CnstAltObjInp
    typeKind: !_TypeKind Input
  !_Identifier CnstAltObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: CnstAltObjOutp
      type: !_OutputBase
        output: RefCnstAltObjOutp
        typeArgs:
        - !_OutputArg
          output: AltCnstAltObjOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefCnstAltObjOutp
        typeArgs:
        - !_OutputArg
          output: AltCnstAltObjOutp
    name: CnstAltObjOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: CnstAltOutp
      type: !_OutputBase
        typeParam: type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: type
    name: CnstAltOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: Number
      name: type
  !_Identifier CnstFieldDmnDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldDmnDual
      type: !_DualBase
        dual: DomCnstFieldDmnDual
    name: CnstFieldDmnDual
    parent: !_DualBase
      dual: RefCnstFieldDmnDual
      typeArgs:
      - !_DualArg
        dual: DomCnstFieldDmnDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstFieldDmnInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldDmnInp
      type: !_InputBase
        input: DomCnstFieldDmnInp
    name: CnstFieldDmnInp
    parent: !_InputBase
      input: RefCnstFieldDmnInp
      typeArgs:
      - !_InputArg
        input: DomCnstFieldDmnInp
    typeKind: !_TypeKind Input
  !_Identifier CnstFieldDmnOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldDmnOutp
      type: !_OutputBase
        output: DomCnstFieldDmnOutp
    name: CnstFieldDmnOutp
    parent: !_OutputBase
      output: RefCnstFieldDmnOutp
      typeArgs:
      - !_OutputArg
        output: DomCnstFieldDmnOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstFieldDualDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldDualDual
      type: !_DualBase
        dual: AltCnstFieldDualDual
    name: CnstFieldDualDual
    parent: !_DualBase
      dual: RefCnstFieldDualDual
      typeArgs:
      - !_DualArg
        dual: AltCnstFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstFieldDualInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldDualInp
      type: !_InputBase
        input: AltCnstFieldDualInp
    name: CnstFieldDualInp
    parent: !_InputBase
      input: RefCnstFieldDualInp
      typeArgs:
      - !_InputArg
        input: AltCnstFieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier CnstFieldDualOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldDualOutp
      type: !_OutputBase
        output: AltCnstFieldDualOutp
    name: CnstFieldDualOutp
    parent: !_OutputBase
      output: RefCnstFieldDualOutp
      typeArgs:
      - !_OutputArg
        output: AltCnstFieldDualOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstFieldObjDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldObjDual
      type: !_DualBase
        dual: AltCnstFieldObjDual
    name: CnstFieldObjDual
    parent: !_DualBase
      dual: RefCnstFieldObjDual
      typeArgs:
      - !_DualArg
        dual: AltCnstFieldObjDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstFieldObjInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldObjInp
      type: !_InputBase
        input: AltCnstFieldObjInp
    name: CnstFieldObjInp
    parent: !_InputBase
      input: RefCnstFieldObjInp
      typeArgs:
      - !_InputArg
        input: AltCnstFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier CnstFieldObjOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldObjOutp
      type: !_OutputBase
        output: AltCnstFieldObjOutp
    name: CnstFieldObjOutp
    parent: !_OutputBase
      output: RefCnstFieldObjOutp
      typeArgs:
      - !_OutputArg
        output: AltCnstFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstPrntDualPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstPrntDualPrntDual
      type: !_DualBase
        dual: Number
    name: CnstPrntDualPrntDual
    parent: !_DualBase
      dual: RefCnstPrntDualPrntDual
      typeArgs:
      - !_DualArg
        dual: AltCnstPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstPrntDualPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstPrntDualPrntInp
      type: !_InputBase
        input: Number
    name: CnstPrntDualPrntInp
    parent: !_InputBase
      input: RefCnstPrntDualPrntInp
      typeArgs:
      - !_InputArg
        input: AltCnstPrntDualPrntInp
    typeKind: !_TypeKind Input
  !_Identifier CnstPrntDualPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstPrntDualPrntOutp
      type: !_OutputBase
        output: Number
    name: CnstPrntDualPrntOutp
    parent: !_OutputBase
      output: RefCnstPrntDualPrntOutp
      typeArgs:
      - !_OutputArg
        output: AltCnstPrntDualPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier CnstPrntObjPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntObjPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltCnstPrntObjPrntDual
      type: !_DualBase
        dual: Number
    name: CnstPrntObjPrntDual
    parent: !_DualBase
      dual: RefCnstPrntObjPrntDual
      typeArgs:
      - !_DualArg
        dual: AltCnstPrntObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstPrntObjPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstPrntObjPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltCnstPrntObjPrntInp
      type: !_InputBase
        input: Number
    name: CnstPrntObjPrntInp
    parent: !_InputBase
      input: RefCnstPrntObjPrntInp
      typeArgs:
      - !_InputArg
        input: AltCnstPrntObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier CnstPrntObjPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstPrntObjPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltCnstPrntObjPrntOutp
      type: !_OutputBase
        output: Number
    name: CnstPrntObjPrntOutp
    parent: !_OutputBase
      output: RefCnstPrntObjPrntOutp
      typeArgs:
      - !_OutputArg
        output: AltCnstPrntObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier Ctgr: !_TypeOutput
    name: Ctgr
    typeKind: !_TypeKind Output
  !_Identifier CtgrAlias: !_TypeOutput
    name: CtgrAlias
    typeKind: !_TypeKind Output
  !_Identifier CtgrDescr: !_TypeOutput
    name: CtgrDescr
    typeKind: !_TypeKind Output
  !_Identifier CtgrDscrs: !_TypeOutput
    name: CtgrDscrs
    typeKind: !_TypeKind Output
  !_Identifier CtgrMod: !_TypeOutput
    name: CtgrMod
    typeKind: !_TypeKind Output
  !_Identifier CtgrOutp: !_TypeOutput
    name: CtgrOutp
    typeKind: !_TypeKind Output
  !_Identifier CtgrOutpDescr: !_TypeOutput
    name: CtgrOutpDescr
    typeKind: !_TypeKind Output
  !_Identifier CtgrOutpDict: !_TypeOutput
    name: CtgrOutpDict
    typeKind: !_TypeKind Output
  !_Identifier CtgrOutpList: !_TypeOutput
    name: CtgrOutpList
    typeKind: !_TypeKind Output
  !_Identifier CtgrOutpOptl: !_TypeOutput
    name: CtgrOutpOptl
    typeKind: !_TypeKind Output
  !_Identifier Descr: !_TypeOutput
    description: 'A simple description'
    name: Descr
    typeKind: !_TypeKind Output
  !_Identifier DescrBcks: !_TypeOutput
    description: 'A backslash ("\") description'
    name: DescrBcks
    typeKind: !_TypeKind Output
  !_Identifier DescrBtwn: !_TypeOutput
    description: 'A description between'
    name: DescrBtwn
    typeKind: !_TypeKind Output
  !_Identifier DescrCmpl: !_TypeOutput
    description: >-
      A "more" 'Complicated' \ description
    name: DescrCmpl
    typeKind: !_TypeKind Output
  !_Identifier DescrDbl: !_TypeOutput
    description: "A 'double-quoted' description"
    name: DescrDbl
    typeKind: !_TypeKind Output
  !_Identifier DescrSngl: !_TypeOutput
    description: 'A "single-quoted" description'
    name: DescrSngl
    typeKind: !_TypeKind Output
  !_Identifier DmnAlias: !_DomainNumber
    aliases: [Num1, Num2]
    domainKind: !_DomainKind Number
    name: DmnAlias
    typeKind: !_TypeKind Domain
  !_Identifier DmnBool: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBool
      exclude: false
      value: false
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBool
      exclude: false
      value: true
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: false
    - !_DomainTrueFalse
      exclude: false
      value: true
    name: DmnBool
    typeKind: !_TypeKind Domain
  !_Identifier DmnBoolDescr: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBoolDescr
      exclude: false
      value: true
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: true
    name: DmnBoolDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnBoolDiff: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBoolDiff
      exclude: false
      value: true
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBoolDiff
      exclude: false
      value: false
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: true
    - !_DomainTrueFalse
      exclude: false
      value: false
    name: DmnBoolDiff
    typeKind: !_TypeKind Domain
  !_Identifier DmnBoolPrnt: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: PrntDmnBoolPrnt
      exclude: false
      value: true
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBoolPrnt
      exclude: false
      value: false
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: false
    name: DmnBoolPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnBoolPrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnBoolPrntDescr: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: PrntDmnBoolPrntDescr
      exclude: false
      value: true
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBoolPrntDescr
      exclude: false
      value: false
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: false
    name: DmnBoolPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnBoolPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnBoolSame: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: DmnBoolSame
      exclude: false
      value: true
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: true
    name: DmnBoolSame
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumAll: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumAll
      exclude: false
      value: !_EnumValue
        label: '*'
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumAll
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: '*'
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumAll
    name: DmnEnumAll
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumAllDescr: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumAllDescr
      exclude: false
      value: !_EnumValue
        label: '*'
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumAllDescr
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: '*'
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumAllDescr
    name: DmnEnumAllDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumAllPrnt: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumAllPrnt
      exclude: false
      value: !_EnumValue
        label: '*'
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumAllPrnt
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: '*'
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumAllPrnt
    name: DmnEnumAllPrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumDescr: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumDescr
      exclude: false
      value: !_EnumValue
        label: dmnEnumDescr
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumDescr
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: dmnEnumDescr
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumDescr
    name: DmnEnumDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumDiff: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumDiff
      exclude: false
      value: !_EnumValue
        label: true
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumDiff
      exclude: false
      value: !_EnumValue
        label: false
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: true
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: false
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    name: DmnEnumDiff
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumLabel: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumLabel
      exclude: false
      value: !_EnumValue
        label: dmnEnumLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumLabel
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: dmnEnumLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumLabel
    name: DmnEnumLabel
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumPrnt: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: PrntDmnEnumPrnt
      exclude: false
      value: !_EnumValue
        label: prnt_dmnEnumPrnt
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumPrnt
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumPrnt
      exclude: false
      value: !_EnumValue
        label: enum_dmnEnumPrnt
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumPrnt
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: enum_dmnEnumPrnt
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumPrnt
    name: DmnEnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnEnumPrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumPrntDescr: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: PrntDmnEnumPrntDescr
      exclude: false
      value: !_EnumValue
        label: prnt_dmnEnumPrntDescr
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumPrntDescr
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumPrntDescr
      exclude: false
      value: !_EnumValue
        label: enum_dmnEnumPrntDescr
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumPrntDescr
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: enum_dmnEnumPrntDescr
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumPrntDescr
    name: DmnEnumPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnEnumPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumSame: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumSame
      exclude: false
      value: !_EnumValue
        label: true
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: true
        typeKind: !_SimpleKind Enum
        typeName: Boolean
    name: DmnEnumSame
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumValue: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumValue
      exclude: false
      value: !_EnumValue
        label: dmnEnumValue
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumValue
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: dmnEnumValue
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumValue
    name: DmnEnumValue
    typeKind: !_TypeKind Domain
  !_Identifier DmnEnumValuePrnt: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DmnEnumValuePrnt
      exclude: false
      value: !_EnumValue
        label: prnt_dmnEnumValuePrnt
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumValuePrnt
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: prnt_dmnEnumValuePrnt
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumValuePrnt
    name: DmnEnumValuePrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbr: !_DomainNumber
    domainKind: !_DomainKind Number
    name: DmnNmbr
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbrDescr: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: DmnNmbrDescr
      exclude: false
      to: 2
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      to: 2
    name: DmnNmbrDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbrDiff: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: DmnNmbrDiff
      exclude: false
      from: 1
      to: 9
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      from: 1
      to: 9
    name: DmnNmbrDiff
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbrPrnt: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: PrntDmnNmbrPrnt
      exclude: false
      to: 2
    - !_DomainItem(_DomainRange)
      domain: DmnNmbrPrnt
      exclude: false
      from: 2
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      from: 2
    name: DmnNmbrPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnNmbrPrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbrPrntDescr: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: PrntDmnNmbrPrntDescr
      exclude: false
      to: 2
    - !_DomainItem(_DomainRange)
      domain: DmnNmbrPrntDescr
      exclude: false
      from: 2
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      from: 2
    name: DmnNmbrPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnNmbrPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnNmbrSame: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: DmnNmbrSame
      exclude: false
      from: 1
      to: 9
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      from: 1
      to: 9
    name: DmnNmbrSame
    typeKind: !_TypeKind Domain
  !_Identifier DmnStr: !_DomainString
    domainKind: !_DomainKind String
    name: DmnStr
    typeKind: !_TypeKind Domain
  !_Identifier DmnStrDescr: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DmnStrDescr
      exclude: false
      pattern: a+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: a+
    name: DmnStrDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnStrDiff: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DmnStrDiff
      exclude: false
      pattern: a+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: a+
    name: DmnStrDiff
    typeKind: !_TypeKind Domain
  !_Identifier DmnStrPrnt: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: PrntDmnStrPrnt
      exclude: false
      pattern: b+
    - !_DomainItem(_DomainRegex)
      domain: DmnStrPrnt
      exclude: false
      pattern: a+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: a+
    name: DmnStrPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnStrPrnt
    typeKind: !_TypeKind Domain
  !_Identifier DmnStrPrntDescr: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: PrntDmnStrPrntDescr
      exclude: false
      pattern: b+
    - !_DomainItem(_DomainRegex)
      domain: DmnStrPrntDescr
      exclude: false
      pattern: a+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: a+
    name: DmnStrPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Domain
      typeName: PrntDmnStrPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier DmnStrSame: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DmnStrSame
      exclude: false
      pattern: a+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: a+
    name: DmnStrSame
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstAltDmnDual: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstAltDmnDual
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstAltDmnDual
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstAltDmnInp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstAltDmnInp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstAltDmnInp
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstAltDmnOutp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstAltDmnOutp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstAltDmnOutp
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstFieldDmnDual: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstFieldDmnDual
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstFieldDmnDual
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstFieldDmnInp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstFieldDmnInp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstFieldDmnInp
    typeKind: !_TypeKind Domain
  !_Identifier DomCnstFieldDmnOutp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomCnstFieldDmnOutp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomCnstFieldDmnOutp
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntEnumDomDual: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DomGnrcPrntEnumDomDual
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomDualLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomDual
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomDualLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomDual
    name: DomGnrcPrntEnumDomDual
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntEnumDomInp: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DomGnrcPrntEnumDomInp
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomInpLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomInp
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomInpLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomInp
    name: DomGnrcPrntEnumDomInp
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntEnumDomOutp: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: DomGnrcPrntEnumDomOutp
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomOutpLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomOutp
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: gnrcPrntEnumDomOutpLabel
        typeKind: !_SimpleKind Enum
        typeName: EnumGnrcPrntEnumDomOutp
    name: DomGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntStrDomDual: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomGnrcPrntStrDomDual
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomGnrcPrntStrDomDual
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntStrDomInp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomGnrcPrntStrDomInp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomGnrcPrntStrDomInp
    typeKind: !_TypeKind Domain
  !_Identifier DomGnrcPrntStrDomOutp: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: DomGnrcPrntStrDomOutp
      exclude: false
      pattern: \w+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: \w+
    name: DomGnrcPrntStrDomOutp
    typeKind: !_TypeKind Domain
  !_Identifier DomOutpParamModDmn: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: DomOutpParamModDmn
      exclude: false
      from: 1
      to: 10
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      from: 1
      to: 10
    name: DomOutpParamModDmn
    typeKind: !_TypeKind Domain
  !_Identifier DomOutpParamModParam: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: DomOutpParamModParam
      exclude: false
      from: 1
      to: 10
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      from: 1
      to: 10
    name: DomOutpParamModParam
    typeKind: !_TypeKind Domain
  !_Identifier Dscrs: !_TypeOutput
    description: >-
      A simple description With extra
    name: Dscrs
    typeKind: !_TypeKind Output
  !_Identifier DupDmnEnumUnqPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: DupDmnEnumUnqPrnt
      name: dmnEnumUnqPrnt
    - !_EnumLabel
      enum: DupDmnEnumUnqPrnt
      name: dup_dmnEnumUnqPrnt
    items:
    - !_Aliased
      name: dmnEnumUnqPrnt
    - !_Aliased
      name: dup_dmnEnumUnqPrnt
    name: DupDmnEnumUnqPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumAlias: !_TypeEnum
    aliases: [En1, En2]
    allItems:
    - !_EnumLabel
      enum: EnumAlias
      name: enumAlias
    items:
    - !_Aliased
      name: enumAlias
    name: EnumAlias
    typeKind: !_TypeKind Enum
  !_Identifier EnumDescr: !_TypeEnum
    allItems:
    - !_EnumLabel
      description: 'Enum Descr'
      enum: EnumDescr
      name: enumDescr
    items:
    - !_Aliased
      description: 'Enum Descr'
      name: enumDescr
    name: EnumDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumDiff: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDiff
      name: one
    - !_EnumLabel
      enum: EnumDiff
      name: two
    items:
    - !_Aliased
      name: one
    - !_Aliased
      name: two
    name: EnumDiff
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumAll: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDmnEnumAll
      name: dmnEnumAll
    - !_EnumLabel
      enum: EnumDmnEnumAll
      name: enum_dmnEnumAll
    items:
    - !_Aliased
      name: dmnEnumAll
    - !_Aliased
      name: enum_dmnEnumAll
    name: EnumDmnEnumAll
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumAllDescr: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDmnEnumAllDescr
      name: dmnEnumAllDescr
    - !_EnumLabel
      enum: EnumDmnEnumAllDescr
      name: enum_dmnEnumAllDescr
    items:
    - !_Aliased
      name: dmnEnumAllDescr
    - !_Aliased
      name: enum_dmnEnumAllDescr
    name: EnumDmnEnumAllDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumAllPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntDmnEnumAllPrnt
      name: prnt_dmnEnumAllPrnt
    - !_EnumLabel
      enum: EnumDmnEnumAllPrnt
      name: dmnEnumAllPrnt
    items:
    - !_Aliased
      name: dmnEnumAllPrnt
    name: EnumDmnEnumAllPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntDmnEnumAllPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumDescr: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDmnEnumDescr
      name: dmnEnumDescr
    items:
    - !_Aliased
      name: dmnEnumDescr
    name: EnumDmnEnumDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumLabel: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDmnEnumLabel
      name: dmnEnumLabel
    items:
    - !_Aliased
      name: dmnEnumLabel
    name: EnumDmnEnumLabel
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDmnEnumPrnt
      name: enum_dmnEnumPrnt
    - !_EnumLabel
      enum: EnumDmnEnumPrnt
      name: prnt_dmnEnumPrnt
    items:
    - !_Aliased
      name: enum_dmnEnumPrnt
    - !_Aliased
      name: prnt_dmnEnumPrnt
    name: EnumDmnEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumPrntDescr: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDmnEnumPrntDescr
      name: enum_dmnEnumPrntDescr
    - !_EnumLabel
      enum: EnumDmnEnumPrntDescr
      name: prnt_dmnEnumPrntDescr
    items:
    - !_Aliased
      name: enum_dmnEnumPrntDescr
    - !_Aliased
      name: prnt_dmnEnumPrntDescr
    name: EnumDmnEnumPrntDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumUnq: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDmnEnumUnq
      name: enum_dmnEnumUnq
    - !_EnumLabel
      enum: EnumDmnEnumUnq
      name: dmnEnumUnq
    items:
    - !_Aliased
      name: enum_dmnEnumUnq
    - !_Aliased
      name: dmnEnumUnq
    name: EnumDmnEnumUnq
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumUnqPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntDmnEnumUnqPrnt
      name: dmnEnumUnqPrnt
    - !_EnumLabel
      enum: PrntDmnEnumUnqPrnt
      name: prnt_dmnEnumUnqPrnt
    - !_EnumLabel
      enum: EnumDmnEnumUnqPrnt
      name: enum_dmnEnumUnqPrnt
    items:
    - !_Aliased
      name: enum_dmnEnumUnqPrnt
    name: EnumDmnEnumUnqPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntDmnEnumUnqPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumValue: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDmnEnumValue
      name: dmnEnumValue
    items:
    - !_Aliased
      name: dmnEnumValue
    name: EnumDmnEnumValue
    typeKind: !_TypeKind Enum
  !_Identifier EnumDmnEnumValuePrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntDmnEnumValuePrnt
      name: prnt_dmnEnumValuePrnt
    - !_EnumLabel
      enum: EnumDmnEnumValuePrnt
      name: dmnEnumValuePrnt
    items:
    - !_Aliased
      name: dmnEnumValuePrnt
    name: EnumDmnEnumValuePrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntDmnEnumValuePrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumDomDup: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumDomDup
      name: dmnEnumUnq
    - !_EnumLabel
      enum: EnumDomDup
      name: dup_dmnEnumUnq
    items:
    - !_Aliased
      name: dmnEnumUnq
    - !_Aliased
      name: dup_dmnEnumUnq
    name: EnumDomDup
    typeKind: !_TypeKind Enum
  !_Identifier EnumFieldModEnumDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumFieldModEnumDual
      name: value
    items:
    - !_Aliased
      name: value
    name: EnumFieldModEnumDual
    typeKind: !_TypeKind Enum
  !_Identifier EnumFieldModEnumInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumFieldModEnumInp
      name: value
    items:
    - !_Aliased
      name: value
    name: EnumFieldModEnumInp
    typeKind: !_TypeKind Enum
  !_Identifier EnumFieldModEnumOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumFieldModEnumOutp
      name: value
    items:
    - !_Aliased
      name: value
    name: EnumFieldModEnumOutp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumChildDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildDual
      name: gnrcPrntEnumChildDualParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumChildDual
      name: gnrcPrntEnumChildDualLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumChildDualLabel
    name: EnumGnrcPrntEnumChildDual
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumChildDual
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumChildInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildInp
      name: gnrcPrntEnumChildInpParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumChildInp
      name: gnrcPrntEnumChildInpLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumChildInpLabel
    name: EnumGnrcPrntEnumChildInp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumChildInp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumChildOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildOutp
      name: gnrcPrntEnumChildOutpParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumChildOutp
      name: gnrcPrntEnumChildOutpLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumChildOutpLabel
    name: EnumGnrcPrntEnumChildOutp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumDomDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomDual
      name: gnrcPrntEnumDomDualLabel
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomDual
      name: gnrcPrntEnumDomDualOther
    items:
    - !_Aliased
      name: gnrcPrntEnumDomDualLabel
    - !_Aliased
      name: gnrcPrntEnumDomDualOther
    name: EnumGnrcPrntEnumDomDual
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumDomInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomInp
      name: gnrcPrntEnumDomInpLabel
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomInp
      name: gnrcPrntEnumDomInpOther
    items:
    - !_Aliased
      name: gnrcPrntEnumDomInpLabel
    - !_Aliased
      name: gnrcPrntEnumDomInpOther
    name: EnumGnrcPrntEnumDomInp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumDomOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomOutp
      name: gnrcPrntEnumDomOutpLabel
    - !_EnumLabel
      enum: EnumGnrcPrntEnumDomOutp
      name: gnrcPrntEnumDomOutpOther
    items:
    - !_Aliased
      name: gnrcPrntEnumDomOutpLabel
    - !_Aliased
      name: gnrcPrntEnumDomOutpOther
    name: EnumGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumPrntDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntDual
      name: gnrcPrntEnumPrntDualParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumPrntDual
      name: gnrcPrntEnumPrntDualLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntDualLabel
    name: EnumGnrcPrntEnumPrntDual
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumPrntInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntInp
      name: gnrcPrntEnumPrntInpParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumPrntInp
      name: gnrcPrntEnumPrntInpLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntInpLabel
    name: EnumGnrcPrntEnumPrntInp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntEnumPrntOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntOutp
      name: gnrcPrntEnumPrntOutpParent
    - !_EnumLabel
      enum: EnumGnrcPrntEnumPrntOutp
      name: gnrcPrntEnumPrntOutpLabel
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntOutpLabel
    name: EnumGnrcPrntEnumPrntOutp
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentGnrcPrntEnumPrntOutp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntSmplEnumDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntSmplEnumDual
      name: gnrcPrntSmplEnumDual
    items:
    - !_Aliased
      name: gnrcPrntSmplEnumDual
    name: EnumGnrcPrntSmplEnumDual
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntSmplEnumInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntSmplEnumInp
      name: gnrcPrntSmplEnumInp
    items:
    - !_Aliased
      name: gnrcPrntSmplEnumInp
    name: EnumGnrcPrntSmplEnumInp
    typeKind: !_TypeKind Enum
  !_Identifier EnumGnrcPrntSmplEnumOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumGnrcPrntSmplEnumOutp
      name: gnrcPrntSmplEnumOutp
    items:
    - !_Aliased
      name: gnrcPrntSmplEnumOutp
    name: EnumGnrcPrntSmplEnumOutp
    typeKind: !_TypeKind Enum
  !_Identifier EnumInpFieldEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumInpFieldEnum
      name: inpFieldEnum
    items:
    - !_Aliased
      name: inpFieldEnum
    name: EnumInpFieldEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpCnstDomEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpCnstDomEnum
      name: outpCnstDomEnum
    - !_EnumLabel
      enum: EnumOutpCnstDomEnum
      name: other
    items:
    - !_Aliased
      name: outpCnstDomEnum
    - !_Aliased
      name: other
    name: EnumOutpCnstDomEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpCnstEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpCnstEnum
      name: outpCnstEnum
    items:
    - !_Aliased
      name: outpCnstEnum
    name: EnumOutpCnstEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpCnstEnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentOutpCnstEnumPrnt
      name: parentOutpCnstEnumPrnt
    - !_EnumLabel
      enum: EnumOutpCnstEnumPrnt
      name: outpCnstEnumPrnt
    items:
    - !_Aliased
      name: outpCnstEnumPrnt
    name: EnumOutpCnstEnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentOutpCnstEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpCnstPrntEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentOutpCnstPrntEnum
      name: parentOutpCnstPrntEnum
    - !_EnumLabel
      enum: EnumOutpCnstPrntEnum
      name: outpCnstPrntEnum
    items:
    - !_Aliased
      name: outpCnstPrntEnum
    name: EnumOutpCnstPrntEnum
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: ParentOutpCnstPrntEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpFieldEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpFieldEnum
      name: outpFieldEnum
    items:
    - !_Aliased
      name: outpFieldEnum
    name: EnumOutpFieldEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpFieldEnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntOutpFieldEnumPrnt
      name: prnt_outpFieldEnumPrnt
    - !_EnumLabel
      enum: EnumOutpFieldEnumPrnt
      name: outpFieldEnumPrnt
    items:
    - !_Aliased
      name: outpFieldEnumPrnt
    name: EnumOutpFieldEnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntOutpFieldEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpFieldValue: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpFieldValue
      name: outpFieldValue
    items:
    - !_Aliased
      name: outpFieldValue
    name: EnumOutpFieldValue
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpFieldValueDescr: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpFieldValueDescr
      name: outpFieldValueDescr
    items:
    - !_Aliased
      name: outpFieldValueDescr
    name: EnumOutpFieldValueDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpGnrcEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpGnrcEnum
      name: outpGnrcEnum
    items:
    - !_Aliased
      name: outpGnrcEnum
    name: EnumOutpGnrcEnum
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpGnrcValue: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumOutpGnrcValue
      name: outpGnrcValue
    items:
    - !_Aliased
      name: outpGnrcValue
    name: EnumOutpGnrcValue
    typeKind: !_TypeKind Enum
  !_Identifier EnumOutpPrntGnrc: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntOutpPrntGnrc
      name: prnt_outpPrntGnrc
    - !_EnumLabel
      enum: EnumOutpPrntGnrc
      name: outpPrntGnrc
    items:
    - !_Aliased
      name: outpPrntGnrc
    name: EnumOutpPrntGnrc
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntOutpPrntGnrc
    typeKind: !_TypeKind Enum
  !_Identifier EnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntEnumPrnt
      name: prnt_enumPrnt
    - !_EnumLabel
      enum: EnumPrnt
      name: enumPrnt
    items:
    - !_Aliased
      name: enumPrnt
    name: EnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumPrntAlias: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntEnumPrntAlias
      name: prnt_enumPrntAlias
    - !_EnumLabel
      enum: EnumPrntAlias
      name: val_enumPrntAlias
    - !_EnumLabel
      aliases: [enumPrntAlias]
      enum: EnumPrntAlias
      name: prnt_enumPrntAlias
    items:
    - !_Aliased
      name: val_enumPrntAlias
    - !_Aliased
      aliases: [enumPrntAlias]
      name: prnt_enumPrntAlias
    name: EnumPrntAlias
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumPrntAlias
    typeKind: !_TypeKind Enum
  !_Identifier EnumPrntDescr: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntEnumPrntDescr
      name: prnt_enumPrntDescr
    - !_EnumLabel
      enum: EnumPrntDescr
      name: enumPrntDescr
    items:
    - !_Aliased
      name: enumPrntDescr
    name: EnumPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumPrntDescr
    typeKind: !_TypeKind Enum
  !_Identifier EnumPrntDup: !_TypeEnum
    allItems:
    - !_EnumLabel
      aliases: [enumPrntDup]
      enum: PrntEnumPrntDup
      name: prnt_enumPrntDup
    - !_EnumLabel
      enum: EnumPrntDup
      name: enumPrntDup
    items:
    - !_Aliased
      name: enumPrntDup
    name: EnumPrntDup
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumPrntDup
    typeKind: !_TypeKind Enum
  !_Identifier EnumSame: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: EnumSame
      name: enumSame
    items:
    - !_Aliased
      name: enumSame
    name: EnumSame
    typeKind: !_TypeKind Enum
  !_Identifier EnumSamePrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntEnumSamePrnt
      name: prnt_enumSamePrnt
    - !_EnumLabel
      enum: EnumSamePrnt
      name: enumSamePrnt
    items:
    - !_Aliased
      name: enumSamePrnt
    name: EnumSamePrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumSamePrnt
    typeKind: !_TypeKind Enum
  !_Identifier EnumValueAlias: !_TypeEnum
    allItems:
    - !_EnumLabel
      aliases: [val1, val2]
      enum: EnumValueAlias
      name: enumValueAlias
    items:
    - !_Aliased
      aliases: [val1, val2]
      name: enumValueAlias
    name: EnumValueAlias
    typeKind: !_TypeKind Enum
  !_Identifier FieldDescrDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      description: 'Test Descr'
      name: field
      object: FieldDescrDual
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      description: 'Test Descr'
      name: field
      type: !_DualBase
        dual: String
    name: FieldDescrDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldDescrInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      description: 'Test Descr'
      name: field
      object: FieldDescrInp
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      description: 'Test Descr'
      name: field
      type: !_InputBase
        input: String
    name: FieldDescrInp
    typeKind: !_TypeKind Input
  !_Identifier FieldDescrOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      description: 'Test Descr'
      name: field
      object: FieldDescrOutp
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      description: 'Test Descr'
      name: field
      type: !_OutputBase
        output: String
    name: FieldDescrOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldDual
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: String
    name: FieldDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldDualDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldDualDual
      type: !_DualBase
        dual: FldFieldDualDual
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: FldFieldDualDual
    name: FieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldDualInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldDualInp
      type: !_DualBase
        dual: FldFieldDualInp
    fields:
    - !_InputField
      name: field
      type: !_DualBase
        dual: FldFieldDualInp
    name: FieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier FieldDualOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldDualOutp
      type: !_DualBase
        dual: FldFieldDualOutp
    fields:
    - !_OutputField
      name: field
      type: !_DualBase
        dual: FldFieldDualOutp
    name: FieldDualOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldGnrcPrntEnumChildDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumChildDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: FieldGnrcPrntEnumChildDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumChildDual
      name: ref
  !_Identifier FieldGnrcPrntEnumChildInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumChildInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: FieldGnrcPrntEnumChildInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumChildInp
      name: ref
  !_Identifier FieldGnrcPrntEnumChildOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumChildOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: FieldGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumChildOutp
      name: ref
  !_Identifier FieldGnrcPrntEnumDomDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumDomDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: FieldGnrcPrntEnumDomDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumDomDual
      name: ref
  !_Identifier FieldGnrcPrntEnumDomInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumDomInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: FieldGnrcPrntEnumDomInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumDomInp
      name: ref
  !_Identifier FieldGnrcPrntEnumDomOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumDomOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: FieldGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumGnrcPrntEnumDomOutp
      name: ref
  !_Identifier FieldGnrcPrntEnumPrntDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumPrntDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: FieldGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: ParentGnrcPrntEnumPrntDual
      name: ref
  !_Identifier FieldGnrcPrntEnumPrntInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumPrntInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: FieldGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: ParentGnrcPrntEnumPrntInp
      name: ref
  !_Identifier FieldGnrcPrntEnumPrntOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumPrntOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: FieldGnrcPrntEnumPrntOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: ParentGnrcPrntEnumPrntOutp
      name: ref
  !_Identifier FieldGnrcPrntSmplEnumDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntSmplEnumDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: FieldGnrcPrntSmplEnumDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Simple
      name: ref
  !_Identifier FieldGnrcPrntSmplEnumInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntSmplEnumInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: FieldGnrcPrntSmplEnumInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Simple
      name: ref
  !_Identifier FieldGnrcPrntSmplEnumOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntSmplEnumOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: FieldGnrcPrntSmplEnumOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Simple
      name: ref
  !_Identifier FieldGnrcPrntStrDomDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntStrDomDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: FieldGnrcPrntStrDomDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier FieldGnrcPrntStrDomInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntStrDomInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: FieldGnrcPrntStrDomInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier FieldGnrcPrntStrDomOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntStrDomOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: FieldGnrcPrntStrDomOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier FieldInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldInp
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: String
    name: FieldInp
    typeKind: !_TypeKind Input
  !_Identifier FieldModEnumDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumDual,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      object: FieldModEnumDual
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumDual,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      type: !_DualBase
        dual: String
    name: FieldModEnumDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldModEnumInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumInp,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      object: FieldModEnumInp
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumInp,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      type: !_InputBase
        input: String
    name: FieldModEnumInp
    typeKind: !_TypeKind Input
  !_Identifier FieldModEnumOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumOutp,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      object: FieldModEnumOutp
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      modifiers: [!_ModifierDictionary {by: EnumFieldModEnumOutp,
          modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Enum}]
      name: field
      type: !_OutputBase
        output: String
    name: FieldModEnumOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldModParamDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      object: FieldModParamDual
      type: !_DualBase
        dual: FldFieldModParamDual
    fields:
    - !_DualField
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      type: !_DualBase
        dual: FldFieldModParamDual
    name: FieldModParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier FieldModParamInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      object: FieldModParamInp
      type: !_InputBase
        input: FldFieldModParamInp
    fields:
    - !_InputField
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      type: !_InputBase
        input: FldFieldModParamInp
    name: FieldModParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier FieldModParamOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      object: FieldModParamOutp
      type: !_OutputBase
        output: FldFieldModParamOutp
    fields:
    - !_OutputField
      modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      name: field
      type: !_OutputBase
        output: FldFieldModParamOutp
    name: FieldModParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier FieldObjDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldObjDual
      type: !_DualBase
        dual: FldFieldObjDual
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: FldFieldObjDual
    name: FieldObjDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldObjInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldObjInp
      type: !_InputBase
        input: FldFieldObjInp
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: FldFieldObjInp
    name: FieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier FieldObjOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldObjOutp
      type: !_OutputBase
        output: FldFieldObjOutp
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: FldFieldObjOutp
    name: FieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldOutp
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: String
    name: FieldOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldSmplDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldSmplDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FieldSmplDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldSmplInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldSmplInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: Number
    name: FieldSmplInp
    typeKind: !_TypeKind Input
  !_Identifier FieldSmplOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldSmplOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: Number
    name: FieldSmplOutp
    typeKind: !_TypeKind Output
  !_Identifier FieldTypeDescrDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldTypeDescrDual
      type: !_DualBase
        description: 'Test Descr'
        dual: Number
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        description: 'Test Descr'
        dual: Number
    name: FieldTypeDescrDual
    typeKind: !_TypeKind Dual
  !_Identifier FieldTypeDescrInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldTypeDescrInp
      type: !_InputBase
        description: 'Test Descr'
        input: Number
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        description: 'Test Descr'
        input: Number
    name: FieldTypeDescrInp
    typeKind: !_TypeKind Input
  !_Identifier FieldTypeDescrOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldTypeDescrOutp
      type: !_OutputBase
        description: 'Test Descr'
        output: Number
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        description: 'Test Descr'
        output: Number
    name: FieldTypeDescrOutp
    typeKind: !_TypeKind Output
  !_Identifier FldFieldDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: FldFieldDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FldFieldDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FldFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: FldFieldDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FldFieldDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FldFieldDualInp
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: FldFieldDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FldFieldDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FldFieldDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldModParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: FldFieldModParamDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FldFieldModParamDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FldFieldModParamDual
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldModParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: FldFieldModParamInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FldFieldModParamInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: Number
    name: FldFieldModParamInp
    typeKind: !_TypeKind Input
  !_Identifier FldFieldModParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: FldFieldModParamOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FldFieldModParamOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: Number
    name: FldFieldModParamOutp
    typeKind: !_TypeKind Output
  !_Identifier FldFieldObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: FldFieldObjDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FldFieldObjDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: FldFieldObjDual
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: FldFieldObjInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FldFieldObjInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: Number
    name: FldFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier FldFieldObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: FldFieldObjOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FldFieldObjOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: Number
    name: FldFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier FldInpFieldNull: !_TypeDual
    name: FldInpFieldNull
    typeKind: !_TypeKind Dual
  !_Identifier FldObjFieldAliasDual: !_TypeDual
    name: FldObjFieldAliasDual
    typeKind: !_TypeKind Dual
  !_Identifier FldObjFieldAliasInp: !_TypeInput
    name: FldObjFieldAliasInp
    typeKind: !_TypeKind Input
  !_Identifier FldObjFieldAliasOutp: !_TypeOutput
    name: FldObjFieldAliasOutp
    typeKind: !_TypeKind Output
  !_Identifier FldObjFieldDual: !_TypeDual
    name: FldObjFieldDual
    typeKind: !_TypeKind Dual
  !_Identifier FldObjFieldInp: !_TypeInput
    name: FldObjFieldInp
    typeKind: !_TypeKind Input
  !_Identifier FldObjFieldOutp: !_TypeOutput
    name: FldObjFieldOutp
    typeKind: !_TypeKind Output
  !_Identifier FldOutpDescrParam: !_TypeDual
    name: FldOutpDescrParam
    typeKind: !_TypeKind Dual
  !_Identifier FldOutpFieldParam: !_TypeDual
    name: FldOutpFieldParam
    typeKind: !_TypeKind Dual
  !_Identifier FldOutpParam: !_TypeDual
    name: FldOutpParam
    typeKind: !_TypeKind Dual
  !_Identifier FldOutpParamDescr: !_TypeDual
    name: FldOutpParamDescr
    typeKind: !_TypeKind Dual
  !_Identifier FldOutpParamTypeDescr: !_TypeDual
    name: FldOutpParamTypeDescr
    typeKind: !_TypeKind Dual
  !_Identifier FldOutpPrntParam: !_TypeDual
    name: FldOutpPrntParam
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltArgDescrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltArgDescrDual
      type: !_DualBase
        dual: RefGnrcAltArgDescrDual
        typeArgs:
        - !_DualArg
          description: 'Test Descr'
          typeParam: type
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefGnrcAltArgDescrDual
        typeArgs:
        - !_DualArg
          description: 'Test Descr'
          typeParam: type
    name: GnrcAltArgDescrDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltArgDescrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltArgDescrInp
      type: !_InputBase
        input: RefGnrcAltArgDescrInp
        typeArgs:
        - !_InputArg
          description: 'Test Descr'
          typeParam: type
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefGnrcAltArgDescrInp
        typeArgs:
        - !_InputArg
          description: 'Test Descr'
          typeParam: type
    name: GnrcAltArgDescrInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltArgDescrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltArgDescrOutp
      type: !_OutputBase
        output: RefGnrcAltArgDescrOutp
        typeArgs:
        - !_OutputArg
          description: 'Test Descr'
          typeParam: type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefGnrcAltArgDescrOutp
        typeArgs:
        - !_OutputArg
          description: 'Test Descr'
          typeParam: type
    name: GnrcAltArgDescrOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltArgDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltArgDual
      type: !_DualBase
        dual: RefGnrcAltArgDual
        typeArgs:
        - !_DualArg
          typeParam: type
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefGnrcAltArgDual
        typeArgs:
        - !_DualArg
          typeParam: type
    name: GnrcAltArgDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltArgInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltArgInp
      type: !_InputBase
        input: RefGnrcAltArgInp
        typeArgs:
        - !_InputArg
          typeParam: type
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefGnrcAltArgInp
        typeArgs:
        - !_InputArg
          typeParam: type
    name: GnrcAltArgInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltArgOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltArgOutp
      type: !_OutputBase
        output: RefGnrcAltArgOutp
        typeArgs:
        - !_OutputArg
          typeParam: type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefGnrcAltArgOutp
        typeArgs:
        - !_OutputArg
          typeParam: type
    name: GnrcAltArgOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltDual
      type: !_DualBase
        typeParam: type
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: type
    name: GnrcAltDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltDualDual
      type: !_DualBase
        dual: RefGnrcAltDualDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefGnrcAltDualDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualDual
    name: GnrcAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltDualInp
      type: !_InputBase
        input: RefGnrcAltDualInp
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefGnrcAltDualInp
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualInp
    name: GnrcAltDualInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcAltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltDualOutp
      type: !_OutputBase
        output: RefGnrcAltDualOutp
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefGnrcAltDualOutp
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltDualOutp
    name: GnrcAltDualOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltInp
      type: !_InputBase
        typeParam: type
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: type
    name: GnrcAltInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltOutp
      type: !_OutputBase
        typeParam: type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: type
    name: GnrcAltOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcAltParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltParamDual
      type: !_DualBase
        dual: RefGnrcAltParamDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltParamDual
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefGnrcAltParamDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcAltParamDual
    name: GnrcAltParamDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltParamInp
      type: !_InputBase
        input: RefGnrcAltParamInp
        typeArgs:
        - !_InputArg
          input: AltGnrcAltParamInp
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefGnrcAltParamInp
        typeArgs:
        - !_InputArg
          input: AltGnrcAltParamInp
    name: GnrcAltParamInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcAltParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltParamOutp
      type: !_OutputBase
        output: RefGnrcAltParamOutp
        typeArgs:
        - !_OutputArg
          output: AltGnrcAltParamOutp
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefGnrcAltParamOutp
        typeArgs:
        - !_OutputArg
          output: AltGnrcAltParamOutp
    name: GnrcAltParamOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcAltSmplDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: GnrcAltSmplDual
      type: !_DualBase
        dual: RefGnrcAltSmplDual
        typeArgs:
        - !_DualArg
          dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: RefGnrcAltSmplDual
        typeArgs:
        - !_DualArg
          dual: String
    name: GnrcAltSmplDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcAltSmplInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: GnrcAltSmplInp
      type: !_InputBase
        input: RefGnrcAltSmplInp
        typeArgs:
        - !_InputArg
          input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: RefGnrcAltSmplInp
        typeArgs:
        - !_InputArg
          input: String
    name: GnrcAltSmplInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcAltSmplOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: GnrcAltSmplOutp
      type: !_OutputBase
        output: RefGnrcAltSmplOutp
        typeArgs:
        - !_OutputArg
          output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefGnrcAltSmplOutp
        typeArgs:
        - !_OutputArg
          output: String
    name: GnrcAltSmplOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcDescrDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: GnrcDescrDual
      type: !_DualBase
        typeParam: type
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: type
    name: GnrcDescrDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      description: 'Test Descr'
      name: type
  !_Identifier GnrcDescrInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: GnrcDescrInp
      type: !_InputBase
        typeParam: type
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: type
    name: GnrcDescrInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      description: 'Test Descr'
      name: type
  !_Identifier GnrcDescrOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: GnrcDescrOutp
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: GnrcDescrOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      description: 'Test Descr'
      name: type
  !_Identifier GnrcFieldArgDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: GnrcFieldArgDual
      type: !_DualBase
        dual: RefGnrcFieldArgDual
        typeArgs:
        - !_DualArg
          typeParam: type
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: RefGnrcFieldArgDual
        typeArgs:
        - !_DualArg
          typeParam: type
    name: GnrcFieldArgDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldArgInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: GnrcFieldArgInp
      type: !_InputBase
        input: RefGnrcFieldArgInp
        typeArgs:
        - !_InputArg
          typeParam: type
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: RefGnrcFieldArgInp
        typeArgs:
        - !_InputArg
          typeParam: type
    name: GnrcFieldArgInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldArgOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: GnrcFieldArgOutp
      type: !_OutputBase
        output: RefGnrcFieldArgOutp
        typeArgs:
        - !_OutputArg
          typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: RefGnrcFieldArgOutp
        typeArgs:
        - !_OutputArg
          typeParam: type
    name: GnrcFieldArgOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: GnrcFieldDual
      type: !_DualBase
        typeParam: type
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: type
    name: GnrcFieldDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldDualDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: GnrcFieldDualDual
      type: !_DualBase
        dual: RefGnrcFieldDualDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualDual
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: RefGnrcFieldDualDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualDual
    name: GnrcFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcFieldDualInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: GnrcFieldDualInp
      type: !_InputBase
        input: RefGnrcFieldDualInp
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualInp
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: RefGnrcFieldDualInp
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualInp
    name: GnrcFieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcFieldDualOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: GnrcFieldDualOutp
      type: !_OutputBase
        output: RefGnrcFieldDualOutp
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualOutp
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: RefGnrcFieldDualOutp
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldDualOutp
    name: GnrcFieldDualOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcFieldInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: GnrcFieldInp
      type: !_InputBase
        typeParam: type
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: type
    name: GnrcFieldInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: GnrcFieldOutp
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: GnrcFieldOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcFieldParamDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: GnrcFieldParamDual
      type: !_DualBase
        dual: RefGnrcFieldParamDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldParamDual
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: RefGnrcFieldParamDual
        typeArgs:
        - !_DualArg
          dual: AltGnrcFieldParamDual
    name: GnrcFieldParamDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcFieldParamInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: GnrcFieldParamInp
      type: !_InputBase
        input: RefGnrcFieldParamInp
        typeArgs:
        - !_InputArg
          input: AltGnrcFieldParamInp
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: RefGnrcFieldParamInp
        typeArgs:
        - !_InputArg
          input: AltGnrcFieldParamInp
    name: GnrcFieldParamInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcFieldParamOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: GnrcFieldParamOutp
      type: !_OutputBase
        output: RefGnrcFieldParamOutp
        typeArgs:
        - !_OutputArg
          output: AltGnrcFieldParamOutp
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: RefGnrcFieldParamOutp
        typeArgs:
        - !_OutputArg
          output: AltGnrcFieldParamOutp
    name: GnrcFieldParamOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntArgDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntArgDual
      type: !_DualBase
        typeParam: ref
    name: GnrcPrntArgDual
    parent: !_DualBase
      dual: RefGnrcPrntArgDual
      typeArgs:
      - !_DualArg
        typeParam: type
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntArgInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntArgInp
      type: !_InputBase
        typeParam: ref
    name: GnrcPrntArgInp
    parent: !_InputBase
      input: RefGnrcPrntArgInp
      typeArgs:
      - !_InputArg
        typeParam: type
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntArgOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntArgOutp
      type: !_OutputBase
        typeParam: ref
    name: GnrcPrntArgOutp
    parent: !_OutputBase
      output: RefGnrcPrntArgOutp
      typeArgs:
      - !_OutputArg
        typeParam: type
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntDescrDual: !_TypeDual
    name: GnrcPrntDescrDual
    parent: !_DualBase
      description: 'Parent comment'
      typeParam: type
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntDescrInp: !_TypeInput
    name: GnrcPrntDescrInp
    parent: !_InputBase
      description: 'Parent comment'
      typeParam: type
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntDescrOutp: !_TypeOutput
    name: GnrcPrntDescrOutp
    parent: !_OutputBase
      description: 'Parent comment'
      typeParam: type
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntDual: !_TypeDual
    name: GnrcPrntDual
    parent: !_DualBase
      typeParam: type
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntDualDual
      type: !_DualBase
        dual: AltGnrcPrntDualDual
    name: GnrcPrntDualDual
    parent: !_DualBase
      dual: RefGnrcPrntDualDual
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntDualInp
      type: !_DualBase
        dual: AltGnrcPrntDualInp
    name: GnrcPrntDualInp
    parent: !_InputBase
      input: RefGnrcPrntDualInp
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntDualOutp
      type: !_DualBase
        dual: AltGnrcPrntDualOutp
    name: GnrcPrntDualOutp
    parent: !_OutputBase
      output: RefGnrcPrntDualOutp
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntDualPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntDualPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntDualPrntDual
      type: !_DualBase
        dual: Number
    name: GnrcPrntDualPrntDual
    parent: !_DualBase
      dual: RefGnrcPrntDualPrntDual
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntDualPrntInp: !_TypeInput
    name: GnrcPrntDualPrntInp
    parent: !_InputBase
      input: RefGnrcPrntDualPrntInp
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualPrntInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntDualPrntOutp: !_TypeOutput
    name: GnrcPrntDualPrntOutp
    parent: !_OutputBase
      output: RefGnrcPrntDualPrntOutp
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntDualPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntEnumChildDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumChildDual
      type: !_DualBase
        dual: ParentGnrcPrntEnumChildDual
    name: GnrcPrntEnumChildDual
    parent: !_DualBase
      dual: FieldGnrcPrntEnumChildDual
      typeArgs:
      - !_DualArg
        dual: ParentGnrcPrntEnumChildDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntEnumChildInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumChildInp
      type: !_InputBase
        input: ParentGnrcPrntEnumChildInp
    name: GnrcPrntEnumChildInp
    parent: !_InputBase
      input: FieldGnrcPrntEnumChildInp
      typeArgs:
      - !_InputArg
        input: ParentGnrcPrntEnumChildInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntEnumChildOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumChildOutp
      type: !_OutputBase
        output: ParentGnrcPrntEnumChildOutp
    name: GnrcPrntEnumChildOutp
    parent: !_OutputBase
      output: FieldGnrcPrntEnumChildOutp
      typeArgs:
      - !_OutputArg
        output: ParentGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntEnumDomDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumDomDual
      type: !_DualBase
        dual: DomGnrcPrntEnumDomDual
    name: GnrcPrntEnumDomDual
    parent: !_DualBase
      dual: FieldGnrcPrntEnumDomDual
      typeArgs:
      - !_DualArg
        dual: DomGnrcPrntEnumDomDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntEnumDomInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumDomInp
      type: !_InputBase
        input: DomGnrcPrntEnumDomInp
    name: GnrcPrntEnumDomInp
    parent: !_InputBase
      input: FieldGnrcPrntEnumDomInp
      typeArgs:
      - !_InputArg
        input: DomGnrcPrntEnumDomInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntEnumDomOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumDomOutp
      type: !_OutputBase
        output: DomGnrcPrntEnumDomOutp
    name: GnrcPrntEnumDomOutp
    parent: !_OutputBase
      output: FieldGnrcPrntEnumDomOutp
      typeArgs:
      - !_OutputArg
        output: DomGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntEnumPrntDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntEnumPrntDual
      type: !_DualBase
        dual: EnumGnrcPrntEnumPrntDual
    name: GnrcPrntEnumPrntDual
    parent: !_DualBase
      dual: FieldGnrcPrntEnumPrntDual
      typeArgs:
      - !_DualArg
        dual: EnumGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntEnumPrntInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntEnumPrntInp
      type: !_InputBase
        input: EnumGnrcPrntEnumPrntInp
    name: GnrcPrntEnumPrntInp
    parent: !_InputBase
      input: FieldGnrcPrntEnumPrntInp
      typeArgs:
      - !_InputArg
        input: EnumGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntEnumPrntOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntEnumPrntOutp
      type: !_OutputBase
        output: EnumGnrcPrntEnumPrntOutp
    name: GnrcPrntEnumPrntOutp
    parent: !_OutputBase
      output: FieldGnrcPrntEnumPrntOutp
      typeArgs:
      - !_OutputArg
        output: EnumGnrcPrntEnumPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntInp: !_TypeInput
    name: GnrcPrntInp
    parent: !_InputBase
      typeParam: type
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntOutp: !_TypeOutput
    name: GnrcPrntOutp
    parent: !_OutputBase
      typeParam: type
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier GnrcPrntParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntParamDual
      type: !_DualBase
        dual: AltGnrcPrntParamDual
    name: GnrcPrntParamDual
    parent: !_DualBase
      dual: RefGnrcPrntParamDual
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntParamDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntParamInp
      type: !_InputBase
        input: AltGnrcPrntParamInp
    name: GnrcPrntParamInp
    parent: !_InputBase
      input: RefGnrcPrntParamInp
      typeArgs:
      - !_InputArg
        input: AltGnrcPrntParamInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntParamOutp
      type: !_OutputBase
        output: AltGnrcPrntParamOutp
    name: GnrcPrntParamOutp
    parent: !_OutputBase
      output: RefGnrcPrntParamOutp
      typeArgs:
      - !_OutputArg
        output: AltGnrcPrntParamOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntParamPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: AltGnrcPrntParamPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: AltGnrcPrntParamPrntDual
      type: !_DualBase
        dual: Number
    name: GnrcPrntParamPrntDual
    parent: !_DualBase
      dual: RefGnrcPrntParamPrntDual
      typeArgs:
      - !_DualArg
        dual: AltGnrcPrntParamPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntParamPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: AltGnrcPrntParamPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: alt
      object: AltGnrcPrntParamPrntInp
      type: !_InputBase
        input: Number
    name: GnrcPrntParamPrntInp
    parent: !_InputBase
      input: RefGnrcPrntParamPrntInp
      typeArgs:
      - !_InputArg
        input: AltGnrcPrntParamPrntInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntParamPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: AltGnrcPrntParamPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: alt
      object: AltGnrcPrntParamPrntOutp
      type: !_OutputBase
        output: Number
    name: GnrcPrntParamPrntOutp
    parent: !_OutputBase
      output: RefGnrcPrntParamPrntOutp
      typeArgs:
      - !_OutputArg
        output: AltGnrcPrntParamPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntSmplEnumDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntSmplEnumDual
      type: !_DualBase
        dual: EnumGnrcPrntSmplEnumDual
    name: GnrcPrntSmplEnumDual
    parent: !_DualBase
      dual: FieldGnrcPrntSmplEnumDual
      typeArgs:
      - !_DualArg
        dual: EnumGnrcPrntSmplEnumDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntSmplEnumInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntSmplEnumInp
      type: !_InputBase
        input: EnumGnrcPrntSmplEnumInp
    name: GnrcPrntSmplEnumInp
    parent: !_InputBase
      input: FieldGnrcPrntSmplEnumInp
      typeArgs:
      - !_InputArg
        input: EnumGnrcPrntSmplEnumInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntSmplEnumOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntSmplEnumOutp
      type: !_OutputBase
        output: EnumGnrcPrntSmplEnumOutp
    name: GnrcPrntSmplEnumOutp
    parent: !_OutputBase
      output: FieldGnrcPrntSmplEnumOutp
      typeArgs:
      - !_OutputArg
        output: EnumGnrcPrntSmplEnumOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcPrntStrDomDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: FieldGnrcPrntStrDomDual
      type: !_DualBase
        dual: DomGnrcPrntStrDomDual
    name: GnrcPrntStrDomDual
    parent: !_DualBase
      dual: FieldGnrcPrntStrDomDual
      typeArgs:
      - !_DualArg
        dual: DomGnrcPrntStrDomDual
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntStrDomInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: FieldGnrcPrntStrDomInp
      type: !_InputBase
        input: DomGnrcPrntStrDomInp
    name: GnrcPrntStrDomInp
    parent: !_InputBase
      input: FieldGnrcPrntStrDomInp
      typeArgs:
      - !_InputArg
        input: DomGnrcPrntStrDomInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntStrDomOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: FieldGnrcPrntStrDomOutp
      type: !_OutputBase
        output: DomGnrcPrntStrDomOutp
    name: GnrcPrntStrDomOutp
    parent: !_OutputBase
      output: FieldGnrcPrntStrDomOutp
      typeArgs:
      - !_OutputArg
        output: DomGnrcPrntStrDomOutp
    typeKind: !_TypeKind Output
  !_Identifier InDrctParam: !_TypeInput
    name: InDrctParam
    typeKind: !_TypeKind Input
  !_Identifier InDrctParamDict: !_TypeInput
    name: InDrctParamDict
    typeKind: !_TypeKind Input
  !_Identifier InDrctParamIn: !_TypeInput
    name: InDrctParamIn
    typeKind: !_TypeKind Input
  !_Identifier InDrctParamList: !_TypeInput
    name: InDrctParamList
    typeKind: !_TypeKind Input
  !_Identifier InDrctParamOpt: !_TypeInput
    name: InDrctParamOpt
    typeKind: !_TypeKind Input
  !_Identifier InOutpDescrParam: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpDescrParam
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpDescrParam
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpDescrParam
    typeKind: !_TypeKind Input
  !_Identifier InOutpParam: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpParam
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpParam
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpParam
    typeKind: !_TypeKind Input
  !_Identifier InOutpParamDescr: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpParamDescr
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpParamDescr
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpParamDescr
    typeKind: !_TypeKind Input
  !_Identifier InOutpParamModDmn: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpParamModDmn
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpParamModDmn
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpParamModDmn
    typeKind: !_TypeKind Input
  !_Identifier InOutpParamModParam: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpParamModParam
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpParamModParam
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpParamModParam
    typeKind: !_TypeKind Input
  !_Identifier InOutpParamTypeDescr: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpParamTypeDescr
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpParamTypeDescr
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpParamTypeDescr
    typeKind: !_TypeKind Input
  !_Identifier InOutpPrntParam: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: InOutpPrntParam
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: param
      object: InOutpPrntParam
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: param
      type: !_InputBase
        input: Number
    name: InOutpPrntParam
    typeKind: !_TypeKind Input
  !_Identifier InpFieldDescrNmbr: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: 42
      description: 'Test Descr'
      name: field
      object: InpFieldDescrNmbr
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      default: 42
      description: 'Test Descr'
      name: field
      type: !_InputBase
        input: Number
    name: InpFieldDescrNmbr
    typeKind: !_TypeKind Input
  !_Identifier InpFieldEnum: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: inpFieldEnum
      name: field
      object: InpFieldEnum
      type: !_InputBase
        input: EnumInpFieldEnum
    fields:
    - !_InputField
      default: inpFieldEnum
      name: field
      type: !_InputBase
        input: EnumInpFieldEnum
    name: InpFieldEnum
    typeKind: !_TypeKind Input
  !_Identifier InpFieldNmbr: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: 42
      name: field
      object: InpFieldNmbr
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      default: 42
      name: field
      type: !_InputBase
        input: Number
    name: InpFieldNmbr
    typeKind: !_TypeKind Input
  !_Identifier InpFieldNmbrDescr: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: 42
      name: field
      object: InpFieldNmbrDescr
      type: !_InputBase
        description: 'Test Descr'
        input: Number
    fields:
    - !_InputField
      default: 42
      name: field
      type: !_InputBase
        description: 'Test Descr'
        input: Number
    name: InpFieldNmbrDescr
    typeKind: !_TypeKind Input
  !_Identifier InpFieldNull: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: !Null null
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: field
      object: InpFieldNull
      type: !_DualBase
        dual: FldInpFieldNull
    fields:
    - !_InputField
      default: !Null null
      modifiers: [!_Modifier {modifierKind: !_ModifierKind Opt}]
      name: field
      type: !_DualBase
        dual: FldInpFieldNull
    name: InpFieldNull
    typeKind: !_TypeKind Input
  !_Identifier InpFieldStr: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      default: 'default'
      name: field
      object: InpFieldStr
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      default: 'default'
      name: field
      type: !_InputBase
        input: String
    name: InpFieldStr
    typeKind: !_TypeKind Input
  !_Identifier JustOutpCnstDomEnum: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: JustOutpCnstDomEnum
      exclude: false
      value: !_EnumValue
        label: outpCnstDomEnum
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpCnstDomEnum
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: outpCnstDomEnum
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpCnstDomEnum
    name: JustOutpCnstDomEnum
    typeKind: !_TypeKind Domain
  !_Identifier ObjAliasDual: !_TypeDual
    aliases: [Dual1, Dual2]
    name: ObjAliasDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjAliasInp: !_TypeInput
    aliases: [Input1, Input2]
    name: ObjAliasInp
    typeKind: !_TypeKind Input
  !_Identifier ObjAliasOutp: !_TypeOutput
    aliases: [Output1, Output2]
    name: ObjAliasOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: ObjAltDual
      type: !_DualBase
        dual: ObjAltDualType
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: ObjAltDualType
    name: ObjAltDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjAltDualType: !_TypeDual
    name: ObjAltDualType
    typeKind: !_TypeKind Dual
  !_Identifier ObjAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: ObjAltInp
      type: !_InputBase
        input: ObjAltInpType
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: ObjAltInpType
    name: ObjAltInp
    typeKind: !_TypeKind Input
  !_Identifier ObjAltInpType: !_TypeInput
    name: ObjAltInpType
    typeKind: !_TypeKind Input
  !_Identifier ObjAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: ObjAltOutp
      type: !_OutputBase
        output: ObjAltOutpType
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: ObjAltOutpType
    name: ObjAltOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjAltOutpType: !_TypeOutput
    name: ObjAltOutpType
    typeKind: !_TypeKind Output
  !_Identifier ObjCnstDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: ObjCnstDual
      type: !_DualBase
        typeParam: type
    - !_ObjectFor(_DualField)
      name: str
      object: ObjCnstDual
      type: !_DualBase
        typeParam: type
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: type
    - !_DualField
      name: str
      type: !_DualBase
        typeParam: type
    name: ObjCnstDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjCnstInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: ObjCnstInp
      type: !_InputBase
        typeParam: type
    - !_ObjectFor(_InputField)
      name: str
      object: ObjCnstInp
      type: !_InputBase
        typeParam: type
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: type
    - !_InputField
      name: str
      type: !_InputBase
        typeParam: type
    name: ObjCnstInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjCnstOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: ObjCnstOutp
      type: !_OutputBase
        typeParam: type
    - !_ObjectFor(_OutputField)
      name: str
      object: ObjCnstOutp
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    - !_OutputField
      name: str
      type: !_OutputBase
        typeParam: type
    name: ObjCnstOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjDual: !_TypeDual
    name: ObjDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjDualAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: ObjDualAltDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: ObjDualAltDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: ObjDualAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjDualAltDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: ObjDualAltDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: ObjDualAltDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: ObjDualAltDualInp
    typeKind: !_TypeKind Dual
  !_Identifier ObjDualAltDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: ObjDualAltDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: alt
      object: ObjDualAltDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: alt
      type: !_DualBase
        dual: Number
    name: ObjDualAltDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier ObjFieldAliasDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      aliases: [field1, field2]
      name: field
      object: ObjFieldAliasDual
      type: !_DualBase
        dual: FldObjFieldAliasDual
    fields:
    - !_DualField
      aliases: [field1, field2]
      name: field
      type: !_DualBase
        dual: FldObjFieldAliasDual
    name: ObjFieldAliasDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjFieldAliasInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      aliases: [field1, field2]
      name: field
      object: ObjFieldAliasInp
      type: !_InputBase
        input: FldObjFieldAliasInp
    fields:
    - !_InputField
      aliases: [field1, field2]
      name: field
      type: !_InputBase
        input: FldObjFieldAliasInp
    name: ObjFieldAliasInp
    typeKind: !_TypeKind Input
  !_Identifier ObjFieldAliasOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      aliases: [field1, field2]
      name: field
      object: ObjFieldAliasOutp
      type: !_OutputBase
        output: FldObjFieldAliasOutp
    fields:
    - !_OutputField
      aliases: [field1, field2]
      name: field
      type: !_OutputBase
        output: FldObjFieldAliasOutp
    name: ObjFieldAliasOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjFieldDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: ObjFieldDual
      type: !_DualBase
        dual: FldObjFieldDual
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: FldObjFieldDual
    name: ObjFieldDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjFieldInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: ObjFieldInp
      type: !_InputBase
        input: FldObjFieldInp
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: FldObjFieldInp
    name: ObjFieldInp
    typeKind: !_TypeKind Input
  !_Identifier ObjFieldOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: ObjFieldOutp
      type: !_OutputBase
        output: FldObjFieldOutp
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: FldObjFieldOutp
    name: ObjFieldOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjFieldTypeAliasDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: ObjFieldTypeAliasDual
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: String
    name: ObjFieldTypeAliasDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjFieldTypeAliasInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: ObjFieldTypeAliasInp
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: String
    name: ObjFieldTypeAliasInp
    typeKind: !_TypeKind Input
  !_Identifier ObjFieldTypeAliasOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: ObjFieldTypeAliasOutp
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: String
    name: ObjFieldTypeAliasOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjInp: !_TypeInput
    name: ObjInp
    typeKind: !_TypeKind Input
  !_Identifier ObjOutp: !_TypeOutput
    name: ObjOutp
    typeKind: !_TypeKind Output
  !_Identifier ObjParamCnstDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: test
      object: ObjParamCnstDual
      type: !_DualBase
        typeParam: test
    - !_ObjectFor(_DualField)
      name: type
      object: ObjParamCnstDual
      type: !_DualBase
        typeParam: test
    fields:
    - !_DualField
      name: test
      type: !_DualBase
        typeParam: test
    - !_DualField
      name: type
      type: !_DualBase
        typeParam: test
    name: ObjParamCnstDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamCnstInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: test
      object: ObjParamCnstInp
      type: !_InputBase
        typeParam: test
    - !_ObjectFor(_InputField)
      name: type
      object: ObjParamCnstInp
      type: !_InputBase
        typeParam: test
    fields:
    - !_InputField
      name: test
      type: !_InputBase
        typeParam: test
    - !_InputField
      name: type
      type: !_InputBase
        typeParam: test
    name: ObjParamCnstInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamCnstOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: test
      object: ObjParamCnstOutp
      type: !_OutputBase
        typeParam: test
    - !_ObjectFor(_OutputField)
      name: type
      object: ObjParamCnstOutp
      type: !_OutputBase
        typeParam: test
    fields:
    - !_OutputField
      name: test
      type: !_OutputBase
        typeParam: test
    - !_OutputField
      name: type
      type: !_OutputBase
        typeParam: test
    name: ObjParamCnstOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: test
      object: ObjParamDual
      type: !_DualBase
        typeParam: test
    - !_ObjectFor(_DualField)
      name: type
      object: ObjParamDual
      type: !_DualBase
        typeParam: type
    fields:
    - !_DualField
      name: test
      type: !_DualBase
        typeParam: test
    - !_DualField
      name: type
      type: !_DualBase
        typeParam: type
    name: ObjParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjParamDupDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: test
      object: ObjParamDupDual
      type: !_DualBase
        typeParam: test
    - !_ObjectFor(_DualField)
      name: type
      object: ObjParamDupDual
      type: !_DualBase
        typeParam: test
    fields:
    - !_DualField
      name: test
      type: !_DualBase
        typeParam: test
    - !_DualField
      name: type
      type: !_DualBase
        typeParam: test
    name: ObjParamDupDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamDupInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: test
      object: ObjParamDupInp
      type: !_InputBase
        typeParam: test
    - !_ObjectFor(_InputField)
      name: type
      object: ObjParamDupInp
      type: !_InputBase
        typeParam: test
    fields:
    - !_InputField
      name: test
      type: !_InputBase
        typeParam: test
    - !_InputField
      name: type
      type: !_InputBase
        typeParam: test
    name: ObjParamDupInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamDupOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: test
      object: ObjParamDupOutp
      type: !_OutputBase
        typeParam: test
    - !_ObjectFor(_OutputField)
      name: type
      object: ObjParamDupOutp
      type: !_OutputBase
        typeParam: test
    fields:
    - !_OutputField
      name: test
      type: !_OutputBase
        typeParam: test
    - !_OutputField
      name: type
      type: !_OutputBase
        typeParam: test
    name: ObjParamDupOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
  !_Identifier ObjParamInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: test
      object: ObjParamInp
      type: !_InputBase
        typeParam: test
    - !_ObjectFor(_InputField)
      name: type
      object: ObjParamInp
      type: !_InputBase
        typeParam: type
    fields:
    - !_InputField
      name: test
      type: !_InputBase
        typeParam: test
    - !_InputField
      name: type
      type: !_InputBase
        typeParam: type
    name: ObjParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjParamOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: test
      object: ObjParamOutp
      type: !_OutputBase
        typeParam: test
    - !_ObjectFor(_OutputField)
      name: type
      object: ObjParamOutp
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: test
      type: !_OutputBase
        typeParam: test
    - !_OutputField
      name: type
      type: !_OutputBase
        typeParam: type
    name: ObjParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: test
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: type
  !_Identifier ObjPrntDual: !_TypeDual
    name: ObjPrntDual
    parent: !_DualBase
      dual: RefObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjPrntInp: !_TypeInput
    name: ObjPrntInp
    parent: !_InputBase
      input: RefObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier ObjPrntOutp: !_TypeOutput
    name: ObjPrntOutp
    parent: !_OutputBase
      output: RefObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier OutpCnstDomEnum: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpCnstDomEnum
      type: !_OutputBase
        output: RefOutpCnstDomEnum
        typeArgs:
        - !_OutputArg
          output: outpCnstDomEnum
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpCnstDomEnum
        typeArgs:
        - !_OutputArg
          output: outpCnstDomEnum
    name: OutpCnstDomEnum
    typeKind: !_TypeKind Output
  !_Identifier OutpCnstEnum: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpCnstEnum
      type: !_OutputBase
        output: RefOutpCnstEnum
        typeArgs:
        - !_OutputArg
          output: outpCnstEnum
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpCnstEnum
        typeArgs:
        - !_OutputArg
          output: outpCnstEnum
    name: OutpCnstEnum
    typeKind: !_TypeKind Output
  !_Identifier OutpCnstEnumPrnt: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpCnstEnumPrnt
      type: !_OutputBase
        output: RefOutpCnstEnumPrnt
        typeArgs:
        - !_OutputArg
          output: outpCnstEnumPrnt
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpCnstEnumPrnt
        typeArgs:
        - !_OutputArg
          output: outpCnstEnumPrnt
    name: OutpCnstEnumPrnt
    typeKind: !_TypeKind Output
  !_Identifier OutpCnstPrntEnum: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpCnstPrntEnum
      type: !_OutputBase
        output: RefOutpCnstPrntEnum
        typeArgs:
        - !_OutputArg
          output: parentOutpCnstPrntEnum
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpCnstPrntEnum
        typeArgs:
        - !_OutputArg
          output: parentOutpCnstPrntEnum
    name: OutpCnstPrntEnum
    typeKind: !_TypeKind Output
  !_Identifier OutpDescrParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      description: 'Test Descr'
      name: field
      object: OutpDescrParam
      parameters:
      - !_InputParam
        input: InOutpDescrParam
      type: !_DualBase
        dual: FldOutpDescrParam
    fields:
    - !_OutputField
      description: 'Test Descr'
      name: field
      parameters:
      - !_InputParam
        input: InOutpDescrParam
      type: !_DualBase
        dual: FldOutpDescrParam
    name: OutpDescrParam
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      field: field
      label: outpFieldEnum
      object: OutpFieldEnum
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldEnum
    fields:
    - !_OutputEnum
      field: field
      label: outpFieldEnum
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldEnum
    name: OutpFieldEnum
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldEnumAlias: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      field: field
      label: true
      object: OutpFieldEnumAlias
      typeKind: !_SimpleKind Enum
      typeName: Boolean
    fields:
    - !_OutputEnum
      field: field
      label: true
      typeKind: !_SimpleKind Enum
      typeName: Boolean
    name: OutpFieldEnumAlias
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldEnumPrnt: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      field: field
      label: prnt_outpFieldEnumPrnt
      object: OutpFieldEnumPrnt
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldEnumPrnt
    fields:
    - !_OutputEnum
      field: field
      label: prnt_outpFieldEnumPrnt
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldEnumPrnt
    name: OutpFieldEnumPrnt
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldEnumValue: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      field: field
      label: true
      object: OutpFieldEnumValue
      typeKind: !_SimpleKind Enum
      typeName: Boolean
    fields:
    - !_OutputEnum
      field: field
      label: true
      typeKind: !_SimpleKind Enum
      typeName: Boolean
    name: OutpFieldEnumValue
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpFieldParam
      parameters:
      - !_InputParam
        input: OutpFieldParam1
      - !_InputParam
        input: OutpFieldParam2
      type: !_DualBase
        dual: FldOutpFieldParam
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: OutpFieldParam1
      - !_InputParam
        input: OutpFieldParam2
      type: !_DualBase
        dual: FldOutpFieldParam
    name: OutpFieldParam
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldParam1: !_TypeInput
    name: OutpFieldParam1
    typeKind: !_TypeKind Input
  !_Identifier OutpFieldParam2: !_TypeInput
    name: OutpFieldParam2
    typeKind: !_TypeKind Input
  !_Identifier OutpFieldValue: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      field: field
      label: outpFieldValue
      object: OutpFieldValue
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldValue
    fields:
    - !_OutputEnum
      field: field
      label: outpFieldValue
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldValue
    name: OutpFieldValue
    typeKind: !_TypeKind Output
  !_Identifier OutpFieldValueDescr: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      description: 'Test Descr'
      field: field
      label: outpFieldValueDescr
      object: OutpFieldValueDescr
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldValueDescr
    fields:
    - !_OutputEnum
      description: 'Test Descr'
      field: field
      label: outpFieldValueDescr
      typeKind: !_SimpleKind Enum
      typeName: EnumOutpFieldValueDescr
    name: OutpFieldValueDescr
    typeKind: !_TypeKind Output
  !_Identifier OutpGnrcEnum: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpGnrcEnum
      type: !_OutputBase
        output: RefOutpGnrcEnum
        typeArgs:
        - !_OutputArg
          label: outpGnrcEnum
          typeKind: !_SimpleKind Enum
          typeName: EnumOutpGnrcEnum
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpGnrcEnum
        typeArgs:
        - !_OutputArg
          label: outpGnrcEnum
          typeKind: !_SimpleKind Enum
          typeName: EnumOutpGnrcEnum
    name: OutpGnrcEnum
    typeKind: !_TypeKind Output
  !_Identifier OutpGnrcValue: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpGnrcValue
      type: !_OutputBase
        output: RefOutpGnrcValue
        typeArgs:
        - !_OutputArg
          output: outpGnrcValue
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpGnrcValue
        typeArgs:
        - !_OutputArg
          output: outpGnrcValue
    name: OutpGnrcValue
    typeKind: !_TypeKind Output
  !_Identifier OutpParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpParam
      parameters:
      - !_InputParam
        input: InOutpParam
      type: !_DualBase
        dual: FldOutpParam
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: InOutpParam
      type: !_DualBase
        dual: FldOutpParam
    name: OutpParam
    typeKind: !_TypeKind Output
  !_Identifier OutpParamDescr: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpParamDescr
      parameters:
      - !_InputParam
        description: 'Test Descr'
        input: InOutpParamDescr
      type: !_DualBase
        dual: FldOutpParamDescr
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        description: 'Test Descr'
        input: InOutpParamDescr
      type: !_DualBase
        dual: FldOutpParamDescr
    name: OutpParamDescr
    typeKind: !_TypeKind Output
  !_Identifier OutpParamModDmn: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpParamModDmn
      parameters:
      - !_InputParam
        input: InOutpParamModDmn
        modifiers: [!_ModifierDictionary {by: DomOutpParamModDmn,
            modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Domain}]
      type: !_OutputBase
        output: DomOutpParamModDmn
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: InOutpParamModDmn
        modifiers: [!_ModifierDictionary {by: DomOutpParamModDmn,
            modifierKind: !_ModifierKind Dict, typeKind: !_SimpleKind Domain}]
      type: !_OutputBase
        output: DomOutpParamModDmn
    name: OutpParamModDmn
    typeKind: !_TypeKind Output
  !_Identifier OutpParamModParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpParamModParam
      parameters:
      - !_InputParam
        input: InOutpParamModParam
        modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      type: !_OutputBase
        output: DomOutpParamModParam
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: InOutpParamModParam
        modifiers: [!_ModifierTypeParam {by: mod, modifierKind: !_ModifierKind Param}]
      type: !_OutputBase
        output: DomOutpParamModParam
    name: OutpParamModParam
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier OutpParamTypeDescr: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpParamTypeDescr
      parameters:
      - !_InputParam
        input: InOutpParamTypeDescr
      type: !_DualBase
        description: 'Test Descr'
        dual: FldOutpParamTypeDescr
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: InOutpParamTypeDescr
      type: !_DualBase
        description: 'Test Descr'
        dual: FldOutpParamTypeDescr
    name: OutpParamTypeDescr
    typeKind: !_TypeKind Output
  !_Identifier OutpPrntGnrc: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: OutpPrntGnrc
      type: !_OutputBase
        output: RefOutpPrntGnrc
        typeArgs:
        - !_OutputArg
          label: prnt_outpPrntGnrc
          typeKind: !_SimpleKind Enum
          typeName: EnumOutpPrntGnrc
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: RefOutpPrntGnrc
        typeArgs:
        - !_OutputArg
          label: prnt_outpPrntGnrc
          typeKind: !_SimpleKind Enum
          typeName: EnumOutpPrntGnrc
    name: OutpPrntGnrc
    typeKind: !_TypeKind Output
  !_Identifier OutpPrntParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: PrntOutpPrntParam
      parameters:
      - !_InputParam
        input: PrntOutpPrntParamIn
      type: !_DualBase
        dual: FldOutpPrntParam
    - !_ObjectFor(_OutputField)
      name: field
      object: OutpPrntParam
      parameters:
      - !_InputParam
        input: InOutpPrntParam
      type: !_DualBase
        dual: FldOutpPrntParam
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: InOutpPrntParam
      type: !_DualBase
        dual: FldOutpPrntParam
    name: OutpPrntParam
    parent: !_OutputBase
      output: PrntOutpPrntParam
    typeKind: !_TypeKind Output
  !_Identifier ParentGnrcPrntEnumChildDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildDual
      name: gnrcPrntEnumChildDualParent
    items:
    - !_Aliased
      name: gnrcPrntEnumChildDualParent
    name: ParentGnrcPrntEnumChildDual
    typeKind: !_TypeKind Enum
  !_Identifier ParentGnrcPrntEnumChildInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildInp
      name: gnrcPrntEnumChildInpParent
    items:
    - !_Aliased
      name: gnrcPrntEnumChildInpParent
    name: ParentGnrcPrntEnumChildInp
    typeKind: !_TypeKind Enum
  !_Identifier ParentGnrcPrntEnumChildOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumChildOutp
      name: gnrcPrntEnumChildOutpParent
    items:
    - !_Aliased
      name: gnrcPrntEnumChildOutpParent
    name: ParentGnrcPrntEnumChildOutp
    typeKind: !_TypeKind Enum
  !_Identifier ParentGnrcPrntEnumPrntDual: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntDual
      name: gnrcPrntEnumPrntDualParent
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntDualParent
    name: ParentGnrcPrntEnumPrntDual
    typeKind: !_TypeKind Enum
  !_Identifier ParentGnrcPrntEnumPrntInp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntInp
      name: gnrcPrntEnumPrntInpParent
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntInpParent
    name: ParentGnrcPrntEnumPrntInp
    typeKind: !_TypeKind Enum
  !_Identifier ParentGnrcPrntEnumPrntOutp: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentGnrcPrntEnumPrntOutp
      name: gnrcPrntEnumPrntOutpParent
    items:
    - !_Aliased
      name: gnrcPrntEnumPrntOutpParent
    name: ParentGnrcPrntEnumPrntOutp
    typeKind: !_TypeKind Enum
  !_Identifier ParentOutpCnstEnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentOutpCnstEnumPrnt
      name: parentOutpCnstEnumPrnt
    items:
    - !_Aliased
      name: parentOutpCnstEnumPrnt
    name: ParentOutpCnstEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier ParentOutpCnstPrntEnum: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: ParentOutpCnstPrntEnum
      name: parentOutpCnstPrntEnum
    items:
    - !_Aliased
      name: parentOutpCnstPrntEnum
    name: ParentOutpCnstPrntEnum
    typeKind: !_TypeKind Enum
  !_Identifier PrntAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntAltDual
      type: !_DualBase
        dual: String
    - !_ObjectFor(_DualAlternate)
      object: PrntAltDual
      type: !_DualBase
        dual: Number
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntAltDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: Number
    name: PrntAltDual
    parent: !_DualBase
      dual: RefPrntAltDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntAltInp
      type: !_InputBase
        input: String
    - !_ObjectFor(_InputAlternate)
      object: PrntAltInp
      type: !_InputBase
        input: Number
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntAltInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: Number
    name: PrntAltInp
    parent: !_InputBase
      input: RefPrntAltInp
    typeKind: !_TypeKind Input
  !_Identifier PrntAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntAltOutp
      type: !_OutputBase
        output: String
    - !_ObjectFor(_OutputAlternate)
      object: PrntAltOutp
      type: !_OutputBase
        output: Number
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntAltOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: Number
    name: PrntAltOutp
    parent: !_OutputBase
      output: RefPrntAltOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntCnstAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstAltDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualInp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstAltDualInp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstAltDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltDualOutp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstAltDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstAltObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstAltObjDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstAltObjDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstAltObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstAltObjInp
      type: !_InputBase
        input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    name: PrntCnstAltObjInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstAltObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstAltObjOutp
      type: !_OutputBase
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    name: PrntCnstAltObjOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntCnstFieldDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstFieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstFieldDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualInp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstFieldDualInp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstFieldDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldDualOutp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstFieldDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstFieldObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstFieldObjDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstFieldObjDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstFieldObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstFieldObjInp
      type: !_InputBase
        input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    name: PrntCnstFieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstFieldObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstFieldObjOutp
      type: !_OutputBase
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    name: PrntCnstFieldObjOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntCnstPrntDualPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstPrntDualPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstPrntDualPrntInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntInp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstPrntDualPrntInp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstPrntDualPrntOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntDualPrntOutp
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstPrntDualPrntOutp
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstPrntObjPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: PrntCnstPrntObjPrntDual
      type: !_DualBase
        dual: String
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    name: PrntCnstPrntObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstPrntObjPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntCnstPrntObjPrntInp
      type: !_InputBase
        input: String
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    name: PrntCnstPrntObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstPrntObjPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: PrntCnstPrntObjPrntOutp
      type: !_OutputBase
        output: String
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    name: PrntCnstPrntObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntDescrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDescrDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDescrDual
      type: !_DualBase
        dual: Number
    name: PrntDescrDual
    parent: !_DualBase
      description: 'Test Descr'
      dual: RefPrntDescrDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntDescrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntDescrInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntDescrInp
      type: !_InputBase
        input: Number
    name: PrntDescrInp
    parent: !_InputBase
      description: 'Test Descr'
      input: RefPrntDescrInp
    typeKind: !_TypeKind Input
  !_Identifier PrntDescrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntDescrOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntDescrOutp
      type: !_OutputBase
        output: Number
    name: PrntDescrOutp
    parent: !_OutputBase
      description: 'Test Descr'
      output: RefPrntDescrOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntDmnBoolPrnt: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: PrntDmnBoolPrnt
      exclude: false
      value: true
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: true
    name: PrntDmnBoolPrnt
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnBoolPrntDescr: !_DomainBoolean
    allItems:
    - !_DomainItem(_DomainTrueFalse)
      domain: PrntDmnBoolPrntDescr
      exclude: false
      value: true
    domainKind: !_DomainKind Boolean
    items:
    - !_DomainTrueFalse
      exclude: false
      value: true
    name: PrntDmnBoolPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnEnumAllPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntDmnEnumAllPrnt
      name: prnt_dmnEnumAllPrnt
    items:
    - !_Aliased
      name: prnt_dmnEnumAllPrnt
    name: PrntDmnEnumAllPrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntDmnEnumPrnt: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: PrntDmnEnumPrnt
      exclude: false
      value: !_EnumValue
        label: prnt_dmnEnumPrnt
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumPrnt
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: prnt_dmnEnumPrnt
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumPrnt
    name: PrntDmnEnumPrnt
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnEnumPrntDescr: !_DomainEnum
    allItems:
    - !_DomainItem(_DomainLabel)
      domain: PrntDmnEnumPrntDescr
      exclude: false
      value: !_EnumValue
        label: prnt_dmnEnumPrntDescr
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumPrntDescr
    domainKind: !_DomainKind Enum
    items:
    - !_DomainLabel
      exclude: false
      value: !_EnumValue
        label: prnt_dmnEnumPrntDescr
        typeKind: !_SimpleKind Enum
        typeName: EnumDmnEnumPrntDescr
    name: PrntDmnEnumPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnEnumUnqPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntDmnEnumUnqPrnt
      name: dmnEnumUnqPrnt
    - !_EnumLabel
      enum: PrntDmnEnumUnqPrnt
      name: prnt_dmnEnumUnqPrnt
    items:
    - !_Aliased
      name: dmnEnumUnqPrnt
    - !_Aliased
      name: prnt_dmnEnumUnqPrnt
    name: PrntDmnEnumUnqPrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntDmnEnumValuePrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntDmnEnumValuePrnt
      name: prnt_dmnEnumValuePrnt
    items:
    - !_Aliased
      name: prnt_dmnEnumValuePrnt
    name: PrntDmnEnumValuePrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntDmnNmbrPrnt: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: PrntDmnNmbrPrnt
      exclude: false
      to: 2
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      to: 2
    name: PrntDmnNmbrPrnt
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnNmbrPrntDescr: !_DomainNumber
    allItems:
    - !_DomainItem(_DomainRange)
      domain: PrntDmnNmbrPrntDescr
      exclude: false
      to: 2
    domainKind: !_DomainKind Number
    items:
    - !_DomainRange
      exclude: false
      to: 2
    name: PrntDmnNmbrPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnStrPrnt: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: PrntDmnStrPrnt
      exclude: false
      pattern: b+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: b+
    name: PrntDmnStrPrnt
    typeKind: !_TypeKind Domain
  !_Identifier PrntDmnStrPrntDescr: !_DomainString
    allItems:
    - !_DomainItem(_DomainRegex)
      domain: PrntDmnStrPrntDescr
      exclude: false
      pattern: b+
    domainKind: !_DomainKind String
    items:
    - !_DomainRegex
      exclude: false
      pattern: b+
    name: PrntDmnStrPrntDescr
    typeKind: !_TypeKind Domain
  !_Identifier PrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDual
      type: !_DualBase
        dual: Number
    name: PrntDual
    parent: !_DualBase
      dual: RefPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualDual
      type: !_DualBase
        dual: Number
    name: PrntDualDual
    parent: !_DualBase
      dual: RefPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualInp
      type: !_DualBase
        dual: Number
    name: PrntDualInp
    parent: !_DualBase
      dual: RefPrntDualInp
    typeKind: !_TypeKind Input
  !_Identifier PrntDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualOutp
      type: !_DualBase
        dual: Number
    name: PrntDualOutp
    parent: !_DualBase
      dual: RefPrntDualOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntEnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntEnumPrnt
      name: prnt_enumPrnt
    items:
    - !_Aliased
      name: prnt_enumPrnt
    name: PrntEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntEnumPrntAlias: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntEnumPrntAlias
      name: prnt_enumPrntAlias
    items:
    - !_Aliased
      name: prnt_enumPrntAlias
    name: PrntEnumPrntAlias
    typeKind: !_TypeKind Enum
  !_Identifier PrntEnumPrntDescr: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntEnumPrntDescr
      name: prnt_enumPrntDescr
    items:
    - !_Aliased
      name: prnt_enumPrntDescr
    name: PrntEnumPrntDescr
    typeKind: !_TypeKind Enum
  !_Identifier PrntEnumPrntDup: !_TypeEnum
    allItems:
    - !_EnumLabel
      aliases: [enumPrntDup]
      enum: PrntEnumPrntDup
      name: prnt_enumPrntDup
    items:
    - !_Aliased
      aliases: [enumPrntDup]
      name: prnt_enumPrntDup
    name: PrntEnumPrntDup
    typeKind: !_TypeKind Enum
  !_Identifier PrntEnumSamePrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntEnumSamePrnt
      name: prnt_enumSamePrnt
    items:
    - !_Aliased
      name: prnt_enumSamePrnt
    name: PrntEnumSamePrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntFieldDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntFieldDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntFieldDual
      type: !_DualBase
        dual: Number
    - !_ObjectFor(_DualField)
      name: field
      object: PrntFieldDual
      type: !_DualBase
        dual: Number
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        dual: Number
    name: PrntFieldDual
    parent: !_DualBase
      dual: RefPrntFieldDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntFieldInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntFieldInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntFieldInp
      type: !_InputBase
        input: Number
    - !_ObjectFor(_InputField)
      name: field
      object: PrntFieldInp
      type: !_InputBase
        input: Number
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        input: Number
    name: PrntFieldInp
    parent: !_InputBase
      input: RefPrntFieldInp
    typeKind: !_TypeKind Input
  !_Identifier PrntFieldOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntFieldOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntFieldOutp
      type: !_OutputBase
        output: Number
    - !_ObjectFor(_OutputField)
      name: field
      object: PrntFieldOutp
      type: !_OutputBase
        output: Number
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        output: Number
    name: PrntFieldOutp
    parent: !_OutputBase
      output: RefPrntFieldOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntInp
      type: !_InputBase
        input: Number
    name: PrntInp
    parent: !_InputBase
      input: RefPrntInp
    typeKind: !_TypeKind Input
  !_Identifier PrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntOutp
      type: !_OutputBase
        output: Number
    name: PrntOutp
    parent: !_OutputBase
      output: RefPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier PrntOutpFieldEnumPrnt: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntOutpFieldEnumPrnt
      name: prnt_outpFieldEnumPrnt
    items:
    - !_Aliased
      name: prnt_outpFieldEnumPrnt
    name: PrntOutpFieldEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntOutpPrntGnrc: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: PrntOutpPrntGnrc
      name: prnt_outpPrntGnrc
    items:
    - !_Aliased
      name: prnt_outpPrntGnrc
    name: PrntOutpPrntGnrc
    typeKind: !_TypeKind Enum
  !_Identifier PrntOutpPrntParam: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: PrntOutpPrntParam
      parameters:
      - !_InputParam
        input: PrntOutpPrntParamIn
      type: !_DualBase
        dual: FldOutpPrntParam
    fields:
    - !_OutputField
      name: field
      parameters:
      - !_InputParam
        input: PrntOutpPrntParamIn
      type: !_DualBase
        dual: FldOutpPrntParam
    name: PrntOutpPrntParam
    typeKind: !_TypeKind Output
  !_Identifier PrntOutpPrntParamIn: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: PrntOutpPrntParamIn
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: PrntOutpPrntParamIn
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: parent
      type: !_InputBase
        input: Number
    name: PrntOutpPrntParamIn
    typeKind: !_TypeKind Input
  !_Identifier PrntParamDiffDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntParamDiffDual
      type: !_DualBase
        typeParam: b
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: PrntParamDiffDual
      type: !_DualBase
        typeParam: a
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: a
    name: PrntParamDiffDual
    parent: !_DualBase
      dual: RefPrntParamDiffDual
      typeArgs:
      - !_DualArg
        typeParam: a
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntParamDiffInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntParamDiffInp
      type: !_InputBase
        typeParam: b
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: PrntParamDiffInp
      type: !_InputBase
        typeParam: a
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: a
    name: PrntParamDiffInp
    parent: !_InputBase
      input: RefPrntParamDiffInp
      typeArgs:
      - !_InputArg
        typeParam: a
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntParamDiffOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntParamDiffOutp
      type: !_OutputBase
        typeParam: b
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: PrntParamDiffOutp
      type: !_OutputBase
        typeParam: a
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: a
    name: PrntParamDiffOutp
    parent: !_OutputBase
      output: RefPrntParamDiffOutp
      typeArgs:
      - !_OutputArg
        typeParam: a
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntParamSameDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntParamSameDual
      type: !_DualBase
        typeParam: a
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: PrntParamSameDual
      type: !_DualBase
        typeParam: a
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: a
    name: PrntParamSameDual
    parent: !_DualBase
      dual: RefPrntParamSameDual
      typeArgs:
      - !_DualArg
        typeParam: a
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntParamSameInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntParamSameInp
      type: !_InputBase
        typeParam: a
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: PrntParamSameInp
      type: !_InputBase
        typeParam: a
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: a
    name: PrntParamSameInp
    parent: !_InputBase
      input: RefPrntParamSameInp
      typeArgs:
      - !_InputArg
        typeParam: a
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntParamSameOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntParamSameOutp
      type: !_OutputBase
        typeParam: a
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: PrntParamSameOutp
      type: !_OutputBase
        typeParam: a
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: a
    name: PrntParamSameOutp
    parent: !_OutputBase
      output: RefPrntParamSameOutp
      typeArgs:
      - !_OutputArg
        typeParam: a
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier PrntUnionPrnt: !_TypeUnion
    allItems:
    - !_UnionMember
      name: Number
      union: PrntUnionPrnt
    items:
    - !_Aliased
      name: Number
    name: PrntUnionPrnt
    typeKind: !_TypeKind Union
  !_Identifier PrntUnionPrntDescr: !_TypeUnion
    allItems:
    - !_UnionMember
      name: Number
      union: PrntUnionPrntDescr
    items:
    - !_Aliased
      name: Number
    name: PrntUnionPrntDescr
    typeKind: !_TypeKind Union
  !_Identifier PrntUnionPrntDup: !_TypeUnion
    allItems:
    - !_UnionMember
      name: Number
      union: PrntUnionPrntDup
    items:
    - !_Aliased
      name: Number
    name: PrntUnionPrntDup
    typeKind: !_TypeKind Union
  !_Identifier PrntUnionSamePrnt: !_TypeUnion
    allItems:
    - !_UnionMember
      name: String
      union: PrntUnionSamePrnt
    items:
    - !_Aliased
      name: String
    name: PrntUnionSamePrnt
    typeKind: !_TypeKind Union
  !_Identifier RefCnstAltDmnDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefCnstAltDmnDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefCnstAltDmnDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstAltDmnInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefCnstAltDmnInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefCnstAltDmnInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstAltDmnOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefCnstAltDmnOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefCnstAltDmnOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefCnstAltDualDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefCnstAltDualDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstAltDualDual
      name: ref
  !_Identifier RefCnstAltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefCnstAltDualInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefCnstAltDualInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstAltDualInp
      name: ref
  !_Identifier RefCnstAltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefCnstAltDualOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefCnstAltDualOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstAltDualOutp
      name: ref
  !_Identifier RefCnstAltObjDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefCnstAltObjDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefCnstAltObjDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstAltObjDual
      name: ref
  !_Identifier RefCnstAltObjInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefCnstAltObjInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefCnstAltObjInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Input
        typeName: PrntCnstAltObjInp
      name: ref
  !_Identifier RefCnstAltObjOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefCnstAltObjOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefCnstAltObjOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: PrntCnstAltObjOutp
      name: ref
  !_Identifier RefCnstFieldDmnDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldDmnDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: RefCnstFieldDmnDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstFieldDmnInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldDmnInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: RefCnstFieldDmnInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstFieldDmnOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldDmnOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: RefCnstFieldDmnOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefCnstFieldDualDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldDualDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: RefCnstFieldDualDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstFieldDualDual
      name: ref
  !_Identifier RefCnstFieldDualInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldDualInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: RefCnstFieldDualInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstFieldDualInp
      name: ref
  !_Identifier RefCnstFieldDualOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldDualOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: RefCnstFieldDualOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstFieldDualOutp
      name: ref
  !_Identifier RefCnstFieldObjDual: !_TypeDual
    allFields:
    - !_ObjectFor(_DualField)
      name: field
      object: RefCnstFieldObjDual
      type: !_DualBase
        typeParam: ref
    fields:
    - !_DualField
      name: field
      type: !_DualBase
        typeParam: ref
    name: RefCnstFieldObjDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstFieldObjDual
      name: ref
  !_Identifier RefCnstFieldObjInp: !_TypeInput
    allFields:
    - !_ObjectFor(_InputField)
      name: field
      object: RefCnstFieldObjInp
      type: !_InputBase
        typeParam: ref
    fields:
    - !_InputField
      name: field
      type: !_InputBase
        typeParam: ref
    name: RefCnstFieldObjInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Input
        typeName: PrntCnstFieldObjInp
      name: ref
  !_Identifier RefCnstFieldObjOutp: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefCnstFieldObjOutp
      type: !_OutputBase
        typeParam: ref
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: ref
    name: RefCnstFieldObjOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: PrntCnstFieldObjOutp
      name: ref
  !_Identifier RefCnstPrntDualPrntDual: !_TypeDual
    name: RefCnstPrntDualPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstPrntDualPrntDual
      name: ref
  !_Identifier RefCnstPrntDualPrntInp: !_TypeInput
    name: RefCnstPrntDualPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstPrntDualPrntInp
      name: ref
  !_Identifier RefCnstPrntDualPrntOutp: !_TypeOutput
    name: RefCnstPrntDualPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstPrntDualPrntOutp
      name: ref
  !_Identifier RefCnstPrntObjPrntDual: !_TypeDual
    name: RefCnstPrntObjPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Dual
        typeName: PrntCnstPrntObjPrntDual
      name: ref
  !_Identifier RefCnstPrntObjPrntInp: !_TypeInput
    name: RefCnstPrntObjPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Input
        typeName: PrntCnstPrntObjPrntInp
      name: ref
  !_Identifier RefCnstPrntObjPrntOutp: !_TypeOutput
    name: RefCnstPrntObjPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Output
        typeName: PrntCnstPrntObjPrntOutp
      name: ref
  !_Identifier RefGnrcAltArgDescrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcAltArgDescrDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltArgDescrDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltArgDescrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcAltArgDescrInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltArgDescrInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltArgDescrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcAltArgDescrOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltArgDescrOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltArgDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcAltArgDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltArgDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltArgInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcAltArgInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltArgInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltArgOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcAltArgOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltArgOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcAltDualDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltDualDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcAltDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcAltDualInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltDualInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcAltDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcAltDualOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltDualOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcAltModParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: RefGnrcAltModParamDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltModParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier RefGnrcAltModParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: RefGnrcAltModParamInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltModParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier RefGnrcAltModParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      object: RefGnrcAltModParamOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      collections:
      - !_ModifierTypeParam
        by: mod
        modifierKind: !_ModifierKind Param
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltModParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: mod
  !_Identifier RefGnrcAltModStrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: RefGnrcAltModStrDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltModStrDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltModStrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: RefGnrcAltModStrInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltModStrInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltModStrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      object: RefGnrcAltModStrOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      collections:
      - !_ModifierDictionary
        by: '*'
        modifierKind: !_ModifierKind Dict
        typeKind: !_SimpleKind Basic
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltModStrOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcAltParamDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcAltParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcAltParamInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Input
      name: ref
  !_Identifier RefGnrcAltParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcAltParamOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Output
      name: ref
  !_Identifier RefGnrcAltSmplDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcAltSmplDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcAltSmplDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltSmplInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcAltSmplInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcAltSmplInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcAltSmplOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcAltSmplOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcAltSmplOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcFieldArgDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcFieldArgDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcFieldArgDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcFieldArgInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcFieldArgInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcFieldArgInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcFieldArgOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcFieldArgOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcFieldArgOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcFieldDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcFieldDualDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcFieldDualDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcFieldDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcFieldDualInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcFieldDualInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcFieldDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcFieldDualOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcFieldDualOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcFieldParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcFieldParamDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcFieldParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcFieldParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcFieldParamInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcFieldParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Input
      name: ref
  !_Identifier RefGnrcFieldParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcFieldParamOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcFieldParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Output
      name: ref
  !_Identifier RefGnrcPrntArgDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntArgDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcPrntArgDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcPrntArgInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntArgInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcPrntArgInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcPrntArgOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntArgOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcPrntArgOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: ref
  !_Identifier RefGnrcPrntDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntDualDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcPrntDualDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntDualInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntDualInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcPrntDualInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntDualOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntDualOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcPrntDualOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntDualPrntDual: !_TypeDual
    name: RefGnrcPrntDualPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntDualPrntInp: !_TypeInput
    name: RefGnrcPrntDualPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntDualPrntOutp: !_TypeOutput
    name: RefGnrcPrntDualPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntParamDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefGnrcPrntParamDual
      type: !_DualBase
        typeParam: ref
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: ref
    name: RefGnrcPrntParamDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntParamInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefGnrcPrntParamInp
      type: !_InputBase
        typeParam: ref
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: ref
    name: RefGnrcPrntParamInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Input
      name: ref
  !_Identifier RefGnrcPrntParamOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefGnrcPrntParamOutp
      type: !_OutputBase
        typeParam: ref
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: ref
    name: RefGnrcPrntParamOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Output
      name: ref
  !_Identifier RefGnrcPrntParamPrntDual: !_TypeDual
    name: RefGnrcPrntParamPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Dual
      name: ref
  !_Identifier RefGnrcPrntParamPrntInp: !_TypeInput
    name: RefGnrcPrntParamPrntInp
    parent: !_InputBase
      typeParam: ref
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Input
      name: ref
  !_Identifier RefGnrcPrntParamPrntOutp: !_TypeOutput
    name: RefGnrcPrntParamPrntOutp
    parent: !_OutputBase
      typeParam: ref
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Output
      name: ref
  !_Identifier RefObjPrntDual: !_TypeDual
    name: RefObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier RefObjPrntInp: !_TypeInput
    name: RefObjPrntInp
    typeKind: !_TypeKind Input
  !_Identifier RefObjPrntOutp: !_TypeOutput
    name: RefObjPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier RefOutpCnstDomEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpCnstDomEnum
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpCnstDomEnum
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Domain
        typeName: JustOutpCnstDomEnum
      name: type
  !_Identifier RefOutpCnstEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpCnstEnum
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpCnstEnum
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumOutpCnstEnum
      name: type
  !_Identifier RefOutpCnstEnumPrnt: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpCnstEnumPrnt
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpCnstEnumPrnt
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: ParentOutpCnstEnumPrnt
      name: type
  !_Identifier RefOutpCnstPrntEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpCnstPrntEnum
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpCnstPrntEnum
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: EnumOutpCnstPrntEnum
      name: type
  !_Identifier RefOutpGnrcEnum: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpGnrcEnum
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpGnrcEnum
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Enum
      name: type
  !_Identifier RefOutpGnrcValue: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpGnrcValue
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpGnrcValue
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Internal
        typeName: _Enum
      name: type
  !_Identifier RefOutpPrntGnrc: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: field
      object: RefOutpPrntGnrc
      type: !_OutputBase
        typeParam: type
    fields:
    - !_OutputField
      name: field
      type: !_OutputBase
        typeParam: type
    name: RefOutpPrntGnrc
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Enum
        typeName: PrntOutpPrntGnrc
      name: type
  !_Identifier RefPrntAltDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntAltDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntAltDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntAltDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntAltInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntAltInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntAltInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: parent
      type: !_InputBase
        input: Number
    name: RefPrntAltInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntAltOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntAltOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntAltOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: parent
      type: !_OutputBase
        output: Number
    name: RefPrntAltOutp
    typeKind: !_TypeKind Output
  !_Identifier RefPrntDescrDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDescrDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDescrDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntDescrDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntDescrInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntDescrInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntDescrInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: parent
      type: !_InputBase
        input: Number
    name: RefPrntDescrInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntDescrOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntDescrOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntDescrOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: parent
      type: !_OutputBase
        output: Number
    name: RefPrntDescrOutp
    typeKind: !_TypeKind Output
  !_Identifier RefPrntDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntDualDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntDualDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntDualInp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualInp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualInp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntDualInp
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntDualOutp: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntDualOutp
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntDualOutp
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntFieldDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntFieldDual
      type: !_DualBase
        dual: String
    allFields:
    - !_ObjectFor(_DualField)
      name: parent
      object: RefPrntFieldDual
      type: !_DualBase
        dual: Number
    alternates:
    - !_DualAlternate
      type: !_DualBase
        dual: String
    fields:
    - !_DualField
      name: parent
      type: !_DualBase
        dual: Number
    name: RefPrntFieldDual
    typeKind: !_TypeKind Dual
  !_Identifier RefPrntFieldInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntFieldInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntFieldInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: parent
      type: !_InputBase
        input: Number
    name: RefPrntFieldInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntFieldOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntFieldOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntFieldOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: parent
      type: !_OutputBase
        output: Number
    name: RefPrntFieldOutp
    typeKind: !_TypeKind Output
  !_Identifier RefPrntInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntInp
      type: !_InputBase
        input: String
    allFields:
    - !_ObjectFor(_InputField)
      name: parent
      object: RefPrntInp
      type: !_InputBase
        input: Number
    alternates:
    - !_InputAlternate
      type: !_InputBase
        input: String
    fields:
    - !_InputField
      name: parent
      type: !_InputBase
        input: Number
    name: RefPrntInp
    typeKind: !_TypeKind Input
  !_Identifier RefPrntOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntOutp
      type: !_OutputBase
        output: String
    allFields:
    - !_ObjectFor(_OutputField)
      name: parent
      object: RefPrntOutp
      type: !_OutputBase
        output: Number
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: String
    fields:
    - !_OutputField
      name: parent
      type: !_OutputBase
        output: Number
    name: RefPrntOutp
    typeKind: !_TypeKind Output
  !_Identifier RefPrntParamDiffDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntParamDiffDual
      type: !_DualBase
        typeParam: b
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: b
    name: RefPrntParamDiffDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: b
  !_Identifier RefPrntParamDiffInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntParamDiffInp
      type: !_InputBase
        typeParam: b
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: b
    name: RefPrntParamDiffInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: b
  !_Identifier RefPrntParamDiffOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntParamDiffOutp
      type: !_OutputBase
        typeParam: b
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: b
    name: RefPrntParamDiffOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: b
  !_Identifier RefPrntParamSameDual: !_TypeDual
    allAlternates:
    - !_ObjectFor(_DualAlternate)
      object: RefPrntParamSameDual
      type: !_DualBase
        typeParam: a
    alternates:
    - !_DualAlternate
      type: !_DualBase
        typeParam: a
    name: RefPrntParamSameDual
    typeKind: !_TypeKind Dual
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier RefPrntParamSameInp: !_TypeInput
    allAlternates:
    - !_ObjectFor(_InputAlternate)
      object: RefPrntParamSameInp
      type: !_InputBase
        typeParam: a
    alternates:
    - !_InputAlternate
      type: !_InputBase
        typeParam: a
    name: RefPrntParamSameInp
    typeKind: !_TypeKind Input
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier RefPrntParamSameOutp: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: RefPrntParamSameOutp
      type: !_OutputBase
        typeParam: a
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        typeParam: a
    name: RefPrntParamSameOutp
    typeKind: !_TypeKind Output
    typeParams:
    - !_TypeParam
      constraint: !_TypeRef(_TypeKind)
        typeKind: !_TypeKind Basic
        typeName: String
      name: a
  !_Identifier UnionAlias: !_TypeUnion
    aliases: [UnA1, UnA2]
    allItems:
    - !_UnionMember
      name: Boolean
      union: UnionAlias
    - !_UnionMember
      name: Number
      union: UnionAlias
    items:
    - !_Aliased
      name: Boolean
    - !_Aliased
      name: Number
    name: UnionAlias
    typeKind: !_TypeKind Union
  !_Identifier UnionDescr: !_TypeUnion
    allItems:
    - !_UnionMember
      description: 'Union Descr'
      name: Number
      union: UnionDescr
    items:
    - !_Aliased
      description: 'Union Descr'
      name: Number
    name: UnionDescr
    typeKind: !_TypeKind Union
  !_Identifier UnionDiff: !_TypeUnion
    allItems:
    - !_UnionMember
      name: Boolean
      union: UnionDiff
    - !_UnionMember
      name: Number
      union: UnionDiff
    items:
    - !_Aliased
      name: Boolean
    - !_Aliased
      name: Number
    name: UnionDiff
    typeKind: !_TypeKind Union
  !_Identifier UnionPrnt: !_TypeUnion
    allItems:
    - !_UnionMember
      name: Number
      union: PrntUnionPrnt
    - !_UnionMember
      name: String
      union: UnionPrnt
    items:
    - !_Aliased
      name: String
    name: UnionPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Union
      typeName: PrntUnionPrnt
    typeKind: !_TypeKind Union
  !_Identifier UnionPrntDescr: !_TypeUnion
    allItems:
    - !_UnionMember
      name: Number
      union: PrntUnionPrntDescr
    - !_UnionMember
      name: Number
      union: UnionPrntDescr
    items:
    - !_Aliased
      name: Number
    name: UnionPrntDescr
    parent: !_TypeRef(_SimpleKind)
      description: 'Parent comment'
      typeKind: !_SimpleKind Union
      typeName: PrntUnionPrntDescr
    typeKind: !_TypeKind Union
  !_Identifier UnionPrntDup: !_TypeUnion
    allItems:
    - !_UnionMember
      name: Number
      union: PrntUnionPrntDup
    - !_UnionMember
      name: Number
      union: UnionPrntDup
    items:
    - !_Aliased
      name: Number
    name: UnionPrntDup
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Union
      typeName: PrntUnionPrntDup
    typeKind: !_TypeKind Union
  !_Identifier UnionSame: !_TypeUnion
    allItems:
    - !_UnionMember
      name: Boolean
      union: UnionSame
    items:
    - !_Aliased
      name: Boolean
    name: UnionSame
    typeKind: !_TypeKind Union
  !_Identifier UnionSamePrnt: !_TypeUnion
    allItems:
    - !_UnionMember
      name: String
      union: PrntUnionSamePrnt
    - !_UnionMember
      name: Boolean
      union: UnionSamePrnt
    items:
    - !_Aliased
      name: Boolean
    name: UnionSamePrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Union
      typeName: PrntUnionSamePrnt
    typeKind: !_TypeKind Union