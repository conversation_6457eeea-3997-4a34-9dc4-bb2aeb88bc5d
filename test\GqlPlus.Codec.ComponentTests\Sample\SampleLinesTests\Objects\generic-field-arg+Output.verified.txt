﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcFieldArgOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: GnrcFieldArgOutp
        type: !_OutputBase
          output: RefGnrcFieldArgOutp
          typeArgs:
            - !_OutputArg
              typeParam: type
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          output: RefGnrcFieldArgOutp
          typeArgs:
            - !_OutputArg
              typeParam: type
    name: GnrcFieldArgOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type
  !_Identifier RefGnrcFieldArgOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcFieldArgOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcFieldArgOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref