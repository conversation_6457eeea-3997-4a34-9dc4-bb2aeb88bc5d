﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstAltDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstAltDualDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltCnstAltDualDual
        type: !_DualBase
          dual: Number
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltCnstAltDualDual
    parent: !_DualBase
      dual: PrntCnstAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstAltDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: CnstAltDualDual
        type: !_DualBase
          dual: RefCnstAltDualDual
          typeArgs:
            - !_DualArg
              dual: AltCnstAltDualDual
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: RefCnstAltDualDual
          typeArgs:
            - !_DualArg
              dual: AltCnstAltDualDual
    name: CnstAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstAltDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstAltDualDual
        type: !_DualBase
          dual: String
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    name: PrntCnstAltDualDual
    typeKind: !_TypeKind Dual
  !_Identifier RefCnstAltDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: RefCnstAltDualDual
        type: !_DualBase
          typeParam: ref
    alternates:
      - !_DualAlternate
        type: !_DualBase
          typeParam: ref
    name: RefCnstAltDualDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Dual
          typeName: PrntCnstAltDualDual
        name: ref