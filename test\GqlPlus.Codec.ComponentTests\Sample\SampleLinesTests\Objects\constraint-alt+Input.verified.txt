﻿!_Schema
types: !_Map_Type
  !_Identifier CnstAltInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: CnstAltInp
        type: !_InputBase
          typeParam: type
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: type
    name: CnstAltInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: Number
        name: type