﻿!_Schema
types: !_Map_Type
  !_Identifier DmnEnumValue: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumValue
        exclude: false
        value: !_EnumValue
          label: dmnEnumValue
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumValue
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: dmnEnumValue
          typeKind: !_SimpleKind Enum
          typeName: EnumDmnEnumValue
    name: DmnEnumValue
    typeKind: !_TypeKind Domain
  !_Identifier EnumDmnEnumValue: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumDmnEnumValue
        name: dmnEnumValue
    items:
      - !_Aliased
        name: dmnEnumValue
    name: EnumDmnEnumValue
    typeKind: !_TypeKind Enum