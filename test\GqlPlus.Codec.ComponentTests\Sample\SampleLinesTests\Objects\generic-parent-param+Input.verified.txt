﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcPrntParamInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: AltGnrcPrntParamInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltGnrcPrntParamInp
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: alt
        type: !_InputBase
          input: Number
    name: AltGnrcPrntParamInp
    typeKind: !_TypeKind Input
  !_Identifier GnrcPrntParamInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefGnrcPrntParamInp
        type: !_InputBase
          input: AltGnrcPrntParamInp
    name: GnrcPrntParamInp
    parent: !_InputBase
      input: RefGnrcPrntParamInp
      typeArgs:
        - !_InputArg
          input: AltGnrcPrntParamInp
    typeKind: !_TypeKind Input
  !_Identifier RefGnrcPrntParamInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefGnrcPrntParamInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefGnrcPrntParamInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Input
        name: ref