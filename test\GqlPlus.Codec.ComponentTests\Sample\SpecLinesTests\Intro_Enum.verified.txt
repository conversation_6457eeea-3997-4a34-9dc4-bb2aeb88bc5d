﻿!_Schema
errors:
  - !_Error
    _kind: !_TokenKind End
    _message: In _TypeEnum can't get model for type '_ParentType'
  - !_Error
    _kind: !_TokenKind End
    _message: In _Enum<PERSON>abe<PERSON> can't get model for type '_Aliased'
  - !_Error
    _kind: !_TokenKind End
    _message: In _EnumValue can't get model for type '_TypeRef'
types: !_Map_Type
  !_Identifier _EnumLabel: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: enum
        object: _EnumLabel
        type: !_DualBase
          dual: _Identifier
    fields:
      - !_DualField
        name: enum
        type: !_DualBase
          dual: _Identifier
    name: _EnumLabel
    parent: !_DualBase
      dual: _Aliased
    typeKind: !_TypeKind Dual
  !_Identifier _EnumValue: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: label
        object: _EnumValue
        type: !_OutputBase
          output: _Identifier
    fields:
      - !_OutputField
        name: label
        type: !_OutputBase
          output: _Identifier
    name: _EnumValue
    parent: !_OutputBase
      output: _TypeRef
      typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    typeKind: !_TypeKind Output
  !_Identifier _TypeEnum: !_TypeOutput
    name: _TypeEnum
    parent: !_OutputBase
      output: _ParentType
      typeArgs:
        - !_OutputArg
          label: Enum
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
        - !_OutputArg
          output: _Aliased
        - !_DualArg
          dual: _EnumLabel
    typeKind: !_TypeKind Output