﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcFieldDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: GnrcFieldDual
        type: !_DualBase
          typeParam: type
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          typeParam: type
    name: GnrcFieldDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type