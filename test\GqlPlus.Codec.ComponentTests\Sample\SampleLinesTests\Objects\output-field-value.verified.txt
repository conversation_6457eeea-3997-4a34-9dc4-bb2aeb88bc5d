﻿!_Schema
types: !_Map_Type
  !_Identifier EnumOutpFieldValue: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumOutpFieldValue
        name: outpFieldValue
    items:
      - !_Aliased
        name: outpFieldValue
    name: EnumOutpFieldValue
    typeKind: !_TypeKind Enum
  !_Identifier OutpFieldValue: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        field: field
        label: outpFieldValue
        object: OutpFieldValue
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpFieldValue
    fields:
      - !_OutputEnum
        field: field
        label: outpFieldValue
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpFieldValue
    name: OutpFieldValue
    typeKind: !_TypeKind Output