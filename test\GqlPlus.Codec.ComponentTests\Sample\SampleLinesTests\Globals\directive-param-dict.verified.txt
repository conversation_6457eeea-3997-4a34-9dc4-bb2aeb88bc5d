﻿!_Schema
directives: !_Map_Directives
  !_Identifier DrctParamDict: !_Directive
    locations: !_Set(_Location){Field:_,Fragment:_,Inline:_,Operation:_,Spread:_,Variable:_}
    name: DrctParamDict
    parameters:
      - !_InputParam
        input: InDrctParamDict
        modifiers: [!_ModifierDictionary{by:String,modifierKind:!_ModifierKind Dict,typeKind:!_SimpleKind Basic}]
    repeatable: false
types: !_Map_Type
  !_Identifier InDrctParamDict: !_TypeInput
    name: InDrctParamDict
    typeKind: !_TypeKind Input