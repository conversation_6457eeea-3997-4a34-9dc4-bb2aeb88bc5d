﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcAltParamOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: AltGnrcAltParamOutp
        type: !_OutputBase
          output: String
    allFields:
      - !_ObjectFor(_OutputField)
        name: alt
        object: AltGnrcAltParamOutp
        type: !_OutputBase
          output: Number
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: String
    fields:
      - !_OutputField
        name: alt
        type: !_OutputBase
          output: Number
    name: AltGnrcAltParamOutp
    typeKind: !_TypeKind Output
  !_Identifier GnrcAltParamOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: GnrcAltParamOutp
        type: !_OutputBase
          output: RefGnrcAltParamOutp
          typeArgs:
            - !_OutputArg
              output: AltGnrcAltParamOutp
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: RefGnrcAltParamOutp
          typeArgs:
            - !_OutputArg
              output: AltGnrcAltParamOutp
    name: GnrcAltParamOutp
    typeKind: !_TypeKind Output
  !_Identifier RefGnrcAltParamOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcAltParamOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcAltParamOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Output
        name: ref