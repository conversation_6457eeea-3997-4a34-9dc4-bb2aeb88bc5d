﻿!_Schema
types: !_Map_Type
  !_Identifier FldObjFieldInp: !_TypeInput
    name: FldObjFieldInp
    typeKind: !_TypeKind Input
  !_Identifier ObjFieldInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: ObjFieldInp
        type: !_InputBase
          input: FldObjFieldInp
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          input: FldObjFieldInp
    name: ObjFieldInp
    typeKind: !_TypeKind Input