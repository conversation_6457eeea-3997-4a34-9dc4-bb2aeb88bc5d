﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstFieldDualInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstFieldDualInp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltCnstFieldDualInp
        type: !_InputBase
          input: Number
    fields:
      - !_InputField
        name: alt
        type: !_InputBase
          input: Number
    name: AltCnstFieldDualInp
    parent: !_DualBase
      dual: PrntCnstFieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier CnstFieldDualInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: RefCnstFieldDualInp
        type: !_InputBase
          input: AltCnstFieldDualInp
    name: CnstFieldDualInp
    parent: !_InputBase
      input: RefCnstFieldDualInp
      typeArgs:
        - !_InputArg
          input: AltCnstFieldDualInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstFieldDualInp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstFieldDualInp
        type: !_DualBase
          dual: String
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    name: PrntCnstFieldDualInp
    typeKind: !_TypeKind Dual
  !_Identifier RefCnstFieldDualInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: RefCnstFieldDualInp
        type: !_InputBase
          typeParam: ref
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          typeParam: ref
    name: RefCnstFieldDualInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Dual
          typeName: PrntCnstFieldDualInp
        name: ref