﻿!_Schema
types: !_Map_Type
  !_Identifier InpFieldStr: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        default: 'default'
        name: field
        object: InpFieldStr
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        default: 'default'
        name: field
        type: !_InputBase
          input: String
    name: InpFieldStr
    typeKind: !_TypeKind Input