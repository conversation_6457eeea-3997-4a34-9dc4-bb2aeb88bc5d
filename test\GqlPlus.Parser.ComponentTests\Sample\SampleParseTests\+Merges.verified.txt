﻿!Sc I@001/0001
Success
{
  !Ca P@010/0001
  ctgr
  (Parallel)
  !Tr I@012/0001
  Ctgr
}
{
  !Ca I@010/0002
  ctgr
  (Parallel)
  !Tr I@017/0002
  Ctgr
}
{
  !Ou I@008/0003
  Ctgr
}
{
  !Ca P@010/0005
  ctgrAlias
  [
    CatA1
  ]
  (Parallel)
  !Tr I@020/0005
  CtgrAlias
}
{
  !Ca P@010/0006
  ctgrAlias
  [
    CatA2
  ]
  (Parallel)
  !Tr I@020/0006
  CtgrAlias
}
{
  !Ou I@008/0007
  CtgrAlias
}
{
  'First category'
  !Ca P@010/0010
  ctgrDescr
  (Parallel)
  !Tr I@012/0010
  CtgrDescr
}
{
  'Second category'
  !Ca P@010/0012
  ctgrDescr
  (Parallel)
  !Tr I@012/0012
  CtgrDescr
}
{
  !Ou I@008/0013
  CtgrDescr
}
{
  !Ca P@010/0014
  ctgrMod
  [
    CatM1
  ]
  (Parallel)
  !Tr I@020/0014
  CtgrMod
  ?
}
{
  !Ca P@010/0015
  ctgrMod
  [
    CatM2
  ]
  (Parallel)
  !Tr I@020/0015
  CtgrMod
  ?
}
{
  !Ou I@008/0016
  CtgrMod
}
{
  !Di I@012/0018
  Drct
  (Unique)
  Inline
}
{
  !Di I@012/0019
  Drct
  (Unique)
  Spread
}
{
  !Di I@012/0021
  DrctAlias
  [
    DirA1
  ]
  (Unique)
  Variable
}
{
  !Di I@012/0022
  DrctAlias
  [
    DirA2
  ]
  (Unique)
  Field
}
{
  !Di I@012/0024
  DrctParam
  (
    !Pa
    I@022/0024
    InDrctParam
  )
  (Unique)
  Operation
}
{
  !Di I@012/0025
  DrctParam
  (Unique)
  Fragment
}
{
  !In I@007/0026
  InDrctParam
}
{
  !Do I@008/0028
  DmnAlias
  [
    Num1
  ]
  Number
}
{
  !Do I@008/0029
  DmnAlias
  [
    Num2
  ]
  Number
}
{
  !Do I@008/0031
  DmnBool
  Boolean
  !DT P@026/0031
  False
  !DT P@026/0031
  True
}
{
  !Do I@008/0032
  DmnBool
  Boolean
  !DT P@026/0032
  False
  !DT P@026/0032
  True
}
{
  !Do I@008/0034
  DmnBoolDiff
  Boolean
  !DT I@030/0034
  True
}
{
  !Do I@008/0035
  DmnBoolDiff
  Boolean
  !DT I@030/0035
  False
}
{
  !Do I@008/0037
  DmnBoolSame
  Boolean
  !DT I@030/0037
  True
}
{
  !Do I@008/0038
  DmnBoolSame
  Boolean
  !DT I@030/0038
  True
}
{
  !Do I@008/0040
  DmnEnumDiff
  Enum
  !DE I@027/0040
  true
}
{
  !Do I@008/0041
  DmnEnumDiff
  Enum
  !DE I@027/0041
  false
}
{
  !Do I@008/0043
  DmnEnumSame
  Enum
  !DE I@027/0043
  true
}
{
  !Do I@008/0044
  DmnEnumSame
  Enum
  !DE I@027/0044
  true
}
{
  !Do I@008/0046
  DmnNmbr
  Number
}
{
  !Do I@008/0047
  DmnNmbr
  Number
}
{
  !Do I@008/0049
  DmnNmbrDiff
  Number
  !DN N@029/0049
  1
  ~
  9
}
{
  !Do I@008/0050
  DmnNmbrDiff
  Number
}
{
  !Do I@008/0052
  DmnNmbrSame
  Number
  !DN N@029/0052
  1
  ~
  9
}
{
  !Do I@008/0053
  DmnNmbrSame
  Number
  !DN N@029/0053
  1
  ~
  9
}
{
  !Do I@008/0055
  DmnStr
  String
}
{
  !Do I@008/0056
  DmnStr
  String
}
{
  !Do I@008/0058
  DmnStrDiff
  String
  !DX R@028/0058
  /a+/
}
{
  !Do I@008/0059
  DmnStrDiff
  String
}
{
  !Do I@008/0061
  DmnStrSame
  String
  !DX R@028/0061
  /a+/
}
{
  !Do I@008/0062
  DmnStrSame
  String
  !DX R@028/0062
  /a+/
}
{
  !En I@006/0064
  EnumAlias
  [
    En1
  ]
  !EL I@024/0064
  enumAlias
}
{
  !En I@006/0065
  EnumAlias
  [
    En2
  ]
  !EL I@024/0065
  enumAlias
}
{
  !En I@006/0067
  EnumDiff
  !EL I@017/0067
  one
}
{
  !En I@006/0068
  EnumDiff
  !EL I@017/0068
  two
}
{
  !En I@006/0070
  EnumSame
  !EL I@017/0070
  enumSame
}
{
  !En I@006/0071
  EnumSame
  !EL I@017/0071
  enumSame
}
{
  !En I@006/0073
  EnumSamePrnt
  :( !Tr I@022/0073 PrntEnumSamePrnt )
  !EL I@039/0073
  enumSamePrnt
}
{
  !En I@006/0074
  EnumSamePrnt
  :( !Tr I@022/0074 PrntEnumSamePrnt )
  !EL I@039/0074
  enumSamePrnt
}
{
  !En I@006/0075
  PrntEnumSamePrnt
  !EL I@025/0075
  prnt_enumSamePrnt
}
{
  !En I@006/0077
  EnumValueAlias
  !EL I@023/0077
  enumValueAlias
  [
    val1
  ]
}
{
  !En I@006/0078
  EnumValueAlias
  !EL I@023/0078
  enumValueAlias
  [
    val2
  ]
}
{
  !Du I@006/0080
  ObjDual
}
{
  !Du I@006/0081
  ObjDual
}
{
  !In I@007/0083
  ObjInp
}
{
  !In I@007/0084
  ObjInp
}
{
  !Ou I@008/0086
  ObjOutp
}
{
  !Ou I@008/0087
  ObjOutp
}
{
  !Du I@006/0089
  ObjAliasDual
  [
    Dual1
  ]
}
{
  !Du I@006/0090
  ObjAliasDual
  [
    Dual2
  ]
}
{
  !In I@007/0092
  ObjAliasInp
  [
    Input1
  ]
}
{
  !In I@007/0093
  ObjAliasInp
  [
    Input2
  ]
}
{
  !Ou I@008/0095
  ObjAliasOutp
  [
    Output1
  ]
}
{
  !Ou I@008/0096
  ObjAliasOutp
  [
    Output2
  ]
}
{
  !Du I@006/0098
  ObjAltDual
  |
  I@021/0098
  ObjAltDualType
}
{
  !Du I@006/0099
  ObjAltDual
  |
  I@021/0099
  ObjAltDualType
}
{
  !Du I@006/0100
  ObjAltDualType
}
{
  !In I@007/0102
  ObjAltInp
  |
  I@021/0102
  ObjAltInpType
}
{
  !In I@007/0103
  ObjAltInp
  |
  I@021/0103
  ObjAltInpType
}
{
  !In I@007/0104
  ObjAltInpType
}
{
  !Ou I@008/0106
  ObjAltOutp
  |
  I@023/0106
  ObjAltOutpType
}
{
  !Ou I@008/0107
  ObjAltOutp
  |
  I@023/0107
  ObjAltOutpType
}
{
  !Ou I@008/0108
  ObjAltOutpType
}
{
  !Du I@006/0110
  ObjCnstDual
  <
    I@019/0110
    $type
    :String
  >
  {
    !DF I@034/0110
    field
    :
    I@042/0110
    $type
  }
}
{
  !Du I@006/0111
  ObjCnstDual
  <
    I@019/0111
    $type
    :String
  >
  {
    !DF I@034/0111
    str
    :
    I@040/0111
    $type
  }
}
{
  !In I@007/0113
  ObjCnstInp
  <
    I@019/0113
    $type
    :String
  >
  {
    !IF I@034/0113
    field
    :
    I@042/0113
    $type
  }
}
{
  !In I@007/0114
  ObjCnstInp
  <
    I@019/0114
    $type
    :String
  >
  {
    !IF I@034/0114
    str
    :
    I@040/0114
    $type
  }
}
{
  !Ou I@008/0116
  ObjCnstOutp
  <
    I@021/0116
    $type
    :String
  >
  {
    !OF I@036/0116
    field
    :
    I@044/0116
    $type
  }
}
{
  !Ou I@008/0117
  ObjCnstOutp
  <
    I@021/0117
    $type
    :String
  >
  {
    !OF I@036/0117
    str
    :
    I@042/0117
    $type
  }
}
{
  !Du I@006/0119
  ObjFieldDual
  {
    !DF I@021/0119
    field
    :
    I@028/0119
    FldObjFieldDual
  }
}
{
  !Du I@006/0120
  ObjFieldDual
  {
    !DF I@021/0120
    field
    :
    I@028/0120
    FldObjFieldDual
  }
}
{
  !Du I@006/0121
  FldObjFieldDual
}
{
  !In I@007/0123
  ObjFieldInp
  {
    !IF I@021/0123
    field
    :
    I@028/0123
    FldObjFieldInp
  }
}
{
  !In I@007/0124
  ObjFieldInp
  {
    !IF I@021/0124
    field
    :
    I@028/0124
    FldObjFieldInp
  }
}
{
  !In I@007/0125
  FldObjFieldInp
}
{
  !Ou I@008/0127
  ObjFieldOutp
  {
    !OF I@023/0127
    field
    :
    I@030/0127
    FldObjFieldOutp
  }
}
{
  !Ou I@008/0128
  ObjFieldOutp
  {
    !OF I@023/0128
    field
    :
    I@030/0128
    FldObjFieldOutp
  }
}
{
  !Ou I@008/0129
  FldObjFieldOutp
}
{
  !Du I@006/0131
  ObjFieldAliasDual
  {
    !DF I@026/0131
    field
    [
      field1
    ]
    :
    I@042/0131
    FldObjFieldAliasDual
  }
}
{
  !Du I@006/0132
  ObjFieldAliasDual
  {
    !DF I@026/0132
    field
    [
      field2
    ]
    :
    I@042/0132
    FldObjFieldAliasDual
  }
}
{
  !Du I@006/0133
  FldObjFieldAliasDual
}
{
  !In I@007/0135
  ObjFieldAliasInp
  {
    !IF I@026/0135
    field
    [
      field1
    ]
    :
    I@042/0135
    FldObjFieldAliasInp
  }
}
{
  !In I@007/0136
  ObjFieldAliasInp
  {
    !IF I@026/0136
    field
    [
      field2
    ]
    :
    I@042/0136
    FldObjFieldAliasInp
  }
}
{
  !In I@007/0137
  FldObjFieldAliasInp
}
{
  !Ou I@008/0139
  ObjFieldAliasOutp
  {
    !OF I@028/0139
    field
    [
      field1
    ]
    :
    I@044/0139
    FldObjFieldAliasOutp
  }
}
{
  !Ou I@008/0140
  ObjFieldAliasOutp
  {
    !OF I@028/0140
    field
    [
      field2
    ]
    :
    I@044/0140
    FldObjFieldAliasOutp
  }
}
{
  !Ou I@008/0141
  FldObjFieldAliasOutp
}
{
  !Du I@006/0143
  ObjFieldTypeAliasDual
  {
    !DF I@030/0143
    field
    :
    I@037/0143
    String
  }
}
{
  !Du I@006/0144
  ObjFieldTypeAliasDual
  {
    !DF I@030/0144
    field
    :
    I@037/0144
    String
  }
}
{
  !In I@007/0146
  ObjFieldTypeAliasInp
  {
    !IF I@030/0146
    field
    :
    I@037/0146
    String
  }
}
{
  !In I@007/0147
  ObjFieldTypeAliasInp
  {
    !IF I@030/0147
    field
    :
    I@037/0147
    String
  }
}
{
  !Ou I@008/0149
  ObjFieldTypeAliasOutp
  {
    !OF I@032/0149
    field
    :
    I@039/0149
    String
  }
}
{
  !Ou I@008/0150
  ObjFieldTypeAliasOutp
  {
    !OF I@032/0150
    field
    :
    I@039/0150
    String
  }
}
{
  !Du I@006/0152
  ObjParamDual
  <
    I@020/0152
    $test
    :String
  >
  {
    !DF I@035/0152
    test
    :
    I@042/0152
    $test
  }
}
{
  !Du I@006/0153
  ObjParamDual
  <
    I@020/0153
    $type
    :String
  >
  {
    !DF I@035/0153
    type
    :
    I@042/0153
    $type
  }
}
{
  !In I@007/0155
  ObjParamInp
  <
    I@020/0155
    $test
    :String
  >
  {
    !IF I@035/0155
    test
    :
    I@042/0155
    $test
  }
}
{
  !In I@007/0156
  ObjParamInp
  <
    I@020/0156
    $type
    :String
  >
  {
    !IF I@035/0156
    type
    :
    I@042/0156
    $type
  }
}
{
  !Ou I@008/0158
  ObjParamOutp
  <
    I@022/0158
    $test
    :String
  >
  {
    !OF I@037/0158
    test
    :
    I@044/0158
    $test
  }
}
{
  !Ou I@008/0159
  ObjParamOutp
  <
    I@022/0159
    $type
    :String
  >
  {
    !OF I@037/0159
    type
    :
    I@044/0159
    $type
  }
}
{
  !Du I@006/0161
  ObjParamCnstDual
  <
    I@024/0161
    $test
    :String
  >
  {
    !DF I@039/0161
    test
    :
    I@046/0161
    $test
  }
}
{
  !Du I@006/0162
  ObjParamCnstDual
  <
    I@024/0162
    $test
    :String
  >
  {
    !DF I@039/0162
    type
    :
    I@046/0162
    $test
  }
}
{
  !In I@007/0164
  ObjParamCnstInp
  <
    I@024/0164
    $test
    :String
  >
  {
    !IF I@039/0164
    test
    :
    I@046/0164
    $test
  }
}
{
  !In I@007/0165
  ObjParamCnstInp
  <
    I@024/0165
    $test
    :String
  >
  {
    !IF I@039/0165
    type
    :
    I@046/0165
    $test
  }
}
{
  !Ou I@008/0167
  ObjParamCnstOutp
  <
    I@026/0167
    $test
    :String
  >
  {
    !OF I@041/0167
    test
    :
    I@048/0167
    $test
  }
}
{
  !Ou I@008/0168
  ObjParamCnstOutp
  <
    I@026/0168
    $test
    :String
  >
  {
    !OF I@041/0168
    type
    :
    I@048/0168
    $test
  }
}
{
  !Du I@006/0170
  ObjParamDupDual
  <
    I@023/0170
    $test
    :String
  >
  {
    !DF I@038/0170
    test
    :
    I@045/0170
    $test
  }
}
{
  !Du I@006/0171
  ObjParamDupDual
  <
    I@023/0171
    $test
    :String
  >
  {
    !DF I@038/0171
    type
    :
    I@045/0171
    $test
  }
}
{
  !In I@007/0173
  ObjParamDupInp
  <
    I@023/0173
    $test
    :String
  >
  {
    !IF I@038/0173
    test
    :
    I@045/0173
    $test
  }
}
{
  !In I@007/0174
  ObjParamDupInp
  <
    I@023/0174
    $test
    :String
  >
  {
    !IF I@038/0174
    type
    :
    I@045/0174
    $test
  }
}
{
  !Ou I@008/0176
  ObjParamDupOutp
  <
    I@025/0176
    $test
    :String
  >
  {
    !OF I@040/0176
    test
    :
    I@047/0176
    $test
  }
}
{
  !Ou I@008/0177
  ObjParamDupOutp
  <
    I@025/0177
    $test
    :String
  >
  {
    !OF I@040/0177
    type
    :
    I@047/0177
    $test
  }
}
{
  !Du I@006/0179
  ObjPrntDual
  :
  I@021/0179
  RefObjPrntDual
}
{
  !Du I@006/0180
  ObjPrntDual
  :
  I@021/0180
  RefObjPrntDual
}
{
  !Du I@006/0181
  RefObjPrntDual
}
{
  !In I@007/0183
  ObjPrntInp
  :
  I@021/0183
  RefObjPrntInp
}
{
  !In I@007/0184
  ObjPrntInp
  :
  I@021/0184
  RefObjPrntInp
}
{
  !In I@007/0185
  RefObjPrntInp
}
{
  !Ou I@008/0187
  ObjPrntOutp
  :
  I@023/0187
  RefObjPrntOutp
}
{
  !Ou I@008/0188
  ObjPrntOutp
  :
  I@023/0188
  RefObjPrntOutp
}
{
  !Ou I@008/0189
  RefObjPrntOutp
}
{
  !Op I@008/0191
  Schema
}
{
  !Op I@008/0192
  Schema
}
{
  !Op I@008/0194
  Schema
  [
    Opt1
  ]
}
{
  !Op I@008/0195
  Schema
  [
    Opt2
  ]
}
{
  !Op I@008/0197
  Schema
  {
    !OS I@017/0197
    merged
    =( !k I@024/0197 Boolean.true )
  }
}
{
  !Op I@008/0198
  Schema
  {
    !OS I@017/0198
    merged
    =( !c P@024/0198 [ !k N@025/0198 0 ] )
  }
}
{
  !Ou I@008/0200
  OutpFieldEnumAlias
  {
    !OF I@029/0200
    field
    [
      field1
    ]
    =
    I@046/0200
    Boolean
    .true
  }
}
{
  !Ou I@008/0201
  OutpFieldEnumAlias
  {
    !OF I@029/0201
    field
    [
      field2
    ]
    =
    I@046/0201
    .true
  }
}
{
  !Ou I@008/0203
  OutpFieldEnumValue
  {
    !OF I@029/0203
    field
    =
    I@037/0203
    Boolean
    .true
  }
}
{
  !Ou I@008/0204
  OutpFieldEnumValue
  {
    !OF I@029/0204
    field
    =
    I@037/0204
    .true
  }
}
{
  !Ou I@008/0206
  OutpFieldParam
  {
    !OF I@025/0206
    field
    (
      !Pa
      I@031/0206
      OutpFieldParam1
    )
    :
    I@049/0206
    FldOutpFieldParam
  }
}
{
  !Ou I@008/0207
  OutpFieldParam
  {
    !OF I@025/0207
    field
    (
      !Pa
      I@031/0207
      OutpFieldParam2
    )
    :
    I@049/0207
    FldOutpFieldParam
  }
}
{
  !In I@007/0208
  OutpFieldParam1
}
{
  !In I@007/0209
  OutpFieldParam2
}
{
  !Du I@006/0210
  FldOutpFieldParam
}
{
  !Un I@007/0212
  UnionAlias
  [
    UnA1
  ]
  !UM I@027/0212
  Boolean
}
{
  !Un I@007/0213
  UnionAlias
  [
    UnA2
  ]
  !UM I@027/0213
  Number
}
{
  !Un I@007/0215
  UnionDiff
  !UM I@019/0215
  Boolean
}
{
  !Un I@007/0216
  UnionDiff
  !UM I@019/0216
  Number
}
{
  !Un I@007/0218
  UnionSame
  !UM I@019/0218
  Boolean
}
{
  !Un I@007/0219
  UnionSame
  !UM I@019/0219
  Boolean
}
{
  !Un I@007/0221
  UnionSamePrnt
  :( !Tr I@024/0221 PrntUnionSamePrnt )
  !UM I@042/0221
  Boolean
}
{
  !Un I@007/0222
  UnionSamePrnt
  :( !Tr I@024/0222 PrntUnionSamePrnt )
  !UM I@042/0222
  Boolean
}
{
  !Un I@007/0223
  PrntUnionSamePrnt
  !UM I@027/0223
  String
}