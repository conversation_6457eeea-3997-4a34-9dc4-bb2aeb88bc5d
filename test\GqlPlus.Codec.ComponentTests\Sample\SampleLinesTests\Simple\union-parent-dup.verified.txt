﻿!_Schema
types: !_Map_Type
  !_Identifier PrntUnionPrntDup: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrntDup
    items:
      - !_Aliased
        name: Number
    name: PrntUnionPrntDup
    typeKind: !_TypeKind Union
  !_Identifier UnionPrntDup: !_TypeUnion
    allItems:
      - !_UnionMember
        name: Number
        union: PrntUnionPrntDup
      - !_UnionMember
        name: Number
        union: UnionPrntDup
    items:
      - !_Aliased
        name: Number
    name: UnionPrntDup
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Union
      typeName: PrntUnionPrntDup
    typeKind: !_TypeKind Union