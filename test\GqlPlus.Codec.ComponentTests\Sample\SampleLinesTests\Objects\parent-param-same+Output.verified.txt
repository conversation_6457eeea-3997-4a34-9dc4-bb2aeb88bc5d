﻿!_Schema
types: !_Map_Type
  !_Identifier PrntParamSameOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefPrntParamSameOutp
        type: !_OutputBase
          typeParam: a
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: PrntParamSameOutp
        type: !_OutputBase
          typeParam: a
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: a
    name: PrntParamSameOutp
    parent: !_OutputBase
      output: RefPrntParamSameOutp
      typeArgs:
        - !_OutputArg
          typeParam: a
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: a
  !_Identifier RefPrntParamSameOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefPrntParamSameOutp
        type: !_OutputBase
          typeParam: a
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: a
    name: RefPrntParamSameOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: a