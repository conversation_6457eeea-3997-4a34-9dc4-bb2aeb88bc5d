﻿!_Schema
types: !_Map_Type
  !_Identifier EnumOutpFieldValueDescr: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumOutpFieldValueDescr
        name: outpFieldValueDescr
    items:
      - !_Aliased
        name: outpFieldValueDescr
    name: EnumOutpFieldValueDescr
    typeKind: !_TypeKind Enum
  !_Identifier OutpFieldValueDescr: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        description: 'Test Descr'
        field: field
        label: outpFieldValueDescr
        object: OutpFieldValueDescr
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpFieldValueDescr
    fields:
      - !_OutputEnum
        description: 'Test Descr'
        field: field
        label: outpFieldValueDescr
        typeKind: !_SimpleKind Enum
        typeName: EnumOutpFieldValueDescr
    name: OutpFieldValueDescr
    typeKind: !_TypeKind Output