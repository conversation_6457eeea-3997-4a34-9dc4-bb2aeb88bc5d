﻿!_Schema
types: !_Map_Type
  !_Identifier FldOutpParamDescr: !_TypeDual
    name: FldOutpParamDescr
    typeKind: !_TypeKind Dual
  !_Identifier InOutpParamDescr: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: InOutpParamDescr
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: param
        object: InOutpParamDescr
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: param
        type: !_InputBase
          input: Number
    name: InOutpParamDescr
    typeKind: !_TypeKind Input
  !_Identifier OutpParamDescr: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: OutpParamDescr
        parameters:
          - !_InputParam
            description: 'Test Descr'
            input: InOutpParamDescr
        type: !_DualBase
          dual: FldOutpParamDescr
    fields:
      - !_OutputField
        name: field
        parameters:
          - !_InputParam
            description: 'Test Descr'
            input: InOutpParamDescr
        type: !_DualBase
          dual: FldOutpParamDescr
    name: OutpParamDescr
    typeKind: !_TypeKind Output