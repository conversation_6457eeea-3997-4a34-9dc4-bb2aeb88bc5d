﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{B834B2ED-0D27-4281-8523-B706B42DCC15}"
	ProjectSection(SolutionItems) = preProject
		src\Directory.Build.props = src\Directory.Build.props
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "GqlPlus.Verifier", "src\GqlPlus.Verifier\GqlPlus.Verifier.csproj", "{DDB8D682-23EA-48C4-A1AD-65F114533202}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{3B90AC37-1A6B-41AF-8490-E9D48E429ACD}"
	ProjectSection(SolutionItems) = preProject
		test\Directory.Build.props = test\Directory.Build.props
		test\Directory.Build.targets = test\Directory.Build.targets
		test\Html\index.html = test\Html\index.html
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{4E1645F9-6194-4B1F-B93D-5B2CDDE5E6DE}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		.gitignore = .gitignore
		.prettierrc.yml = .prettierrc.yml
		autoverify.ps1 = autoverify.ps1
		collect-samples.ps1 = collect-samples.ps1
		.github\copilot-instructions.md = .github\copilot-instructions.md
		.github\dependabot.yml = .github\dependabot.yml
		Directory.Build.props = Directory.Build.props
		.github\workflows\dotnet-test.yml = .github\workflows\dotnet-test.yml
		.config\dotnet-tools.json = .config\dotnet-tools.json
		format.ps1 = format.ps1
		make-summary.ps1 = make-summary.ps1
		README.md = README.md
		test.ps1 = test.ps1
		update.ps1 = update.ps1
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "GqlPlus.Verifier.ComponentTests", "test\GqlPlus.Verifier.ComponentTests\GqlPlus.Verifier.ComponentTests.csproj", "{D97ABCF2-E6C4-4384-AFB7-C55943EBE1E2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "GqlPlus.TestBase", "test\GqlPlus.TestBase\GqlPlus.TestBase.csproj", "{B73CDEC7-3F67-444E-8A06-16C20DF56DFB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Coverage", "Coverage", "{B152D8E1-AD02-4204-8E0D-A475F2A14183}"
	ProjectSection(SolutionItems) = preProject
		class-combine.ps1 = class-combine.ps1
		class-coverage.ps1 = class-coverage.ps1
		class.runsettings = class.runsettings
		coverage.ps1 = coverage.ps1
		coverage.runsettings = coverage.runsettings
		report.ps1 = report.ps1
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Otel", "Otel", "{90C6E29D-04F6-427C-8E57-27C57664AC04}"
	ProjectSection(SolutionItems) = preProject
		otel-tests.ps1 = otel-tests.ps1
		otel.runsettings = otel.runsettings
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "GqlPlus.Abstractions", "src\GqlPlus.Abstractions\GqlPlus.Abstractions.csproj", "{3AE01A85-4A1B-4D70-92D7-92A6C606EDDB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "GqlPlus.Parser", "src\GqlPlus.Parser\GqlPlus.Parser.csproj", "{2CD75718-076D-4554-9A1A-AB2FD1FFDA47}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "GqlPlus.Parser.ClassTests", "test\GqlPlus.Parser.ClassTests\GqlPlus.Parser.ClassTests.csproj", "{0910A54F-5BA1-4CCF-BDF2-B2AE8DB81D90}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "GqlPlus.Parser.ComponentTests", "test\GqlPlus.Parser.ComponentTests\GqlPlus.Parser.ComponentTests.csproj", "{F9D2FECF-2F45-4984-9D2E-A136F285E47B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "GqlPlus.Modeller", "src\GqlPlus.Modeller\GqlPlus.Modeller.csproj", "{33279A3A-60F3-49A6-BE09-33F3582E61DA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "GqlPlus.Modeller.ComponentTests", "test\GqlPlus.Modeller.ComponentTests\GqlPlus.Modeller.ComponentTests.csproj", "{B265FB57-1412-41AF-88F8-A86CEA28A66E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "GqlPlus.ComponentTestBase", "test\GqlPlus.ComponentTestBase\GqlPlus.ComponentTestBase.csproj", "{F10AC1D0-D945-4D8E-A49F-26D9DC08C42D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Modeller.ClassTests", "test\GqlPlus.Modeller.ClassTests\GqlPlus.Modeller.ClassTests.csproj", "{2EB2CC0E-4803-4D8A-8DFC-921FF815F719}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Verifier.ClassTests", "test\GqlPlus.Verifier.ClassTests\GqlPlus.Verifier.ClassTests.csproj", "{7D18D0EF-C933-414A-AF79-26309EFA4B94}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.ClassTestBase", "test\GqlPlus.ClassTestBase\GqlPlus.ClassTestBase.csproj", "{2CDE8A80-4B51-499E-8C10-C865C38D03E1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Abstractions.ClassTests", "test\GqlPlus.Abstractions.ClassTests\GqlPlus.Abstractions.ClassTests.csproj", "{A037825A-92B7-4B00-B039-EEC7A1AA0823}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.TestGenerators", "test\GqlPlus.TestGenerators\GqlPlus.TestGenerators.csproj", "{B061A811-3DBB-4E5A-AD96-91F0A74FE3C2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.TestGenerators.Tests", "test\GqlPlus.TestGenerators.Tests\GqlPlus.TestGenerators.Tests.csproj", "{BB113C8C-1B2C-4815-A4DF-051F9237AF0E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.PolyFill", "src\GqlPlus.PolyFill\GqlPlus.PolyFill.csproj", "{44D55F97-4EBA-48C8-A72C-7FBC12A44719}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.GeneratorsTestBase", "test\GqlPlus.GeneratorsTestBase\GqlPlus.GeneratorsTestBase.csproj", "{0336A900-AA73-4B44-99BD-B70A5B2D14D3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.VerifyTestBase", "test\GqlPlus.VerifyTestBase\GqlPlus.VerifyTestBase.csproj", "{14F619F3-163D-43D5-B3A4-14817438274E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Converter.Json", "src\GqlPlus.Converter.Json\GqlPlus.Converter.Json.csproj", "{B8D01714-3F6B-41D6-89F5-5F7056290ACF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Converter.Json.ComponentTests", "test\GqlPlus.Converter.Json.ComponentTests\GqlPlus.Converter.Json.ComponentTests.csproj", "{3289A609-8CB0-495D-9D10-326D63BAA5F0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Codec.ComponentTestBase", "test\GqlPlus.Codec.ComponentTestBase\GqlPlus.Codec.ComponentTestBase.csproj", "{7A0D7D70-6533-442A-9FD1-29131DE207F4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Converter.Yaml.ComponentTests", "test\GqlPlus.Converter.Yaml.ComponentTests\GqlPlus.Converter.Yaml.ComponentTests.csproj", "{8F67479F-A4B7-4574-B8EA-10C8E19DA2E4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Converter.Yaml", "src\GqlPlus.Converter.Yaml\GqlPlus.Converter.Yaml.csproj", "{DF7D0CA6-48AE-460D-BCFF-B274D6D004BA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Converter.Json.ClassTests", "test\GqlPlus.Converter.Json.ClassTests\GqlPlus.Converter.Json.ClassTests.csproj", "{E3D3D14A-A5FA-4292-8676-2A8386931EC4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Converter.Yaml.ClassTests", "test\GqlPlus.Converter.Yaml.ClassTests\GqlPlus.Converter.Yaml.ClassTests.csproj", "{D11248A7-A1B2-4D8F-A396-36615F36A4F5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Converter.ClassTestBase", "test\GqlPlus.Converter.ClassTestBase\GqlPlus.Converter.ClassTestBase.csproj", "{9A60308A-96CA-45DC-B338-92ABA3035987}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Codec", "src\GqlPlus.Codec\GqlPlus.Codec.csproj", "{0E70B5E4-06A2-4186-B025-952E1424EB57}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Codec.ClassTests", "test\GqlPlus.Codec.ClassTests\GqlPlus.Codec.ClassTests.csproj", "{3D07CC4F-1DF7-47AB-A02F-FE73EF1D5542}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GqlPlus.Codec.ComponentTests", "test\GqlPlus.Codec.ComponentTests\GqlPlus.Codec.ComponentTests.csproj", "{7607F6AB-9D34-42AD-8174-06A46760AABE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DDB8D682-23EA-48C4-A1AD-65F114533202}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DDB8D682-23EA-48C4-A1AD-65F114533202}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DDB8D682-23EA-48C4-A1AD-65F114533202}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DDB8D682-23EA-48C4-A1AD-65F114533202}.Release|Any CPU.Build.0 = Release|Any CPU
		{D97ABCF2-E6C4-4384-AFB7-C55943EBE1E2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D97ABCF2-E6C4-4384-AFB7-C55943EBE1E2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D97ABCF2-E6C4-4384-AFB7-C55943EBE1E2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D97ABCF2-E6C4-4384-AFB7-C55943EBE1E2}.Release|Any CPU.Build.0 = Release|Any CPU
		{B73CDEC7-3F67-444E-8A06-16C20DF56DFB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B73CDEC7-3F67-444E-8A06-16C20DF56DFB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B73CDEC7-3F67-444E-8A06-16C20DF56DFB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B73CDEC7-3F67-444E-8A06-16C20DF56DFB}.Release|Any CPU.Build.0 = Release|Any CPU
		{3AE01A85-4A1B-4D70-92D7-92A6C606EDDB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3AE01A85-4A1B-4D70-92D7-92A6C606EDDB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3AE01A85-4A1B-4D70-92D7-92A6C606EDDB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3AE01A85-4A1B-4D70-92D7-92A6C606EDDB}.Release|Any CPU.Build.0 = Release|Any CPU
		{2CD75718-076D-4554-9A1A-AB2FD1FFDA47}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2CD75718-076D-4554-9A1A-AB2FD1FFDA47}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2CD75718-076D-4554-9A1A-AB2FD1FFDA47}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2CD75718-076D-4554-9A1A-AB2FD1FFDA47}.Release|Any CPU.Build.0 = Release|Any CPU
		{0910A54F-5BA1-4CCF-BDF2-B2AE8DB81D90}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0910A54F-5BA1-4CCF-BDF2-B2AE8DB81D90}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0910A54F-5BA1-4CCF-BDF2-B2AE8DB81D90}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0910A54F-5BA1-4CCF-BDF2-B2AE8DB81D90}.Release|Any CPU.Build.0 = Release|Any CPU
		{F9D2FECF-2F45-4984-9D2E-A136F285E47B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F9D2FECF-2F45-4984-9D2E-A136F285E47B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F9D2FECF-2F45-4984-9D2E-A136F285E47B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F9D2FECF-2F45-4984-9D2E-A136F285E47B}.Release|Any CPU.Build.0 = Release|Any CPU
		{33279A3A-60F3-49A6-BE09-33F3582E61DA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33279A3A-60F3-49A6-BE09-33F3582E61DA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{33279A3A-60F3-49A6-BE09-33F3582E61DA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33279A3A-60F3-49A6-BE09-33F3582E61DA}.Release|Any CPU.Build.0 = Release|Any CPU
		{B265FB57-1412-41AF-88F8-A86CEA28A66E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B265FB57-1412-41AF-88F8-A86CEA28A66E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B265FB57-1412-41AF-88F8-A86CEA28A66E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B265FB57-1412-41AF-88F8-A86CEA28A66E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F10AC1D0-D945-4D8E-A49F-26D9DC08C42D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F10AC1D0-D945-4D8E-A49F-26D9DC08C42D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F10AC1D0-D945-4D8E-A49F-26D9DC08C42D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F10AC1D0-D945-4D8E-A49F-26D9DC08C42D}.Release|Any CPU.Build.0 = Release|Any CPU
		{2EB2CC0E-4803-4D8A-8DFC-921FF815F719}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2EB2CC0E-4803-4D8A-8DFC-921FF815F719}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2EB2CC0E-4803-4D8A-8DFC-921FF815F719}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2EB2CC0E-4803-4D8A-8DFC-921FF815F719}.Release|Any CPU.Build.0 = Release|Any CPU
		{7D18D0EF-C933-414A-AF79-26309EFA4B94}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D18D0EF-C933-414A-AF79-26309EFA4B94}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D18D0EF-C933-414A-AF79-26309EFA4B94}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D18D0EF-C933-414A-AF79-26309EFA4B94}.Release|Any CPU.Build.0 = Release|Any CPU
		{2CDE8A80-4B51-499E-8C10-C865C38D03E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2CDE8A80-4B51-499E-8C10-C865C38D03E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2CDE8A80-4B51-499E-8C10-C865C38D03E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2CDE8A80-4B51-499E-8C10-C865C38D03E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{A037825A-92B7-4B00-B039-EEC7A1AA0823}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A037825A-92B7-4B00-B039-EEC7A1AA0823}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A037825A-92B7-4B00-B039-EEC7A1AA0823}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A037825A-92B7-4B00-B039-EEC7A1AA0823}.Release|Any CPU.Build.0 = Release|Any CPU
		{B061A811-3DBB-4E5A-AD96-91F0A74FE3C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B061A811-3DBB-4E5A-AD96-91F0A74FE3C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B061A811-3DBB-4E5A-AD96-91F0A74FE3C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B061A811-3DBB-4E5A-AD96-91F0A74FE3C2}.Release|Any CPU.Build.0 = Release|Any CPU
		{BB113C8C-1B2C-4815-A4DF-051F9237AF0E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB113C8C-1B2C-4815-A4DF-051F9237AF0E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB113C8C-1B2C-4815-A4DF-051F9237AF0E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB113C8C-1B2C-4815-A4DF-051F9237AF0E}.Release|Any CPU.Build.0 = Release|Any CPU
		{44D55F97-4EBA-48C8-A72C-7FBC12A44719}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44D55F97-4EBA-48C8-A72C-7FBC12A44719}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44D55F97-4EBA-48C8-A72C-7FBC12A44719}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44D55F97-4EBA-48C8-A72C-7FBC12A44719}.Release|Any CPU.Build.0 = Release|Any CPU
		{0336A900-AA73-4B44-99BD-B70A5B2D14D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0336A900-AA73-4B44-99BD-B70A5B2D14D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0336A900-AA73-4B44-99BD-B70A5B2D14D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0336A900-AA73-4B44-99BD-B70A5B2D14D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{14F619F3-163D-43D5-B3A4-14817438274E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{14F619F3-163D-43D5-B3A4-14817438274E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{14F619F3-163D-43D5-B3A4-14817438274E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{14F619F3-163D-43D5-B3A4-14817438274E}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8D01714-3F6B-41D6-89F5-5F7056290ACF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8D01714-3F6B-41D6-89F5-5F7056290ACF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8D01714-3F6B-41D6-89F5-5F7056290ACF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8D01714-3F6B-41D6-89F5-5F7056290ACF}.Release|Any CPU.Build.0 = Release|Any CPU
		{3289A609-8CB0-495D-9D10-326D63BAA5F0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3289A609-8CB0-495D-9D10-326D63BAA5F0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3289A609-8CB0-495D-9D10-326D63BAA5F0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3289A609-8CB0-495D-9D10-326D63BAA5F0}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A0D7D70-6533-442A-9FD1-29131DE207F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A0D7D70-6533-442A-9FD1-29131DE207F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A0D7D70-6533-442A-9FD1-29131DE207F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A0D7D70-6533-442A-9FD1-29131DE207F4}.Release|Any CPU.Build.0 = Release|Any CPU
		{8F67479F-A4B7-4574-B8EA-10C8E19DA2E4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F67479F-A4B7-4574-B8EA-10C8E19DA2E4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F67479F-A4B7-4574-B8EA-10C8E19DA2E4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F67479F-A4B7-4574-B8EA-10C8E19DA2E4}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF7D0CA6-48AE-460D-BCFF-B274D6D004BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF7D0CA6-48AE-460D-BCFF-B274D6D004BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF7D0CA6-48AE-460D-BCFF-B274D6D004BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF7D0CA6-48AE-460D-BCFF-B274D6D004BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{E3D3D14A-A5FA-4292-8676-2A8386931EC4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E3D3D14A-A5FA-4292-8676-2A8386931EC4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E3D3D14A-A5FA-4292-8676-2A8386931EC4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E3D3D14A-A5FA-4292-8676-2A8386931EC4}.Release|Any CPU.Build.0 = Release|Any CPU
		{D11248A7-A1B2-4D8F-A396-36615F36A4F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D11248A7-A1B2-4D8F-A396-36615F36A4F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D11248A7-A1B2-4D8F-A396-36615F36A4F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D11248A7-A1B2-4D8F-A396-36615F36A4F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A60308A-96CA-45DC-B338-92ABA3035987}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A60308A-96CA-45DC-B338-92ABA3035987}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A60308A-96CA-45DC-B338-92ABA3035987}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A60308A-96CA-45DC-B338-92ABA3035987}.Release|Any CPU.Build.0 = Release|Any CPU
		{0E70B5E4-06A2-4186-B025-952E1424EB57}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0E70B5E4-06A2-4186-B025-952E1424EB57}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0E70B5E4-06A2-4186-B025-952E1424EB57}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0E70B5E4-06A2-4186-B025-952E1424EB57}.Release|Any CPU.Build.0 = Release|Any CPU
		{3D07CC4F-1DF7-47AB-A02F-FE73EF1D5542}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D07CC4F-1DF7-47AB-A02F-FE73EF1D5542}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D07CC4F-1DF7-47AB-A02F-FE73EF1D5542}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D07CC4F-1DF7-47AB-A02F-FE73EF1D5542}.Release|Any CPU.Build.0 = Release|Any CPU
		{7607F6AB-9D34-42AD-8174-06A46760AABE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7607F6AB-9D34-42AD-8174-06A46760AABE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7607F6AB-9D34-42AD-8174-06A46760AABE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7607F6AB-9D34-42AD-8174-06A46760AABE}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{DDB8D682-23EA-48C4-A1AD-65F114533202} = {B834B2ED-0D27-4281-8523-B706B42DCC15}
		{D97ABCF2-E6C4-4384-AFB7-C55943EBE1E2} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{B73CDEC7-3F67-444E-8A06-16C20DF56DFB} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{B152D8E1-AD02-4204-8E0D-A475F2A14183} = {4E1645F9-6194-4B1F-B93D-5B2CDDE5E6DE}
		{90C6E29D-04F6-427C-8E57-27C57664AC04} = {4E1645F9-6194-4B1F-B93D-5B2CDDE5E6DE}
		{3AE01A85-4A1B-4D70-92D7-92A6C606EDDB} = {B834B2ED-0D27-4281-8523-B706B42DCC15}
		{2CD75718-076D-4554-9A1A-AB2FD1FFDA47} = {B834B2ED-0D27-4281-8523-B706B42DCC15}
		{0910A54F-5BA1-4CCF-BDF2-B2AE8DB81D90} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{F9D2FECF-2F45-4984-9D2E-A136F285E47B} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{33279A3A-60F3-49A6-BE09-33F3582E61DA} = {B834B2ED-0D27-4281-8523-B706B42DCC15}
		{B265FB57-1412-41AF-88F8-A86CEA28A66E} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{F10AC1D0-D945-4D8E-A49F-26D9DC08C42D} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{2EB2CC0E-4803-4D8A-8DFC-921FF815F719} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{7D18D0EF-C933-414A-AF79-26309EFA4B94} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{2CDE8A80-4B51-499E-8C10-C865C38D03E1} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{A037825A-92B7-4B00-B039-EEC7A1AA0823} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{B061A811-3DBB-4E5A-AD96-91F0A74FE3C2} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{BB113C8C-1B2C-4815-A4DF-051F9237AF0E} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{44D55F97-4EBA-48C8-A72C-7FBC12A44719} = {B834B2ED-0D27-4281-8523-B706B42DCC15}
		{0336A900-AA73-4B44-99BD-B70A5B2D14D3} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{14F619F3-163D-43D5-B3A4-14817438274E} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{B8D01714-3F6B-41D6-89F5-5F7056290ACF} = {B834B2ED-0D27-4281-8523-B706B42DCC15}
		{3289A609-8CB0-495D-9D10-326D63BAA5F0} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{7A0D7D70-6533-442A-9FD1-29131DE207F4} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{8F67479F-A4B7-4574-B8EA-10C8E19DA2E4} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{DF7D0CA6-48AE-460D-BCFF-B274D6D004BA} = {B834B2ED-0D27-4281-8523-B706B42DCC15}
		{E3D3D14A-A5FA-4292-8676-2A8386931EC4} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{D11248A7-A1B2-4D8F-A396-36615F36A4F5} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{9A60308A-96CA-45DC-B338-92ABA3035987} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{0E70B5E4-06A2-4186-B025-952E1424EB57} = {B834B2ED-0D27-4281-8523-B706B42DCC15}
		{3D07CC4F-1DF7-47AB-A02F-FE73EF1D5542} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
		{7607F6AB-9D34-42AD-8174-06A46760AABE} = {3B90AC37-1A6B-41AF-8490-E9D48E429ACD}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {148B651C-DFAA-434F-B1ED-51E535FC2DE8}
	EndGlobalSection
EndGlobal
