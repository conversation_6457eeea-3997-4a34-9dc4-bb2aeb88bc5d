﻿!_Schema
types: !_Map_Type
  !_Identifier DomGnrcPrntStrDomOutp: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: DomGnrcPrntStrDomOutp
        exclude: false
        pattern: \w+
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: \w+
    name: DomGnrcPrntStrDomOutp
    typeKind: !_TypeKind Domain
  !_Identifier FieldGnrcPrntStrDomOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: FieldGnrcPrntStrDomOutp
        type: !_OutputBase
          typeParam: ref
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: ref
    name: FieldGnrcPrntStrDomOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref
  !_Identifier GnrcPrntStrDomOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: FieldGnrcPrntStrDomOutp
        type: !_OutputBase
          output: DomGnrcPrntStrDomOutp
    name: GnrcPrntStrDomOutp
    parent: !_OutputBase
      output: FieldGnrcPrntStrDomOutp
      typeArgs:
        - !_OutputArg
          output: DomGnrcPrntStrDomOutp
    typeKind: !_TypeKind Output