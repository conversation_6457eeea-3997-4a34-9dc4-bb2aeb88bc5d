﻿!_Schema
types: !_Map_Type
  !_Identifier AltGnrcPrntDualOutp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltGnrcPrntDualOutp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltGnrcPrntDualOutp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltGnrcPrntDualOutp
    typeKind: !_TypeKind Dual
  !_Identifier GnrcPrntDualOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcPrntDualOutp
        type: !_DualBase
          dual: AltGnrcPrntDualOutp
    name: GnrcPrntDualOutp
    parent: !_OutputBase
      output: RefGnrcPrntDualOutp
      typeArgs:
        - !_DualArg
          dual: AltGnrcPrntDualOutp
    typeKind: !_TypeKind Output
  !_Identifier RefGnrcPrntDualOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcPrntDualOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcPrntDualOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Internal
          typeName: _Dual
        name: ref