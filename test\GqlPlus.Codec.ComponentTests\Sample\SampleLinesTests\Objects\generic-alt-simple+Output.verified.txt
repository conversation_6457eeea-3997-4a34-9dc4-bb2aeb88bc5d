﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcAltSmplOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: GnrcAltSmplOutp
        type: !_OutputBase
          output: RefGnrcAltSmplOutp
          typeArgs:
            - !_OutputArg
              output: String
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: RefGnrcAltSmplOutp
          typeArgs:
            - !_OutputArg
              output: String
    name: GnrcAltSmplOutp
    typeKind: !_TypeKind Output
  !_Identifier RefGnrcAltSmplOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: RefGnrcAltSmplOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcAltSmplOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref