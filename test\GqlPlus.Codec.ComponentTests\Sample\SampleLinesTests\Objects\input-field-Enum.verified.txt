﻿!_Schema
types: !_Map_Type
  !_Identifier EnumInpFieldEnum: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumInpFieldEnum
        name: inpFieldEnum
    items:
      - !_Aliased
        name: inpFieldEnum
    name: EnumInpFieldEnum
    typeKind: !_TypeKind Enum
  !_Identifier InpFieldEnum: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        default: inpFieldEnum
        name: field
        object: InpFieldEnum
        type: !_InputBase
          input: EnumInpFieldEnum
    fields:
      - !_InputField
        default: inpFieldEnum
        name: field
        type: !_InputBase
          input: EnumInpFieldEnum
    name: InpFieldEnum
    typeKind: !_TypeKind Input