﻿<section>
  {%- if object.typeParams or object.parent %}
  <div class="grid">
    {%- if object.typeParams %}
    {%- render "typeParams" with object.typeParams as typeParams -%}
    {%- endif -%}
    {%- if object.parent %}
    <div>
      <sub>Parent:</sub> {% render "base" with object.parent as base %}
    </div>
    {% endif -%}
  </div>
  {% endif -%}
  {%- if object.allAlternates %}
  {%- render "alternates" with object.allAlternates as alternates -%}
  {%- endif -%}
  {%- if object.allFields %}
  {%- render "fields" with object.allFields as fields -%}
  {%- endif -%}
</section>
