﻿!_Schema
errors:
  - !_Error
    _kind: !_TokenKind End
    _message: In _Category can't get model for type '_Aliased'
types: !_Map_Type
  !_Identifier _Categories: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: _Categories
        type: !_OutputBase
          output: _Category
      - !_ObjectFor(_OutputAlternate)
        object: _Categories
        type: !_OutputBase
          output: _Type
    allFields:
      - !_ObjectFor(_OutputField)
        name: category
        object: _Categories
        type: !_OutputBase
          output: _Category
      - !_ObjectFor(_OutputField)
        name: type
        object: _Categories
        type: !_OutputBase
          output: _Type
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          output: _Category
      - !_OutputAlternate
        type: !_OutputBase
          output: _Type
    fields:
      - !_OutputField
        name: category
        type: !_OutputBase
          output: _Category
      - !_OutputField
        name: type
        type: !_OutputBase
          output: _Type
    name: _Categories
    typeKind: !_TypeKind Output
  !_Identifier _Category: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: resolution
        object: _Category
        type: !_OutputBase
          output: _Resolution
      - !_ObjectFor(_OutputField)
        name: output
        object: _Category
        type: !_OutputBase
          output: _TypeRef
          typeArgs:
            - !_OutputArg
              label: Output
              typeKind: !_SimpleKind Enum
              typeName: _TypeKind
      - !_ObjectFor(_OutputField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: modifiers
        object: _Category
        type: !_OutputBase
          output: _Modifiers
    fields:
      - !_OutputField
        name: resolution
        type: !_OutputBase
          output: _Resolution
      - !_OutputField
        name: output
        type: !_OutputBase
          output: _TypeRef
          typeArgs:
            - !_OutputArg
              label: Output
              typeKind: !_SimpleKind Enum
              typeName: _TypeKind
      - !_OutputField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: modifiers
        type: !_OutputBase
          output: _Modifiers
    name: _Category
    parent: !_OutputBase
      output: _Aliased
    typeKind: !_TypeKind Output
  !_Identifier _Resolution: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: _Resolution
        name: Parallel
      - !_EnumLabel
        enum: _Resolution
        name: Sequential
      - !_EnumLabel
        enum: _Resolution
        name: Single
    items:
      - !_Aliased
        name: Parallel
      - !_Aliased
        name: Sequential
      - !_Aliased
        name: Single
    name: _Resolution
    typeKind: !_TypeKind Enum