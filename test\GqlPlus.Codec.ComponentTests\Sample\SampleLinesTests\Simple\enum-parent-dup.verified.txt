﻿!_Schema
types: !_Map_Type
  !_Identifier EnumPrntDup: !_TypeEnum
    allItems:
      - !_EnumLabel
        aliases: [enumPrntDup]
        enum: PrntEnumPrntDup
        name: prnt_enumPrntDup
      - !_EnumLabel
        enum: EnumPrntDup
        name: enumPrntDup
    items:
      - !_Aliased
        name: enumPrntDup
    name: EnumPrntDup
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumPrntDup
    typeKind: !_TypeKind Enum
  !_Identifier PrntEnumPrntDup: !_TypeEnum
    allItems:
      - !_EnumLabel
        aliases: [enumPrntDup]
        enum: PrntEnumPrntDup
        name: prnt_enumPrntDup
    items:
      - !_Aliased
        aliases: [enumPrntDup]
        name: prnt_enumPrntDup
    name: PrntEnumPrntDup
    typeKind: !_TypeKind Enum