﻿!_Schema
categories: !_Map_Categories
  !_Identifier all: !_Category
    description: 'A Category'
    name: all
    output: !_TypeRef(_TypeKind)
      description: 'Category Result'
      typeKind: !_TypeKind Output
      typeName: All
    resolution: !_Resolution Parallel
directives: !_Map_Directives
  !_Identifier all: !_Directive
    description: 'A Directive'
    locations: !_Set(_Location){Field:_,Fragment:_,Inline:_,Operation:_,Spread:_,Variable:_}
    name: all
    repeatable: false
name: Schema
settings: !_Map_Setting
  !_Identifier all: !_Setting
    description: 'Schema Setting'
    name: all
    value: 'test'
types: !_Map_Type
  !_Identifier All: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        object: All
        type: !_OutputBase
          description: 'Alternates'
          output: String
    allFields:
      - !_ObjectFor(_OutputField)
        description: 'Some items'
        name: items
        object: All
        parameters:
          - !_InputParam
            input: Param
            modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        type: !_DualBase
          dual: Field
    alternates:
      - !_OutputAlternate
        type: !_OutputBase
          description: 'Alternates'
          output: String
    description: 'An Output'
    fields:
      - !_OutputField
        description: 'Some items'
        name: items
        parameters:
          - !_InputParam
            input: Param
            modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        type: !_DualBase
          dual: Field
    name: All
    typeKind: !_TypeKind Output
  !_Identifier Field: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        description: 'Some strings'
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: strings
        object: Field
        type: !_DualBase
          description: 'Strings array'
          dual: String
    description: 'A Dual'
    fields:
      - !_DualField
        description: 'Some strings'
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: strings
        type: !_DualBase
          description: 'Strings array'
          dual: String
    name: Field
    typeKind: !_TypeKind Dual
  !_Identifier Guid: !_DomainString
    allItems:
      - !_DomainItem(_DomainRegex)
        domain: Guid
        exclude: false
        pattern: '[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}'
    description: 'A Domain'
    domainKind: !_DomainKind String
    items:
      - !_DomainRegex
        exclude: false
        pattern: '[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}'
    name: Guid
    typeKind: !_TypeKind Domain
  !_Identifier Many: !_TypeUnion
    allItems:
      - !_UnionMember
        description: 'Guid Id'
        name: Guid
        union: Many
      - !_UnionMember
        description: 'Numeric Id'
        name: Number
        union: Many
    description: 'A Union'
    items:
      - !_Aliased
        description: 'Guid Id'
        name: Guid
      - !_Aliased
        description: 'Numeric Id'
        name: Number
    name: Many
    typeKind: !_TypeKind Union
  !_Identifier One: !_TypeEnum
    allItems:
      - !_EnumLabel
        description: 'Label 2'
        enum: One
        name: Two
      - !_EnumLabel
        description: 'Label 3'
        enum: One
        name: Three
    description: 'An Enum'
    items:
      - !_Aliased
        description: 'Label 2'
        name: Two
      - !_Aliased
        description: 'Label 3'
        name: Three
    name: One
    typeKind: !_TypeKind Enum
  !_Identifier Param: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: Param
        type: !_InputBase
          description: 'Alternate parameter'
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        description: 'First Id'
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: afterId
        object: Param
        type: !_InputBase
          description: 'Guid or Int'
          input: Many
      - !_ObjectFor(_InputField)
        description: 'Last Id'
        name: beforeId
        object: Param
        type: !_InputBase
          description: 'Guid or Int'
          input: Many
    alternates:
      - !_InputAlternate
        type: !_InputBase
          description: 'Alternate parameter'
          input: String
    description: 'An Input'
    fields:
      - !_InputField
        description: 'First Id'
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: afterId
        type: !_InputBase
          description: 'Guid or Int'
          input: Many
      - !_InputField
        description: 'Last Id'
        name: beforeId
        type: !_InputBase
          description: 'Guid or Int'
          input: Many
    name: Param
    typeKind: !_TypeKind Input