﻿!_Schema
categories: !_Map_Categories
  !_Identifier mutation: !_Category
    name: mutation
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: Mutation
    resolution: !_Resolution Sequential
  !_Identifier query: !_Category
    name: query
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: Query
    resolution: !_Resolution Parallel
  !_Identifier subscription: !_Category
    name: subscription
    output: !_TypeRef(_TypeKind)
      typeKind: !_TypeKind Output
      typeName: Subscription
    resolution: !_Resolution Single
types: !_Map_Type
  !_Identifier Mutation: !_TypeOutput
    name: Mutation
    typeKind: !_TypeKind Output
  !_Identifier Query: !_TypeOutput
    name: Query
    typeKind: !_TypeKind Output
  !_Identifier Subscription: !_TypeOutput
    name: Subscription
    typeKind: !_TypeKind Output
  !_Identifier _Schema: !_TypeOutput
    name: _Schema
    typeKind: !_TypeKind Output