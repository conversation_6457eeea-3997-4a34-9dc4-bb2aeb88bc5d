﻿!_Schema
types: !_Map_Type
  !_Identifier RefGnrcAltModStrOutp: !_TypeOutput
    allAlternates:
      - !_ObjectFor(_OutputAlternate)
        collections:
          - !_ModifierDictionary
            by: '*'
            modifierKind: !_ModifierKind Dict
            typeKind: !_SimpleKind Basic
        object: RefGnrcAltModStrOutp
        type: !_OutputBase
          typeParam: ref
    alternates:
      - !_OutputAlternate
        collections:
          - !_ModifierDictionary
            by: '*'
            modifierKind: !_ModifierKind Dict
            typeKind: !_SimpleKind Basic
        type: !_OutputBase
          typeParam: ref
    name: RefGnrcAltModStrOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref