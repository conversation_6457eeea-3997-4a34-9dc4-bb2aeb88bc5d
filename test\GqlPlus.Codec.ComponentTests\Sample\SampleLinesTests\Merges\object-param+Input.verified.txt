﻿!_Schema
types: !_Map_Type
  !_Identifier ObjParamInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: test
        object: ObjParamInp
        type: !_InputBase
          typeParam: test
      - !_ObjectFor(_InputField)
        name: type
        object: ObjParamInp
        type: !_InputBase
          typeParam: type
    fields:
      - !_InputField
        name: test
        type: !_InputBase
          typeParam: test
      - !_InputField
        name: type
        type: !_InputBase
          typeParam: type
    name: ObjParamInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: test
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type