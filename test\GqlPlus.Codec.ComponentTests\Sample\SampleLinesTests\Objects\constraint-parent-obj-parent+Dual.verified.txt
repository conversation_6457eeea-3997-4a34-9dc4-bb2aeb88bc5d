﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstPrntObjPrntDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstPrntObjPrntDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltCnstPrntObjPrntDual
        type: !_DualBase
          dual: Number
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltCnstPrntObjPrntDual
    parent: !_DualBase
      dual: PrntCnstPrntObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier CnstPrntObjPrntDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstPrntObjPrntDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltCnstPrntObjPrntDual
        type: !_DualBase
          dual: Number
    name: CnstPrntObjPrntDual
    parent: !_DualBase
      dual: RefCnstPrntObjPrntDual
      typeArgs:
        - !_DualArg
          dual: AltCnstPrntObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier PrntCnstPrntObjPrntDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: PrntCnstPrntObjPrntDual
        type: !_DualBase
          dual: String
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    name: PrntCnstPrntObjPrntDual
    typeKind: !_TypeKind Dual
  !_Identifier RefCnstPrntObjPrntDual: !_TypeDual
    name: RefCnstPrntObjPrntDual
    parent: !_DualBase
      typeParam: ref
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Dual
          typeName: PrntCnstPrntObjPrntDual
        name: ref