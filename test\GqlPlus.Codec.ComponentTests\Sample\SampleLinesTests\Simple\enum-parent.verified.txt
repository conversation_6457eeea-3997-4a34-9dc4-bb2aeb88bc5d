﻿!_Schema
types: !_Map_Type
  !_Identifier EnumPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntEnumPrnt
        name: prnt_enumPrnt
      - !_EnumLabel
        enum: EnumPrnt
        name: enumPrnt
    items:
      - !_Aliased
        name: enumPrnt
    name: EnumPrnt
    parent: !_TypeRef(_SimpleKind)
      typeKind: !_SimpleKind Enum
      typeName: PrntEnumPrnt
    typeKind: !_TypeKind Enum
  !_Identifier PrntEnumPrnt: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: PrntEnumPrnt
        name: prnt_enumPrnt
    items:
      - !_Aliased
        name: prnt_enumPrnt
    name: PrntEnumPrnt
    typeKind: !_TypeKind Enum