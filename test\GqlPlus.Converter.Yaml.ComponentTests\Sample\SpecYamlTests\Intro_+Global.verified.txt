﻿!_Schema
errors:
- !_Error
  _kind: !_TokenKind End
  _message: In _Category can't get model for type '_Aliased'
- !_Error
  _kind: !_TokenKind End
  _message: In _Directive can't get model for type '_Aliased'
- !_Error
  _kind: !_TokenKind End
  _message: In _Setting can't get model for type '_Named'
types: !_Map_Type
  !_Identifier _Categories: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _Categories
      type: !_OutputBase
        output: _Category
    - !_ObjectFor(_OutputAlternate)
      object: _Categories
      type: !_OutputBase
        output: _Type
    allFields:
    - !_ObjectFor(_OutputField)
      name: category
      object: _Categories
      type: !_OutputBase
        output: _Category
    - !_ObjectFor(_OutputField)
      name: type
      object: _Categories
      type: !_OutputBase
        output: _Type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _Category
    - !_OutputAlternate
      type: !_OutputBase
        output: _Type
    fields:
    - !_OutputField
      name: category
      type: !_OutputBase
        output: _Category
    - !_OutputField
      name: type
      type: !_OutputBase
        output: _Type
    name: _Categories
    typeKind: !_TypeKind Output
  !_Identifier _Category: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: resolution
      object: _Category
      type: !_OutputBase
        output: _Resolution
    - !_ObjectFor(_OutputField)
      name: output
      object: _Category
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Output
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      object: _Category
      type: !_OutputBase
        output: _Modifiers
    fields:
    - !_OutputField
      name: resolution
      type: !_OutputBase
        output: _Resolution
    - !_OutputField
      name: output
      type: !_OutputBase
        output: _TypeRef
        typeArgs:
        - !_OutputArg
          label: Output
          typeKind: !_SimpleKind Enum
          typeName: _TypeKind
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: modifiers
      type: !_OutputBase
        output: _Modifiers
    name: _Category
    parent: !_OutputBase
      output: _Aliased
    typeKind: !_TypeKind Output
  !_Identifier _Directive: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: parameters
      object: _Directive
      type: !_OutputBase
        output: _InputParam
    - !_ObjectFor(_OutputField)
      name: repeatable
      object: _Directive
      type: !_OutputBase
        output: Boolean
    - !_ObjectFor(_OutputField)
      modifiers: [!_ModifierDictionary {by: _Location, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Enum}]
      name: locations
      object: _Directive
      type: !_OutputBase
        output: _
    fields:
    - !_OutputField
      modifiers: [!_Modifier {modifierKind: !_ModifierKind List}]
      name: parameters
      type: !_OutputBase
        output: _InputParam
    - !_OutputField
      name: repeatable
      type: !_OutputBase
        output: Boolean
    - !_OutputField
      modifiers: [!_ModifierDictionary {by: _Location, modifierKind: !_ModifierKind Dict,
          typeKind: !_SimpleKind Enum}]
      name: locations
      type: !_OutputBase
        output: _
    name: _Directive
    parent: !_OutputBase
      output: _Aliased
    typeKind: !_TypeKind Output
  !_Identifier _Directives: !_TypeOutput
    allAlternates:
    - !_ObjectFor(_OutputAlternate)
      object: _Directives
      type: !_OutputBase
        output: _Directive
    - !_ObjectFor(_OutputAlternate)
      object: _Directives
      type: !_OutputBase
        output: _Type
    allFields:
    - !_ObjectFor(_OutputField)
      name: directive
      object: _Directives
      type: !_OutputBase
        output: _Directive
    - !_ObjectFor(_OutputField)
      name: type
      object: _Directives
      type: !_OutputBase
        output: _Type
    alternates:
    - !_OutputAlternate
      type: !_OutputBase
        output: _Directive
    - !_OutputAlternate
      type: !_OutputBase
        output: _Type
    fields:
    - !_OutputField
      name: directive
      type: !_OutputBase
        output: _Directive
    - !_OutputField
      name: type
      type: !_OutputBase
        output: _Type
    name: _Directives
    typeKind: !_TypeKind Output
  !_Identifier _Location: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: _Location
      name: Operation
    - !_EnumLabel
      enum: _Location
      name: Variable
    - !_EnumLabel
      enum: _Location
      name: Field
    - !_EnumLabel
      enum: _Location
      name: Inline
    - !_EnumLabel
      enum: _Location
      name: Spread
    - !_EnumLabel
      enum: _Location
      name: Fragment
    items:
    - !_Aliased
      name: Operation
    - !_Aliased
      name: Variable
    - !_Aliased
      name: Field
    - !_Aliased
      name: Inline
    - !_Aliased
      name: Spread
    - !_Aliased
      name: Fragment
    name: _Location
    typeKind: !_TypeKind Enum
  !_Identifier _Resolution: !_TypeEnum
    allItems:
    - !_EnumLabel
      enum: _Resolution
      name: Parallel
    - !_EnumLabel
      enum: _Resolution
      name: Sequential
    - !_EnumLabel
      enum: _Resolution
      name: Single
    items:
    - !_Aliased
      name: Parallel
    - !_Aliased
      name: Sequential
    - !_Aliased
      name: Single
    name: _Resolution
    typeKind: !_TypeKind Enum
  !_Identifier _Setting: !_TypeOutput
    allFields:
    - !_ObjectFor(_OutputField)
      name: value
      object: _Setting
      type: !_OutputBase
        output: _Constant
    fields:
    - !_OutputField
      name: value
      type: !_OutputBase
        output: _Constant
    name: _Setting
    parent: !_OutputBase
      output: _Named
    typeKind: !_TypeKind Output