﻿{
  "$tag": "_Schema",
  "aliases": ["Opt1", "Opt2"],
  "categories": {
    "$tag": "_Map_Categories",
    "ctgr": {
      "$tag": "_Category",
      "name": "ctgr",
      "output": {
        "$tag": "_TypeRef(_TypeKind)",
        "typeKind": {
          "$tag": "_TypeKind",
          "value": "Output"
        },
        "typeName": "Ctgr"
      },
      "resolution": {
        "$tag": "_Resolution",
        "value": "Parallel"
      }
    },
    "ctgrAlias": {
      "$tag": "_Category",
      "aliases": ["CatA1", "CatA2"],
      "name": "ctgrAlias",
      "output": {
        "$tag": "_TypeRef(_TypeKind)",
        "typeKind": {
          "$tag": "_TypeKind",
          "value": "Output"
        },
        "typeName": "CtgrAlias"
      },
      "resolution": {
        "$tag": "_Resolution",
        "value": "Parallel"
      }
    },
    "ctgrDescr": {
      "$tag": "_Category",
      "description": "First category Second category",
      "name": "ctgrDescr",
      "output": {
        "$tag": "_TypeRef(_TypeKind)",
        "typeKind": {
          "$tag": "_TypeKind",
          "value": "Output"
        },
        "typeName": "CtgrDescr"
      },
      "resolution": {
        "$tag": "_Resolution",
        "value": "Parallel"
      }
    },
    "ctgrMod": {
      "$tag": "_Category",
      "aliases": ["CatM1", "CatM2"],
      "modifiers": [
        {
          "$tag": "_Modifier",
          "modifierKind": {
            "$tag": "_ModifierKind",
            "value": "Opt"
          }
        }
      ],
      "name": "ctgrMod",
      "output": {
        "$tag": "_TypeRef(_TypeKind)",
        "typeKind": {
          "$tag": "_TypeKind",
          "value": "Output"
        },
        "typeName": "CtgrMod"
      },
      "resolution": {
        "$tag": "_Resolution",
        "value": "Parallel"
      }
    }
  },
  "directives": {
    "$tag": "_Map_Directives",
    "Drct": {
      "$tag": "_Directive",
      "locations": {
        "$tag": "_Set(_Location)",
        "Inline": "_",
        "Spread": "_"
      },
      "name": "Drct",
      "repeatable": false
    },
    "DrctAlias": {
      "$tag": "_Directive",
      "aliases": ["DirA1", "DirA2"],
      "locations": {
        "$tag": "_Set(_Location)",
        "Field": "_",
        "Variable": "_"
      },
      "name": "DrctAlias",
      "repeatable": false
    },
    "DrctParam": {
      "$tag": "_Directive",
      "locations": {
        "$tag": "_Set(_Location)",
        "Fragment": "_",
        "Operation": "_"
      },
      "name": "DrctParam",
      "parameters": [
        {
          "$tag": "_InputParam",
          "input": "InDrctParam"
        }
      ],
      "repeatable": false
    }
  },
  "name": "Schema",
  "settings": {
    "$tag": "_Map_Setting",
    "merged": {
      "$tag": "_Setting",
      "name": "merged",
      "value": [true, 0]
    }
  },
  "types": {
    "$tag": "_Map_Type",
    "Ctgr": {
      "$tag": "_TypeOutput",
      "name": "Ctgr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CtgrAlias": {
      "$tag": "_TypeOutput",
      "name": "CtgrAlias",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CtgrDescr": {
      "$tag": "_TypeOutput",
      "name": "CtgrDescr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "CtgrMod": {
      "$tag": "_TypeOutput",
      "name": "CtgrMod",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "DmnAlias": {
      "$tag": "_DomainNumber",
      "aliases": ["Num1", "Num2"],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Number"
      },
      "name": "DmnAlias",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnBool": {
      "$tag": "_DomainBoolean",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainTrueFalse)",
          "domain": "DmnBool",
          "exclude": false,
          "value": false
        },
        {
          "$tag": "_DomainItem(_DomainTrueFalse)",
          "domain": "DmnBool",
          "exclude": false,
          "value": true
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Boolean"
      },
      "items": [
        {
          "$tag": "_DomainTrueFalse",
          "exclude": false,
          "value": false
        },
        {
          "$tag": "_DomainTrueFalse",
          "exclude": false,
          "value": true
        }
      ],
      "name": "DmnBool",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnBoolDiff": {
      "$tag": "_DomainBoolean",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainTrueFalse)",
          "domain": "DmnBoolDiff",
          "exclude": false,
          "value": true
        },
        {
          "$tag": "_DomainItem(_DomainTrueFalse)",
          "domain": "DmnBoolDiff",
          "exclude": false,
          "value": false
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Boolean"
      },
      "items": [
        {
          "$tag": "_DomainTrueFalse",
          "exclude": false,
          "value": true
        },
        {
          "$tag": "_DomainTrueFalse",
          "exclude": false,
          "value": false
        }
      ],
      "name": "DmnBoolDiff",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnBoolSame": {
      "$tag": "_DomainBoolean",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainTrueFalse)",
          "domain": "DmnBoolSame",
          "exclude": false,
          "value": true
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Boolean"
      },
      "items": [
        {
          "$tag": "_DomainTrueFalse",
          "exclude": false,
          "value": true
        }
      ],
      "name": "DmnBoolSame",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnEnumDiff": {
      "$tag": "_DomainEnum",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainLabel)",
          "domain": "DmnEnumDiff",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "true",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "Boolean"
          }
        },
        {
          "$tag": "_DomainItem(_DomainLabel)",
          "domain": "DmnEnumDiff",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "false",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "Boolean"
          }
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Enum"
      },
      "items": [
        {
          "$tag": "_DomainLabel",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "true",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "Boolean"
          }
        },
        {
          "$tag": "_DomainLabel",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "false",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "Boolean"
          }
        }
      ],
      "name": "DmnEnumDiff",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnEnumSame": {
      "$tag": "_DomainEnum",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainLabel)",
          "domain": "DmnEnumSame",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "true",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "Boolean"
          }
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Enum"
      },
      "items": [
        {
          "$tag": "_DomainLabel",
          "exclude": false,
          "value": {
            "$tag": "_EnumValue",
            "label": "true",
            "typeKind": {
              "$tag": "_SimpleKind",
              "value": "Enum"
            },
            "typeName": "Boolean"
          }
        }
      ],
      "name": "DmnEnumSame",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnNmbr": {
      "$tag": "_DomainNumber",
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Number"
      },
      "name": "DmnNmbr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnNmbrDiff": {
      "$tag": "_DomainNumber",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRange)",
          "domain": "DmnNmbrDiff",
          "exclude": false,
          "from": 1,
          "to": 9
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Number"
      },
      "items": [
        {
          "$tag": "_DomainRange",
          "exclude": false,
          "from": 1,
          "to": 9
        }
      ],
      "name": "DmnNmbrDiff",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnNmbrSame": {
      "$tag": "_DomainNumber",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRange)",
          "domain": "DmnNmbrSame",
          "exclude": false,
          "from": 1,
          "to": 9
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "Number"
      },
      "items": [
        {
          "$tag": "_DomainRange",
          "exclude": false,
          "from": 1,
          "to": 9
        }
      ],
      "name": "DmnNmbrSame",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnStr": {
      "$tag": "_DomainString",
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "name": "DmnStr",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnStrDiff": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DmnStrDiff",
          "exclude": false,
          "pattern": "a\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "a\u002B"
        }
      ],
      "name": "DmnStrDiff",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "DmnStrSame": {
      "$tag": "_DomainString",
      "allItems": [
        {
          "$tag": "_DomainItem(_DomainRegex)",
          "domain": "DmnStrSame",
          "exclude": false,
          "pattern": "a\u002B"
        }
      ],
      "domainKind": {
        "$tag": "_DomainKind",
        "value": "String"
      },
      "items": [
        {
          "$tag": "_DomainRegex",
          "exclude": false,
          "pattern": "a\u002B"
        }
      ],
      "name": "DmnStrSame",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Domain"
      }
    },
    "EnumAlias": {
      "$tag": "_TypeEnum",
      "aliases": ["En1", "En2"],
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumAlias",
          "name": "enumAlias"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "enumAlias"
        }
      ],
      "name": "EnumAlias",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumDiff": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumDiff",
          "name": "one"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumDiff",
          "name": "two"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "one"
        },
        {
          "$tag": "_Aliased",
          "name": "two"
        }
      ],
      "name": "EnumDiff",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumSame": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "EnumSame",
          "name": "enumSame"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "enumSame"
        }
      ],
      "name": "EnumSame",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumSamePrnt": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "PrntEnumSamePrnt",
          "name": "prnt_enumSamePrnt"
        },
        {
          "$tag": "_EnumLabel",
          "enum": "EnumSamePrnt",
          "name": "enumSamePrnt"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "enumSamePrnt"
        }
      ],
      "name": "EnumSamePrnt",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Enum"
        },
        "typeName": "PrntEnumSamePrnt"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "EnumValueAlias": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "aliases": ["val1", "val2"],
          "enum": "EnumValueAlias",
          "name": "enumValueAlias"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "aliases": ["val1", "val2"],
          "name": "enumValueAlias"
        }
      ],
      "name": "EnumValueAlias",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "FldObjFieldAliasDual": {
      "$tag": "_TypeDual",
      "name": "FldObjFieldAliasDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldObjFieldAliasInp": {
      "$tag": "_TypeInput",
      "name": "FldObjFieldAliasInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FldObjFieldAliasOutp": {
      "$tag": "_TypeOutput",
      "name": "FldObjFieldAliasOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FldObjFieldDual": {
      "$tag": "_TypeDual",
      "name": "FldObjFieldDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "FldObjFieldInp": {
      "$tag": "_TypeInput",
      "name": "FldObjFieldInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "FldObjFieldOutp": {
      "$tag": "_TypeOutput",
      "name": "FldObjFieldOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "FldOutpFieldParam": {
      "$tag": "_TypeDual",
      "name": "FldOutpFieldParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "InDrctParam": {
      "$tag": "_TypeInput",
      "name": "InDrctParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "ObjAliasDual": {
      "$tag": "_TypeDual",
      "aliases": ["Dual1", "Dual2"],
      "name": "ObjAliasDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "ObjAliasInp": {
      "$tag": "_TypeInput",
      "aliases": ["Input1", "Input2"],
      "name": "ObjAliasInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "ObjAliasOutp": {
      "$tag": "_TypeOutput",
      "aliases": ["Output1", "Output2"],
      "name": "ObjAliasOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "ObjAltDual": {
      "$tag": "_TypeDual",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_DualAlternate)",
          "object": "ObjAltDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "ObjAltDualType"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_DualAlternate",
          "type": {
            "$tag": "_DualBase",
            "dual": "ObjAltDualType"
          }
        }
      ],
      "name": "ObjAltDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "ObjAltDualType": {
      "$tag": "_TypeDual",
      "name": "ObjAltDualType",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "ObjAltInp": {
      "$tag": "_TypeInput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_InputAlternate)",
          "object": "ObjAltInp",
          "type": {
            "$tag": "_InputBase",
            "input": "ObjAltInpType"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_InputAlternate",
          "type": {
            "$tag": "_InputBase",
            "input": "ObjAltInpType"
          }
        }
      ],
      "name": "ObjAltInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "ObjAltInpType": {
      "$tag": "_TypeInput",
      "name": "ObjAltInpType",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "ObjAltOutp": {
      "$tag": "_TypeOutput",
      "allAlternates": [
        {
          "$tag": "_ObjectFor(_OutputAlternate)",
          "object": "ObjAltOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "ObjAltOutpType"
          }
        }
      ],
      "alternates": [
        {
          "$tag": "_OutputAlternate",
          "type": {
            "$tag": "_OutputBase",
            "output": "ObjAltOutpType"
          }
        }
      ],
      "name": "ObjAltOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "ObjAltOutpType": {
      "$tag": "_TypeOutput",
      "name": "ObjAltOutpType",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "ObjCnstDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "ObjCnstDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "str",
          "object": "ObjCnstDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        },
        {
          "$tag": "_DualField",
          "name": "str",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "ObjCnstDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "ObjCnstInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "ObjCnstInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "str",
          "object": "ObjCnstInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        },
        {
          "$tag": "_InputField",
          "name": "str",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "ObjCnstInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "ObjCnstOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "ObjCnstOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "str",
          "object": "ObjCnstOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "str",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "ObjCnstOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "ObjDual": {
      "$tag": "_TypeDual",
      "name": "ObjDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "ObjFieldAliasDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "aliases": ["field1", "field2"],
          "name": "field",
          "object": "ObjFieldAliasDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldObjFieldAliasDual"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "aliases": ["field1", "field2"],
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldObjFieldAliasDual"
          }
        }
      ],
      "name": "ObjFieldAliasDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "ObjFieldAliasInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "aliases": ["field1", "field2"],
          "name": "field",
          "object": "ObjFieldAliasInp",
          "type": {
            "$tag": "_InputBase",
            "input": "FldObjFieldAliasInp"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "aliases": ["field1", "field2"],
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "FldObjFieldAliasInp"
          }
        }
      ],
      "name": "ObjFieldAliasInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "ObjFieldAliasOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "aliases": ["field1", "field2"],
          "name": "field",
          "object": "ObjFieldAliasOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "FldObjFieldAliasOutp"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "aliases": ["field1", "field2"],
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "FldObjFieldAliasOutp"
          }
        }
      ],
      "name": "ObjFieldAliasOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "ObjFieldDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "ObjFieldDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldObjFieldDual"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "FldObjFieldDual"
          }
        }
      ],
      "name": "ObjFieldDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "ObjFieldInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "ObjFieldInp",
          "type": {
            "$tag": "_InputBase",
            "input": "FldObjFieldInp"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "FldObjFieldInp"
          }
        }
      ],
      "name": "ObjFieldInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "ObjFieldOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "ObjFieldOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "FldObjFieldOutp"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "FldObjFieldOutp"
          }
        }
      ],
      "name": "ObjFieldOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "ObjFieldTypeAliasDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "field",
          "object": "ObjFieldTypeAliasDual",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "field",
          "type": {
            "$tag": "_DualBase",
            "dual": "String"
          }
        }
      ],
      "name": "ObjFieldTypeAliasDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "ObjFieldTypeAliasInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "field",
          "object": "ObjFieldTypeAliasInp",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "field",
          "type": {
            "$tag": "_InputBase",
            "input": "String"
          }
        }
      ],
      "name": "ObjFieldTypeAliasInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "ObjFieldTypeAliasOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "ObjFieldTypeAliasOutp",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "type": {
            "$tag": "_OutputBase",
            "output": "String"
          }
        }
      ],
      "name": "ObjFieldTypeAliasOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "ObjInp": {
      "$tag": "_TypeInput",
      "name": "ObjInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "ObjOutp": {
      "$tag": "_TypeOutput",
      "name": "ObjOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "ObjParamCnstDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "test",
          "object": "ObjParamCnstDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "type",
          "object": "ObjParamCnstDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "test"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "test",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_DualField",
          "name": "type",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "test"
          }
        }
      ],
      "name": "ObjParamCnstDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "test"
        }
      ]
    },
    "ObjParamCnstInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "test",
          "object": "ObjParamCnstInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "type",
          "object": "ObjParamCnstInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "test"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "test",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_InputField",
          "name": "type",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "test"
          }
        }
      ],
      "name": "ObjParamCnstInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "test"
        }
      ]
    },
    "ObjParamCnstOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "test",
          "object": "ObjParamCnstOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "ObjParamCnstOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "test"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "test",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "type",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "test"
          }
        }
      ],
      "name": "ObjParamCnstOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "test"
        }
      ]
    },
    "ObjParamDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "test",
          "object": "ObjParamDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "type",
          "object": "ObjParamDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "test",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_DualField",
          "name": "type",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "ObjParamDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "test"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "ObjParamDupDual": {
      "$tag": "_TypeDual",
      "allFields": [
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "test",
          "object": "ObjParamDupDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_ObjectFor(_DualField)",
          "name": "type",
          "object": "ObjParamDupDual",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "test"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_DualField",
          "name": "test",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_DualField",
          "name": "type",
          "type": {
            "$tag": "_DualBase",
            "typeParam": "test"
          }
        }
      ],
      "name": "ObjParamDupDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "test"
        }
      ]
    },
    "ObjParamDupInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "test",
          "object": "ObjParamDupInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "type",
          "object": "ObjParamDupInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "test"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "test",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_InputField",
          "name": "type",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "test"
          }
        }
      ],
      "name": "ObjParamDupInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "test"
        }
      ]
    },
    "ObjParamDupOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "test",
          "object": "ObjParamDupOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "ObjParamDupOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "test"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "test",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "type",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "test"
          }
        }
      ],
      "name": "ObjParamDupOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "test"
        }
      ]
    },
    "ObjParamInp": {
      "$tag": "_TypeInput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "test",
          "object": "ObjParamInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_ObjectFor(_InputField)",
          "name": "type",
          "object": "ObjParamInp",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_InputField",
          "name": "test",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_InputField",
          "name": "type",
          "type": {
            "$tag": "_InputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "ObjParamInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "test"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "ObjParamOutp": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "test",
          "object": "ObjParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "type",
          "object": "ObjParamOutp",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "test",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "test"
          }
        },
        {
          "$tag": "_OutputField",
          "name": "type",
          "type": {
            "$tag": "_OutputBase",
            "typeParam": "type"
          }
        }
      ],
      "name": "ObjParamOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      },
      "typeParams": [
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "test"
        },
        {
          "$tag": "_TypeParam",
          "constraint": {
            "$tag": "_TypeRef(_TypeKind)",
            "typeKind": {
              "$tag": "_TypeKind",
              "value": "Basic"
            },
            "typeName": "String"
          },
          "name": "type"
        }
      ]
    },
    "ObjPrntDual": {
      "$tag": "_TypeDual",
      "name": "ObjPrntDual",
      "parent": {
        "$tag": "_DualBase",
        "dual": "RefObjPrntDual"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "ObjPrntInp": {
      "$tag": "_TypeInput",
      "name": "ObjPrntInp",
      "parent": {
        "$tag": "_InputBase",
        "input": "RefObjPrntInp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "ObjPrntOutp": {
      "$tag": "_TypeOutput",
      "name": "ObjPrntOutp",
      "parent": {
        "$tag": "_OutputBase",
        "output": "RefObjPrntOutp"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpFieldEnumAlias": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "field": "field",
          "label": "true",
          "object": "OutpFieldEnumAlias",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "Boolean"
        }
      ],
      "fields": [
        {
          "$tag": "_OutputEnum",
          "field": "field",
          "label": "true",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "Boolean"
        }
      ],
      "name": "OutpFieldEnumAlias",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpFieldEnumValue": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "field": "field",
          "label": "true",
          "object": "OutpFieldEnumValue",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "Boolean"
        }
      ],
      "fields": [
        {
          "$tag": "_OutputEnum",
          "field": "field",
          "label": "true",
          "typeKind": {
            "$tag": "_SimpleKind",
            "value": "Enum"
          },
          "typeName": "Boolean"
        }
      ],
      "name": "OutpFieldEnumValue",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpFieldParam": {
      "$tag": "_TypeOutput",
      "allFields": [
        {
          "$tag": "_ObjectFor(_OutputField)",
          "name": "field",
          "object": "OutpFieldParam",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "OutpFieldParam1"
            },
            {
              "$tag": "_InputParam",
              "input": "OutpFieldParam2"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpFieldParam"
          }
        }
      ],
      "fields": [
        {
          "$tag": "_OutputField",
          "name": "field",
          "parameters": [
            {
              "$tag": "_InputParam",
              "input": "OutpFieldParam1"
            },
            {
              "$tag": "_InputParam",
              "input": "OutpFieldParam2"
            }
          ],
          "type": {
            "$tag": "_DualBase",
            "dual": "FldOutpFieldParam"
          }
        }
      ],
      "name": "OutpFieldParam",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "OutpFieldParam1": {
      "$tag": "_TypeInput",
      "name": "OutpFieldParam1",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "OutpFieldParam2": {
      "$tag": "_TypeInput",
      "name": "OutpFieldParam2",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "PrntEnumSamePrnt": {
      "$tag": "_TypeEnum",
      "allItems": [
        {
          "$tag": "_EnumLabel",
          "enum": "PrntEnumSamePrnt",
          "name": "prnt_enumSamePrnt"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "prnt_enumSamePrnt"
        }
      ],
      "name": "PrntEnumSamePrnt",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Enum"
      }
    },
    "PrntUnionSamePrnt": {
      "$tag": "_TypeUnion",
      "allItems": [
        {
          "$tag": "_UnionMember",
          "name": "String",
          "union": "PrntUnionSamePrnt"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "String"
        }
      ],
      "name": "PrntUnionSamePrnt",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Union"
      }
    },
    "RefObjPrntDual": {
      "$tag": "_TypeDual",
      "name": "RefObjPrntDual",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Dual"
      }
    },
    "RefObjPrntInp": {
      "$tag": "_TypeInput",
      "name": "RefObjPrntInp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Input"
      }
    },
    "RefObjPrntOutp": {
      "$tag": "_TypeOutput",
      "name": "RefObjPrntOutp",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Output"
      }
    },
    "UnionAlias": {
      "$tag": "_TypeUnion",
      "aliases": ["UnA1", "UnA2"],
      "allItems": [
        {
          "$tag": "_UnionMember",
          "name": "Boolean",
          "union": "UnionAlias"
        },
        {
          "$tag": "_UnionMember",
          "name": "Number",
          "union": "UnionAlias"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Boolean"
        },
        {
          "$tag": "_Aliased",
          "name": "Number"
        }
      ],
      "name": "UnionAlias",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Union"
      }
    },
    "UnionDiff": {
      "$tag": "_TypeUnion",
      "allItems": [
        {
          "$tag": "_UnionMember",
          "name": "Boolean",
          "union": "UnionDiff"
        },
        {
          "$tag": "_UnionMember",
          "name": "Number",
          "union": "UnionDiff"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Boolean"
        },
        {
          "$tag": "_Aliased",
          "name": "Number"
        }
      ],
      "name": "UnionDiff",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Union"
      }
    },
    "UnionSame": {
      "$tag": "_TypeUnion",
      "allItems": [
        {
          "$tag": "_UnionMember",
          "name": "Boolean",
          "union": "UnionSame"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Boolean"
        }
      ],
      "name": "UnionSame",
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Union"
      }
    },
    "UnionSamePrnt": {
      "$tag": "_TypeUnion",
      "allItems": [
        {
          "$tag": "_UnionMember",
          "name": "String",
          "union": "PrntUnionSamePrnt"
        },
        {
          "$tag": "_UnionMember",
          "name": "Boolean",
          "union": "UnionSamePrnt"
        }
      ],
      "items": [
        {
          "$tag": "_Aliased",
          "name": "Boolean"
        }
      ],
      "name": "UnionSamePrnt",
      "parent": {
        "$tag": "_TypeRef(_SimpleKind)",
        "typeKind": {
          "$tag": "_SimpleKind",
          "value": "Union"
        },
        "typeName": "PrntUnionSamePrnt"
      },
      "typeKind": {
        "$tag": "_TypeKind",
        "value": "Union"
      }
    }
  }
}