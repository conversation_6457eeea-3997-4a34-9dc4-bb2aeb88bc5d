﻿!_Schema
types: !_Map_Type
  !_Identifier DomGnrcPrntEnumDomOutp: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DomGnrcPrntEnumDomOutp
        exclude: false
        value: !_EnumValue
          label: gnrcPrntEnumDomOutpLabel
          typeKind: !_SimpleKind Enum
          typeName: EnumGnrcPrntEnumDomOutp
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: gnrcPrntEnumDomOutpLabel
          typeKind: !_SimpleKind Enum
          typeName: EnumGnrcPrntEnumDomOutp
    name: DomGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Domain
  !_Identifier EnumGnrcPrntEnumDomOutp: !_TypeEnum
    allItems:
      - !_EnumLabel
        enum: EnumGnrcPrntEnumDomOutp
        name: gnrcPrntEnumDomOutpLabel
      - !_EnumLabel
        enum: EnumGnrcPrntEnumDomOutp
        name: gnrcPrntEnumDomOutpOther
    items:
      - !_Aliased
        name: gnrcPrntEnumDomOutpLabel
      - !_Aliased
        name: gnrcPrntEnumDomOutpOther
    name: EnumGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Enum
  !_Identifier FieldGnrcPrntEnumDomOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: FieldGnrcPrntEnumDomOutp
        type: !_OutputBase
          typeParam: ref
    fields:
      - !_OutputField
        name: field
        type: !_OutputBase
          typeParam: ref
    name: FieldGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Enum
          typeName: EnumGnrcPrntEnumDomOutp
        name: ref
  !_Identifier GnrcPrntEnumDomOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: FieldGnrcPrntEnumDomOutp
        type: !_OutputBase
          output: DomGnrcPrntEnumDomOutp
    name: GnrcPrntEnumDomOutp
    parent: !_OutputBase
      output: FieldGnrcPrntEnumDomOutp
      typeArgs:
        - !_OutputArg
          output: DomGnrcPrntEnumDomOutp
    typeKind: !_TypeKind Output