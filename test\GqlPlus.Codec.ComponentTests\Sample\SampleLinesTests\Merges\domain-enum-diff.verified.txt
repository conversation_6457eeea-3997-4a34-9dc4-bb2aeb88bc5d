﻿!_Schema
types: !_Map_Type
  !_Identifier DmnEnumDiff: !_DomainEnum
    allItems:
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumDiff
        exclude: false
        value: !_EnumValue
          label: true
          typeKind: !_SimpleKind Enum
          typeName: Boolean
      - !_DomainItem(_DomainLabel)
        domain: DmnEnumDiff
        exclude: false
        value: !_EnumValue
          label: false
          typeKind: !_SimpleKind Enum
          typeName: Boolean
    domainKind: !_DomainKind Enum
    items:
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: true
          typeKind: !_SimpleKind Enum
          typeName: Boolean
      - !_DomainLabel
        exclude: false
        value: !_EnumValue
          label: false
          typeKind: !_SimpleKind Enum
          typeName: Boolean
    name: DmnEnumDiff
    typeKind: !_TypeKind Domain