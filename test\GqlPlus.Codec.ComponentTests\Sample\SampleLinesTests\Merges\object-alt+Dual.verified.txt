﻿!_Schema
types: !_Map_Type
  !_Identifier ObjAltDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: ObjAltDual
        type: !_DualBase
          dual: ObjAltDualType
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: ObjAltDualType
    name: ObjAltDual
    typeKind: !_TypeKind Dual
  !_Identifier ObjAltDualType: !_TypeDual
    name: ObjAltDualType
    typeKind: !_TypeKind Dual