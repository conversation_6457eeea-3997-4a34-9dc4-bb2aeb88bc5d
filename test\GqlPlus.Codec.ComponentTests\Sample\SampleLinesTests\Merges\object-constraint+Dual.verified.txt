﻿!_Schema
types: !_Map_Type
  !_Identifier ObjCnstDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: ObjCnstDual
        type: !_DualBase
          typeParam: type
      - !_ObjectFor(_DualField)
        name: str
        object: ObjCnstDual
        type: !_DualBase
          typeParam: type
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          typeParam: type
      - !_DualField
        name: str
        type: !_DualBase
          typeParam: type
    name: ObjCnstDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type