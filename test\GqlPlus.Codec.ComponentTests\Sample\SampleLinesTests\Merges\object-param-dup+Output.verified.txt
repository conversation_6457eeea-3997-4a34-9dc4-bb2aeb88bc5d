﻿!_Schema
types: !_Map_Type
  !_Identifier ObjParamDupOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: test
        object: ObjParamDupOutp
        type: !_OutputBase
          typeParam: test
      - !_ObjectFor(_OutputField)
        name: type
        object: ObjParamDupOutp
        type: !_OutputBase
          typeParam: test
    fields:
      - !_OutputField
        name: test
        type: !_OutputBase
          typeParam: test
      - !_OutputField
        name: type
        type: !_OutputBase
          typeParam: test
    name: ObjParamDupOutp
    typeKind: !_TypeKind Output
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: test