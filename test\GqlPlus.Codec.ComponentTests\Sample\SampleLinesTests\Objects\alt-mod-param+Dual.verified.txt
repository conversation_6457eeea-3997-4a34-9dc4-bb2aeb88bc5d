﻿!_Schema
types: !_Map_Type
  !_Identifier AltAltModParamDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: AltAltModParamDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: alt
        object: AltAltModParamDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: alt
        type: !_DualBase
          dual: Number
    name: AltAltModParamDual
    typeKind: !_TypeKind Dual
  !_Identifier AltModParamDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        object: AltModParamDual
        type: !_DualBase
          dual: AltAltModParamDual
    alternates:
      - !_DualAlternate
        collections:
          - !_ModifierTypeParam
            by: mod
            modifierKind: !_ModifierKind Param
        type: !_DualBase
          dual: AltAltModParamDual
    name: AltModParamDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: mod