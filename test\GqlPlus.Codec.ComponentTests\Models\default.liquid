﻿<!doctype html>
<html lang="en">
  <head>
    {%- render "pico" -%}
    <title>Schema {{ name }} Documentation</title>
    <style>
      table { width: 80%; }
      sub { font-style: italic; }
      .from { width: 15%; text-align: right;}
      article article:last-child { margin-bottom: 0;}
    </style>
  </head>
  <body class="container">
    <aside>
      <details>
        <summary>YAML</summary>
        <pre class="overflow-auto">{{ yaml }}</pre>
      </details>
    </aside>
    {%- if name or aliases -%}
    {%- render 'schema' -%}
    {%- endif -%}
    <main>
      {%- if _errors %}
      <article>
        <header><h3>Errors</h3></header>
        <ul>
          {% render 'error' for _errors as error %}
        </ul>
      </article>
      {%- endif -%}
      {%- if types %}
      <article>
        <header><h1>Types</h1></header>
        {%- render 'type' for types as type %}
      </article>
      {%- endif -%}
      {%- if categories %}
      <article>
        <header><h1>Categories</h1></header>
        {%- render 'category' for categories as category %}
      </article>
      {%- endif -%}
      {%- if directives %}
      <article>
        <header><h1>Directives</h1></header>
        {%- render 'directive' for directives as directive %}
      </article>
      {%- endif -%}
      {%- if settings %}
      <article>
        <header><h1>Settings</h1></header>        
        <table>
          <tr>
            <th>Name</th>
            <th>Value</th>
          </tr>
          {%- render 'setting' for settings as setting %}
        </table>
      </article>
      {%- endif %}
    </main>
  </body>
</html>