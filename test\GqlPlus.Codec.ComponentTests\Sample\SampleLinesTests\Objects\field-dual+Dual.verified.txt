﻿!_Schema
types: !_Map_Type
  !_Identifier FieldDualDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FieldDualDual
        type: !_DualBase
          dual: FldFieldDualDual
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          dual: FldFieldDualDual
    name: FieldDualDual
    typeKind: !_TypeKind Dual
  !_Identifier FldFieldDualDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: FldFieldDualDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FldFieldDualDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          dual: Number
    name: FldFieldDualDual
    typeKind: !_TypeKind Dual