﻿!_Schema
types: !_Map_Type
  !_Identifier FldInpFieldNull: !_TypeDual
    name: FldInpFieldNull
    typeKind: !_TypeKind Dual
  !_Identifier InpFieldNull: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        default: !Null null
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: field
        object: InpFieldNull
        type: !_DualBase
          dual: FldInpFieldNull
    fields:
      - !_InputField
        default: !Null null
        modifiers: [!_Modifier{modifierKind:!_ModifierKind Opt}]
        name: field
        type: !_DualBase
          dual: FldInpFieldNull
    name: InpFieldNull
    typeKind: !_TypeKind Input