﻿!_Schema
types: !_Map_Type
  !_Identifier GnrcAltArgInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: GnrcAltArgInp
        type: !_InputBase
          input: RefGnrcAltArgInp
          typeArgs:
            - !_InputArg
              typeParam: type
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: RefGnrcAltArgInp
          typeArgs:
            - !_InputArg
              typeParam: type
    name: GnrcAltArgInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: type
  !_Identifier RefGnrcAltArgInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefGnrcAltArgInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefGnrcAltArgInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: ref