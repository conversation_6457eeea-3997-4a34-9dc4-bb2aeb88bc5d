﻿!_Schema
types: !_Map_Type
  !_Identifier FieldModParamDual: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        modifiers: [!_ModifierTypeParam{by:mod,modifierKind:!_ModifierKind Param}]
        name: field
        object: FieldModParamDual
        type: !_DualBase
          dual: FldFieldModParamDual
    fields:
      - !_DualField
        modifiers: [!_ModifierTypeParam{by:mod,modifierKind:!_ModifierKind Param}]
        name: field
        type: !_DualBase
          dual: FldFieldModParamDual
    name: FieldModParamDual
    typeKind: !_TypeKind Dual
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Basic
          typeName: String
        name: mod
  !_Identifier FldFieldModParamDual: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: FldFieldModParamDual
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FldFieldModParamDual
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          dual: Number
    name: FldFieldModParamDual
    typeKind: !_TypeKind Dual