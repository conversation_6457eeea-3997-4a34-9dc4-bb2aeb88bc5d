﻿!_Schema
types: !_Map_Type
  !_Identifier AltCnstAltObjInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: PrntCnstAltObjInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: alt
        object: AltCnstAltObjInp
        type: !_InputBase
          input: Number
    fields:
      - !_InputField
        name: alt
        type: !_InputBase
          input: Number
    name: AltCnstAltObjInp
    parent: !_InputBase
      input: PrntCnstAltObjInp
    typeKind: !_TypeKind Input
  !_Identifier CnstAltObjInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: CnstAltObjInp
        type: !_InputBase
          input: RefCnstAltObjInp
          typeArgs:
            - !_InputArg
              input: AltCnstAltObjInp
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: RefCnstAltObjInp
          typeArgs:
            - !_InputArg
              input: AltCnstAltObjInp
    name: CnstAltObjInp
    typeKind: !_TypeKind Input
  !_Identifier PrntCnstAltObjInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: PrntCnstAltObjInp
        type: !_InputBase
          input: String
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    name: PrntCnstAltObjInp
    typeKind: !_TypeKind Input
  !_Identifier RefCnstAltObjInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: RefCnstAltObjInp
        type: !_InputBase
          typeParam: ref
    alternates:
      - !_InputAlternate
        type: !_InputBase
          typeParam: ref
    name: RefCnstAltObjInp
    typeKind: !_TypeKind Input
    typeParams:
      - !_TypeParam
        constraint: !_TypeRef(_TypeKind)
          typeKind: !_TypeKind Input
          typeName: PrntCnstAltObjInp
        name: ref