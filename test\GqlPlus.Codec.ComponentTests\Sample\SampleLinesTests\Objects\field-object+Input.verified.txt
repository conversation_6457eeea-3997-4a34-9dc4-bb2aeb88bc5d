﻿!_Schema
types: !_Map_Type
  !_Identifier FieldObjInp: !_TypeInput
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: FieldObjInp
        type: !_InputBase
          input: FldFieldObjInp
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          input: FldFieldObjInp
    name: FieldObjInp
    typeKind: !_TypeKind Input
  !_Identifier FldFieldObjInp: !_TypeInput
    allAlternates:
      - !_ObjectFor(_InputAlternate)
        object: FldFieldObjInp
        type: !_InputBase
          input: String
    allFields:
      - !_ObjectFor(_InputField)
        name: field
        object: FldFieldObjInp
        type: !_InputBase
          input: Number
    alternates:
      - !_InputAlternate
        type: !_InputBase
          input: String
    fields:
      - !_InputField
        name: field
        type: !_InputBase
          input: Number
    name: FldFieldObjInp
    typeKind: !_TypeKind Input