﻿!_Schema
types: !_Map_Type
  !_Identifier FieldDualOutp: !_TypeOutput
    allFields:
      - !_ObjectFor(_OutputField)
        name: field
        object: FieldDualOutp
        type: !_DualBase
          dual: FldFieldDualOutp
    fields:
      - !_OutputField
        name: field
        type: !_DualBase
          dual: FldFieldDualOutp
    name: FieldDualOutp
    typeKind: !_TypeKind Output
  !_Identifier FldFieldDualOutp: !_TypeDual
    allAlternates:
      - !_ObjectFor(_DualAlternate)
        object: FldFieldDualOutp
        type: !_DualBase
          dual: String
    allFields:
      - !_ObjectFor(_DualField)
        name: field
        object: FldFieldDualOutp
        type: !_DualBase
          dual: Number
    alternates:
      - !_DualAlternate
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        name: field
        type: !_DualBase
          dual: Number
    name: FldFieldDualOutp
    typeKind: !_TypeKind Dual