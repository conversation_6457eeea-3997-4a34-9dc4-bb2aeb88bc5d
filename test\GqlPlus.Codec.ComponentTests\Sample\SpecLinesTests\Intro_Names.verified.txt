﻿!_Schema
types: !_Map_Type
  !_Identifier _Aliased: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: description
        object: _Described
        type: !_DualBase
          dual: String
      - !_ObjectFor(_DualField)
        name: name
        object: _Named
        type: !_DualBase
          dual: _Identifier
      - !_ObjectFor(_DualField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: aliases
        object: _Aliased
        type: !_DualBase
          dual: _Identifier
    fields:
      - !_DualField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: aliases
        type: !_DualBase
          dual: _Identifier
    name: _Aliased
    parent: !_DualBase
      dual: _Named
    typeKind: !_TypeKind Dual
  !_Identifier _Described: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: description
        object: _Described
        type: !_DualBase
          dual: String
    fields:
      - !_DualField
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: description
        type: !_DualBase
          dual: String
    name: _Described
    typeKind: !_TypeKind Dual
  !_Identifier _Named: !_TypeDual
    allFields:
      - !_ObjectFor(_DualField)
        modifiers: [!_Modifier{modifierKind:!_ModifierKind List}]
        name: description
        object: _Described
        type: !_DualBase
          dual: String
      - !_ObjectFor(_DualField)
        name: name
        object: _Named
        type: !_DualBase
          dual: _Identifier
    fields:
      - !_DualField
        name: name
        type: !_DualBase
          dual: _Identifier
    name: _Named
    parent: !_DualBase
      dual: _Described
    typeKind: !_TypeKind Dual